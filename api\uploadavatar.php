<?php

//decode by nige112
$user_zh = $_COOKIE["username"];
$user_passid = $_COOKIE["passid"];
if ($user_zh == "" || $user_passid == "") {
    exit("<script language=\"JavaScript\">;alert(\"请先登录!\");location.href=\"../index.php\";</script>");
}

include "../config.php";
$conn = new mysqli($servername, $username, $password, $dbname);
if ($conn->connect_error) {
    exit("连接失败: " . $conn->connect_error);
}

// Fetch user info
$user_zhsg = addslashes(htmlspecialchars($user_zh));
$sqls = "SELECT * FROM user WHERE username='{$user_zhsg}'";
$data_results = mysqli_query($conn, $sqls);
$data2s_row = mysqli_fetch_array($data_results);
$user_zh = $data2s_row["username"];
$user_name = $data2s_row["name"];
$user_img = $data2s_row["img"];
$user_url = $data2s_row["url"];

$sql = "SELECT * FROM admin";
$result = $conn->query($sql);
while ($row = $result->fetch_assoc()) {
    $glyzhuser = $row["username"];
}

$data_result = mysqli_query($conn, "SELECT * FROM user WHERE username='{$user_zh}'");
$data2_row = mysqli_fetch_array($data_result);
$zjzhq = $data2_row["username"];
$zjnc = $data2_row["name"];
$zjimg = $data2_row["img"];
$zjhomeimg = $data2_row["homeimg"];
$zjsign = $data2_row["sign"];
$zjemail = $data2_row["email"];
$zjurl = $data2_row["url"];
$zjfbqx = $data2_row["essqx"];
$zjemtz = $data2_row["esseam"];
$passid = $data2_row["passid"];
$zid = $data2_row["id"];
$zjpassword = $data2_row["password"];

if ($user_passid != $passid) {
    setcookie("username", "", time() - 3600, "/");
    setcookie("passid", "", time() - 3600, "/");
    exit("<script language=\"JavaScript\">;alert(\"账号信息异常,请重新登录!\");location.href=\"../index.php\";</script>");
}

// 处理上传的文件
$file = $_FILES["file"];
$allowedExts = ["gif", "jpeg", "jpg", "png", "webp"];
$temp = explode(".", $_FILES["file"]["name"]);
$extension = end($temp);

if ($extension == "") {
    exit("<script language=\"JavaScript\">;alert(\"未选择图片!\");location.href=\"../setup.php\";</script>;");
}
if (!in_array($extension, $allowedExts)) {
    exit("<script language=\"JavaScript\">;alert(\"文件类型错误,请上传图片!\");location.href=\"../setup.php\";</script>;");
}
if ($file["size"] > 2 * 1024 * 1024) {
    
    exit("<script language=\"JavaScript\">;alert(\"请上传 2M 以内的图片!\");location.href=\"../setup.php\";</script>;");
}

// 腾讯云配置参数
require '../tencent-php/vendor/autoload.php'; // 包含腾讯云 SDK 自动加载文件
use Qcloud\Cos\Client;
use Qcloud\Cos\Exception\ServiceResponseException;

$secretId = 'AKIDtb8VfbKrxqWzHzXv65Nl5EAtQ3YxFNbh';
$secretKey = 'HXeZLwMgornYBg2BPSp23tcBdt9HVfpr';
$bucketName = '0717-1314972303';
$region = 'ap-guangzhou';
$operatorurl = 'https://0717-1314972303.cos.ap-guangzhou.myqcloud.com';

// 初始化腾讯云 COS 客户端
$cosClient = new Client([
    'region' => $region,
    'credentials' => [
        'secretId' => $secretId,
        'secretKey' => $secretKey,
    ]
]);

// 上传图片到腾讯云 COS
try {
    $cow = "/lan/";
    $imageName = mt_rand() . str_replace(".", "", microtime(true)) . substr(md5($zjzhq), 0, 12) . $file["name"];
    $imagePath = $file["tmp_name"];

    // 上传文件到腾讯云 COS
    $result = $cosClient->putObject([
        'Bucket' => $bucketName,
        'Key' => $cow . $imageName,
        'Body' => fopen($imagePath, 'rb'),
    ]);

    // 获取文件 URL
    $userimg = $operatorurl . $cow . $imageName;

    // 更新用户表
    $sql = "UPDATE user SET img='{$userimg}' WHERE id='{$zid}'";
    if (!$conn->query($sql)) {
        die('更新用户表失败: ' . $conn->error);
    }

    // 表格和字段映射
    $tables = [
        "essay" => ["column" => "ptpimg", "user_column" => "ptpuser"],
        "lcke" => ["column" => "limg", "user_column" => "luser"],
        "comm" => ["column" => "coimg", "user_column" => "couser"],
        "message" => ["column" => "fimg", "user_column" => "fuser"]
    ];

    foreach ($tables as $table => $info) {
        $sql = "SELECT id FROM $table WHERE {$info['user_column']} = '{$user_zh}'";
        $result = mysqli_query($conn, $sql);

        if (!$result) {
            die('查询错误: ' . mysqli_error($conn));
        }

        while ($row = mysqli_fetch_assoc($result)) {
            $ncid = $row["id"];
            $updateSql = "UPDATE $table SET {$info['column']}='{$userimg}' WHERE id='{$ncid}'";
            if (!$conn->query($updateSql)) {
                die('更新表格失败: ' . $conn->error);
            }
        }
    }

    ?><script language="JavaScript">;alert("上传成功！");location.href="../setup.php";</script>;<?php

} catch (ServiceResponseException $e) {
    ?><script language="JavaScript">;alert("上传失败: <?php echo $e->getMessage(); ?>");location.href="../setup.php";</script>;<?php
}

$conn->close();
?>
