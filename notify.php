<?php

//decode by nige112
if ($_SERVER["REQUEST_METHOD"] === "POST") {
	$arr = [["code" => "201", "msg" => "非法请求"]];
	exit(json_encode($arr, JSON_UNESCAPED_UNICODE));
}
$iteace = "0";
if (is_file("./config.php")) {
	require "./config.php";
} else {
	exit("请先安装程序！<a href=\"./install/\">点击安装</a>");
}
require "./api/wz.php";

// 如果用户未登录，跳转到首页
if ($userdlzt == 0) {
    echo "<script>location.href='./index.php';</script>";
    exit;
}

// 获取通知数据
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$perPage = 10;
$offset = ($page - 1) * $perPage;

// 检查通知表是否有数据
$sql_count = "SELECT COUNT(*) as count FROM notifications WHERE user_to = '{$user_zh}'";
$result_count = $conn->query($sql_count);
$total_count = 0;
if ($result_count && $row_count = $result_count->fetch_assoc()) {
    $total_count = $row_count['count'];
}

// 如果没有notifications表中的数据，从点赞和评论表中生成通知数据
if ($total_count == 0) {
    // 创建notifications表（如果不存在）
    $sql_create_table = "CREATE TABLE IF NOT EXISTS notifications (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        user_from VARCHAR(255),
        user_to VARCHAR(255),
        message TEXT,
        type VARCHAR(50),
        content_id VARCHAR(255),
        is_read TINYINT(1) DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
    $conn->query($sql_create_table);
    
    // 从点赞表获取数据
    $sql_likes = "SELECT l.id, l.luser as userid, l.lwz as cid, l.ltime as addtime, l.lname as name, l.limg as img, e.ptptext, e.ptpuser 
                  FROM lcke l 
                  LEFT JOIN essay e ON l.lwz = e.cid 
                  WHERE e.ptpuser = '{$user_zh}' AND l.luser != '{$user_zh}'
                  ORDER BY l.ltime DESC 
                  LIMIT 20";
    $result_likes = $conn->query($sql_likes);
    
    if ($result_likes && $result_likes->num_rows > 0) {
        while ($like = $result_likes->fetch_assoc()) {
            // 插入点赞通知
            $message = "{$like['name']} 赞了你的帖子：" . (mb_strlen($like['ptptext']) > 20 ? mb_substr($like['ptptext'], 0, 20) . '...' : $like['ptptext']);
            $sql_insert = "INSERT INTO notifications (user_from, user_to, message, type, content_id, is_read) 
                          VALUES ('{$like['userid']}', '{$user_zh}', '{$message}', 'like', '{$like['cid']}', 0)";
            $conn->query($sql_insert);
        }
    }
    
    // 从评论表获取数据
    $sql_comments = "SELECT c.id, c.pluser as userid, c.plwz as cid, c.pltime as addtime, c.plname as name, c.plimg as img, c.pltext as comment, e.ptptext, e.ptpuser 
                    FROM comm c 
                    LEFT JOIN essay e ON c.plwz = e.cid 
                    WHERE e.ptpuser = '{$user_zh}' AND c.pluser != '{$user_zh}'
                    ORDER BY c.pltime DESC 
                    LIMIT 20";
    $result_comments = $conn->query($sql_comments);
    
    if ($result_comments && $result_comments->num_rows > 0) {
        while ($comment = $result_comments->fetch_assoc()) {
            // 插入评论通知
            $message = "{$comment['name']} 评论了你的帖子：" . (mb_strlen($comment['comment']) > 20 ? mb_substr($comment['comment'], 0, 20) . '...' : $comment['comment']);
            $sql_insert = "INSERT INTO notifications (user_from, user_to, message, type, content_id, is_read) 
                          VALUES ('{$comment['userid']}', '{$user_zh}', '{$message}', 'comment', '{$comment['cid']}', 0)";
            $conn->query($sql_insert);
        }
    }
}

// 获取通知列表
$sql_notifications = "SELECT n.*, u.name as from_name, u.img as from_avatar 
                     FROM notifications n 
                     LEFT JOIN user u ON n.user_from = u.username 
                     WHERE n.user_to = '{$user_zh}' ";

// 根据筛选条件添加WHERE子句
if ($filter_type != 'all' && in_array($filter_type, ['like', 'comment', 'favorite'])) {
    $sql_notifications .= " AND n.type = '{$filter_type}' ";
}

$sql_notifications .= " ORDER BY n.created_at DESC LIMIT {$offset}, {$perPage}";
$result_notifications = $conn->query($sql_notifications);

// 获取未读通知数量
$sql_unread = "SELECT COUNT(*) as count FROM notifications WHERE user_to = '{$user_zh}' AND is_read = 0";
$result_unread = $conn->query($sql_unread);
$unread_count = 0;
if ($result_unread && $row_unread = $result_unread->fetch_assoc()) {
    $unread_count = $row_unread['count'];
}

// 获取通知类型筛选参数
$filter_type = isset($_GET['type']) ? $_GET['type'] : 'all';

// 获取总通知数量用于分页
$sql_count_filtered = "SELECT COUNT(*) as count FROM notifications WHERE user_to = '{$user_zh}'";
if ($filter_type != 'all' && in_array($filter_type, ['like', 'comment', 'favorite'])) {
    $sql_count_filtered .= " AND type = '{$filter_type}' ";
}
$result_count_filtered = $conn->query($sql_count_filtered);
$total_count_filtered = 0;
if ($result_count_filtered && $row_count_filtered = $result_count_filtered->fetch_assoc()) {
    $total_count_filtered = $row_count_filtered['count'];
}

// 分页参数
$total_pages = ceil($total_count_filtered / $perPage);

// 获取当前页的通知
$notifications = [];
if ($result_notifications && $result_notifications->num_rows > 0) {
    while ($row = $result_notifications->fetch_assoc()) {
        // 确保头像URL有效
        if (empty($row['from_avatar'])) {
            $row['from_avatar'] = './assets/img/default-avatar.png';
        }
        $notifications[] = $row;
    }
}

// 获取不同类型通知的统计
$notification_types = [
    'all' => ['label' => '全部', 'count' => 0, 'unread' => 0, 'icon' => 'icon-tongzhi'],
    'like' => ['label' => '点赞', 'count' => 0, 'unread' => 0, 'icon' => 'icon-dianzan'],
    'comment' => ['label' => '评论', 'count' => 0, 'unread' => 0, 'icon' => 'icon-pinglun'],
    'favorite' => ['label' => '收藏', 'count' => 0, 'unread' => 0, 'icon' => 'icon-shoucang']
];

// 获取每种类型的总通知数量
$sql_type_counts = "SELECT type, COUNT(*) as count FROM notifications 
                    WHERE user_to = '{$user_zh}' GROUP BY type";
$result_type_counts = $conn->query($sql_type_counts);

// 获取每种类型的未读通知数量
$sql_unread_counts = "SELECT type, COUNT(*) as count FROM notifications 
                      WHERE user_to = '{$user_zh}' AND is_read = 0 GROUP BY type";
$result_unread_counts = $conn->query($sql_unread_counts);

// 获取每种类型的通知总数
$total_all = 0;
if ($result_type_counts && $result_type_counts->num_rows > 0) {
    while ($row = $result_type_counts->fetch_assoc()) {
        $type = $row['type'];
        $count = $row['count'];
        $total_all += $count;
        
        if (isset($notification_types[$type])) {
            $notification_types[$type]['count'] = $count;
        }
    }
}
$notification_types['all']['count'] = $total_all;

// 获取每种类型的未读通知数
$total_unread = 0;
if ($result_unread_counts && $result_unread_counts->num_rows > 0) {
    while ($row = $result_unread_counts->fetch_assoc()) {
        $type = $row['type'];
        $count = $row['count'];
        $total_unread += $count;
        
        if (isset($notification_types[$type])) {
            $notification_types[$type]['unread'] = $count;
        }
    }
}
$notification_types['all']['unread'] = $total_unread;

// 标记所有通知为已读
if (isset($_GET['mark_read'])) {
    $sql_mark_read = "UPDATE notifications SET is_read = 1 WHERE user_to = '{$user_zh}'";
    $conn->query($sql_mark_read);
    header("Location: ./notify.php");
    exit;
}

?><!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>通知 - <?php echo $name;?></title>
    <!--为搜索引擎定义关键词-->
    <meta name="keywords" content="0717谦友圈、谦友、薛之谦粉丝、薛之谦、兴趣爱好分享、生活分享、在线聊天、谦友群聊、粉丝社交平台">
    <!--为网页定义描述内容 用于告诉搜索引擎，你网站的主要内容-->
    <meta name="description" content="<?php echo $name . "," . $subtitle;?>">
    <meta name="author" content="<?php echo $name;?>">
    <meta name="copyright" content="<?php echo $name;?>">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta http-equiv="cache-control" content="no-cache">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0,minimum-scale=1.0, user-scalable=no minimal-ui">
    <link rel="apple-touch-icon" sizes="57x57" href="<?php echo $icon;?>" /><!-- Standard iPhone -->
    <link rel="apple-touch-icon" sizes="114x114" href="<?php echo $icon;?>" /><!-- Retina iPhone -->
    <link rel="apple-touch-icon" sizes="72x72" href="<?php echo $icon;?>" /><!-- Standard iPad -->
    <link rel="apple-touch-icon" sizes="144x144" href="<?php echo $icon;?>" /><!-- Retina iPad -->
    <link rel="icon" href="<?php echo $icon;?>" type="image/x-icon" />
    <meta name="robots" content="index,follow">
    <meta name="format-detection" content="telephone=no">
    <meta http-equiv="x-rim-auto-match" content="none">
    
    <?php 
if ($rosdomain == 1) {
	?><meta name="referrer" content="no-referrer"><?php 
}
?>
    <link rel="stylesheet" href="<?php echo $iconurl_url;?>">
    <link rel="stylesheet" type="text/css" href="./assets/css/style.css?v=<?php echo $resversion;?>">
    <link rel="stylesheet" type="text/css" href="./assets/css/footer-menu.css?v=<?php echo $resversion;?>">
    <link rel="stylesheet" type="text/css" href="./assets/mesg/dist/css/style.css?v=<?php echo $resversion;?>"><!--引入弹窗对话框-->
    <link rel="stylesheet" type="text/css" href="./assets/css/jquery.fancybox.min.css?v=<?php echo $resversion;?>">
    <!-- 引入更丰富的图标库 -->
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">

    <style>
        /* 全局设置 */
        body, html {
            scroll-behavior: smooth;
        }
        
        /* 通知页顶部 */
        .notify-container {
            padding-bottom: 70px;
        }
        
        .sh-main-head {
            height: auto;
            min-height: 40px;
            position: sticky;
            top: 0;
            z-index: 1000;
            background-color: rgba(255, 255, 255, 0.85);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            transition: all 0.3s ease;
            padding: 5px 0;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }
        
        .dark-theme .sh-main-head {
            background-color: rgba(30, 30, 30, 0.85);
            border-bottom: 1px solid rgba(255,255,255,0.05);
        }
        
        .notify-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            margin-bottom: 10px;
        }
        
        .notify-title {
            font-size: 24px;
            font-weight: bold;
            background: linear-gradient(135deg, #007aff, #5ac8fa);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            display: flex;
            align-items: center;
        }
        
        .notify-title i {
            margin-right: 8px;
            font-size: 26px;
            background: linear-gradient(135deg, #007aff, #5ac8fa);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .notify-badge {
            background-color: #ff3b30;
            color: white;
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 10px;
            margin-left: 5px;
            animation: pulse 1.5s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.2); opacity: 0.8; }
            100% { transform: scale(1); opacity: 1; }
        }
        
        .notify-mark-read {
            font-size: 14px;
            color: #007aff;
            text-decoration: none;
            background-color: rgba(0, 122, 255, 0.1);
            padding: 8px 15px;
            border-radius: 15px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            display: flex;
            align-items: center;
        }
        
        .notify-mark-read i {
            margin-right: 5px;
            font-size: 16px;
        }
        
        .notify-mark-read:active {
            background-color: rgba(0, 122, 255, 0.2);
            transform: scale(0.95);
        }
        
        /* 通知分类导航 */
        .notify-tabs {
            display: flex;
            overflow-x: auto;
            padding: 10px 10px 5px;
            margin: 5px 0 15px;
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE 10+ */
            background-color: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            position: sticky;
            top: 40px;
            z-index: 100;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }
        
        .notify-tabs::-webkit-scrollbar {
            display: none; /* Chrome/Safari/Opera */
        }
        
        .dark-theme .notify-tabs {
            background-color: rgba(30, 30, 30, 0.8);
            border-bottom: 1px solid rgba(255,255,255,0.05);
        }
        
        .notify-tab {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 12px 18px;
            margin-right: 12px;
            border-radius: 15px;
            min-width: 70px;
            background-color: #b3b3b3;
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            overflow: hidden;
        }
        
        .dark-theme .notify-tab {
            background-color: #333;
        }
        
        .notify-tab.active {
            background-color: #007aff;
            box-shadow: 0 5px 15px rgba(0, 122, 255, 0.3);
            transform: translateY(-3px);
        }
        
        .notify-tab:active {
            transform: scale(0.95);
        }
        
        .notify-tab-icon {
            font-size: 22px;
            color: #666;
            margin-bottom: 8px;
            position: relative;
            z-index: 1;
        }
        
        .dark-theme .notify-tab-icon {
            color: #bbb;
        }
        
        .notify-tab.active .notify-tab-icon,
        .notify-tab.active .notify-tab-label,
        .notify-tab.active .notify-tab-count {
            color: white;
        }
        
        .notify-tab-label {
            font-size: 12px;
            color: #666;
            font-weight: 500;
            position: relative;
            z-index: 1;
        }
        
        .dark-theme .notify-tab-label {
            color: #bbb;
        }
        
        .notify-tab-count {
            position: absolute;
            top: 5px;
            right: 5px;
            background-color: #ff3b30;
            color: white;
            font-size: 10px;
            min-width: 16px;
            height: 16px;
            border-radius: 8px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-weight: bold;
            z-index: 2;
        }
        
        .notify-tab.active .notify-tab-count {
            background-color: white;
            color: #007aff;
        }
        
        /* 通知项样式 */
        .notify-item-container {
            margin: 0 15px 15px;
        }
        
        .notify-item {
            background-color: white;
            border-radius: 18px;
            padding: 18px;
            margin-bottom: 15px;
            box-shadow: 0 8px 20px rgba(0,0,0,0.05);
            position: relative;
            overflow: hidden;
            transform: translateY(0);
            transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
            animation: fadeInUp 0.5s ease-out forwards;
            opacity: 0;
            border: 1px solid rgba(0,0,0,0.03);
        }
        
        @keyframes fadeInUp {
            from { transform: translateY(20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        
        .notify-item:active {
            transform: scale(0.98);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .dark-theme .notify-item {
            background-color: #272727;
            box-shadow: 0 8px 20px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.05);
        }
        
        .notify-item.unread {
            border-left: 3px solid #007aff;
        }
        
        .notify-item.unread:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, rgba(0, 122, 255, 0.05), transparent);
            z-index: 0;
        }
        
        .notify-item-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            position: relative;
            z-index: 1;
        }
        
        .notify-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 12px;
            border: 2px solid rgba(0, 122, 255, 0.2);
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }
        
        .notify-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .notify-info {
            flex: 1;
        }
        
        .notify-name {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
        }
        
        .dark-theme .notify-name {
            color: #eee;
        }
        
        .notify-time {
            font-size: 12px;
            color: #999;
            display: flex;
            align-items: center;
        }
        
        .notify-time i {
            font-size: 14px;
            margin-right: 4px;
        }
        
        .notify-type-icon {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: rgba(0, 122, 255, 0.1);
            color: #007aff;
            font-size: 18px;
            box-shadow: 0 4px 10px rgba(0, 122, 255, 0.15);
        }
        
        .notify-item-content {
            padding-left: 60px;
            position: relative;
            z-index: 1;
        }
        
        .notify-message {
            font-size: 15px;
            line-height: 1.5;
            color: #333;
            margin-bottom: 12px;
        }
        
        .dark-theme .notify-message {
            color: #ddd;
        }
        
        .notify-actions {
            display: flex;
            justify-content: flex-end;
        }
        
        .notify-action-btn {
            padding: 8px 14px;
            border-radius: 15px;
            font-size: 13px;
            background-color: #f5f5f5;
            color: #666;
            margin-left: 10px;
            transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
            font-weight: 500;
        }
        
        .dark-theme .notify-action-btn {
            background-color: #333;
            color: #bbb;
        }
        
        .notify-action-btn.primary {
            background-color: #007aff;
            color: white;
            box-shadow: 0 4px 10px rgba(0, 122, 255, 0.2);
        }
        
        .notify-action-btn:active {
            transform: scale(0.95);
        }
        
        /* 空状态样式 */
        .notify-empty {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 60px 0;
            color: #999;
            text-align: center;
        }
        
        .notify-empty i {
            font-size: 70px;
            color: #ddd;
            margin-bottom: 20px;
        }
        
        .dark-theme .notify-empty i {
            color: #444;
        }
        
        .notify-empty-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #666;
        }
        
        .dark-theme .notify-empty-title {
            color: #bbb;
        }
        
        .notify-empty-desc {
            font-size: 15px;
            max-width: 280px;
            line-height: 1.5;
        }
        
        /* 分页样式 */
        .notify-pagination {
            display: flex;
            justify-content: center;
            padding: 20px 0;
        }
        
        .page-link {
            display: inline-flex;
            justify-content: center;
            align-items: center;
            min-width: 38px;
            height: 38px;
            border-radius: 12px;
            margin: 0 5px;
            background-color: white;
            color: #666;
            text-decoration: none;
            transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
            box-shadow: 0 4px 10px rgba(0,0,0,0.05);
            font-weight: 500;
        }
        
        .dark-theme .page-link {
            background-color: #272727;
            color: #bbb;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }
        
        .page-link.active {
            background-color: #007aff;
            color: white;
            box-shadow: 0 4px 10px rgba(0, 122, 255, 0.2);
        }
        
        .page-link:active {
            transform: scale(0.95);
        }
        
        /* 底部菜单徽章位置调整 */
        .footer-menu .notification-dot {
            position: absolute;
            top: 5px;
            right: 25%;
            width: 8px;
            height: 8px;
            background-color: #ff3b30;
            border-radius: 50%;
            animation: pulse 1.5s infinite;
        }
    </style>
    
    <?php echo $scfontzt;?>
    
    <?php 
$folderPath = "./user/bobg/";
if (!file_exists($folderPath)) {
	mkdir($folderPath, 511, true);
}
if (file_exists("./user/bobg/bobg.jpg")) {
	?><!--背景图--><style>body{background-image: url(./user/bobg/bobg.jpg);}</style><?php 
} elseif (file_exists("./user/bobg/bobg.png")) {
	?><!--背景图--><style>body{background-image: url(./user/bobg/bobg.png);}</style><?php 
} elseif (file_exists("./user/bobg/bobg.jpeg")) {
	?><!--背景图--><style>body{background-image: url(./user/bobg/bobg.jpeg);}</style><?php 
} elseif (file_exists("./user/bobg/bobg.gif")) {
	?><!--背景图--><style>body{background-image: url(./user/bobg/bobg.gif);}</style><?php 
}
?>
    <?php echo "<style>" . $filtercucss . "</style>";?>
</head>
<body class="<?php 
if (isset($_COOKIE["dark_theme"])) {
	if ($_COOKIE["dark_theme"] == "dark-theme") {
		echo "dark-theme";
	}
}
echo "\">
    ";
if ($pagepass != "") {
	if (isset($_COOKIE["pagepass"])) {
		if ($_COOKIE["pagepass"] != md5(md5($pagepass))) {
			include "./site/pagepass.php";
		}
	} else {
		include "./site/pagepass.php";
	}
}
?>
    <div class="centent">
        <div class="sh-main">
            
            
            <div class="notify-container">
                <!-- 通知页头部 -->
                <div class="notify-header">
                    <div class="notify-title">
                        <i class="ri-notification-3-line"></i>通知
                        <?php if ($unread_count > 0): ?>
                        <span class="notify-badge"><?php echo $unread_count; ?></span>
                        <?php endif; ?>
                    </div>
                    <?php if ($unread_count > 0): ?>
                    <a href="./notify.php?mark_read=1" class="notify-mark-read">
                        <i class="ri-check-double-line"></i>全部已读
                    </a>
                    <?php endif; ?>
                </div>
                
                <!-- 通知标签 -->
                <div class="notify-tabs">
                    <?php foreach ($notification_types as $type => $data): ?>
                    <a href="./notify.php?type=<?php echo $type; ?>" class="notify-tab <?php echo $filter_type === $type ? 'active' : ''; ?>">
                        <i class="iconfont <?php echo $data['icon']; ?>"></i>
                        <span><?php echo $data['label']; ?></span>
                        <?php if ($data['unread'] > 0): ?>
                        <span class="notify-tab-count"><?php echo $data['unread']; ?></span>
                        <?php endif; ?>
                    </a>
                    <?php endforeach; ?>
                </div>
                
                <!-- 通知列表 -->
                <div class="notify-item-container">
                    <?php if (empty($notifications)): ?>
                    <div class="notify-empty">
                        <i class="ri-notification-off-line"></i>
                        <div class="notify-empty-title">暂无通知</div>
                        <div class="notify-empty-desc">当有人与你互动时，你会在这里收到通知</div>
                    </div>
                    <?php else: ?>
                    <?php foreach ($notifications as $index => $notification): 
                        $is_unread = $notification['is_read'] == 0;
                        $type_icon = $notification['type'] == 'like' ? 'heart-3-fill' : 
                           ($notification['type'] == 'comment' ? 'chat-1-fill' : 
                           ($notification['type'] == 'favorite' ? 'star-fill' : 'settings-3-fill'));
                        
                        // 计算时间差
                        $time_diff = time() - strtotime($notification['created_at']);
                        if ($time_diff < 60) {
                            $time_str = '刚刚';
                        } elseif ($time_diff < 3600) {
                            $time_str = floor($time_diff / 60) . '分钟前';
                        } elseif ($time_diff < 86400) {
                            $time_str = floor($time_diff / 3600) . '小时前';
                        } elseif ($time_diff < 2592000) {
                            $time_str = floor($time_diff / 86400) . '天前';
                        } else {
                            $time_str = date('Y-m-d', strtotime($notification['created_at']));
                        }
                        
                        // 获取发送者姓名和头像
                        $from_name = !empty($notification['from_name']) ? htmlspecialchars($notification['from_name']) : '系统通知';
                        $from_avatar = !empty($notification['from_avatar']) ? $notification['from_avatar'] : './assets/img/default-avatar.png';
                    ?>
                    <div class="notify-item <?php echo $is_unread ? 'unread' : ''; ?>" style="animation-delay: <?php echo $index * 0.05; ?>s" onclick="markAsRead(<?php echo $notification['id']; ?>, '<?php echo $notification['content_id']; ?>')">
                        <div class="notify-item-header">
                            <div class="notify-avatar">
                                <img src="<?php echo $from_avatar; ?>" alt="头像" onerror="this.src='./assets/img/default-avatar.png'">
                            </div>
                            <div class="notify-info">
                                <div class="notify-name"><?php echo $from_name; ?></div>
                                <div class="notify-time"><i class="ri-time-line"></i> <?php echo $time_str; ?></div>
                            </div>
                            <div class="notify-type-icon">
                                <i class="ri-<?php echo $type_icon; ?>"></i>
                            </div>
                        </div>
                        <div class="notify-item-content">
                            <div class="notify-message"><?php echo htmlspecialchars($notification['message']); ?></div>
                            <?php if ($notification['type'] === 'favorite' && $notification['user_from'] !== $user_zh): ?>
                            <div class="notify-actions">
                                <a href="./api/favorite.php?user=<?php echo $notification['user_from']; ?>" class="notify-action-btn primary">收藏回ta</a>
                                <a href="./message.php?user=<?php echo $notification['user_from']; ?>" class="notify-action-btn">发消息</a>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                    
                    <!-- 分页 -->
                    <?php if ($total_pages > 1): ?>
                    <div class="notify-pagination">
                        <?php if ($page > 1): ?>
                        <a href="./notify.php?type=<?php echo $filter_type; ?>&page=<?php echo $page - 1; ?>" class="page-link">
                            <i class="ri-arrow-left-s-line"></i>
                        </a>
                        <?php endif; ?>
                        
                        <?php
                        $start_page = max(1, $page - 2);
                        $end_page = min($total_pages, $start_page + 4);
                        if ($end_page - $start_page < 4 && $start_page > 1) {
                            $start_page = max(1, $end_page - 4);
                        }
                        
                        for ($i = $start_page; $i <= $end_page; $i++):
                        ?>
                        <a href="./notify.php?type=<?php echo $filter_type; ?>&page=<?php echo $i; ?>" class="page-link <?php echo $i == $page ? 'active' : ''; ?>">
                            <?php echo $i; ?>
                        </a>
                        <?php endfor; ?>
                        
                        <?php if ($page < $total_pages): ?>
                        <a href="./notify.php?type=<?php echo $filter_type; ?>&page=<?php echo $page + 1; ?>" class="page-link">
                            <i class="ri-arrow-right-s-line"></i>
                        </a>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 底部菜单 -->
    <div class="footer-menu">
        <a href="./index.php" class="footer-menu-item">
            <i class="ri-home-4-line"></i>
            <span>首页</span>
        </a>
        <a href="./discover.php" class="footer-menu-item">
            <i class="ri-compass-3-line"></i>
            <span>发现</span>
        </a>
        <a href="./notify.php" class="footer-menu-item active">
            <i class="ri-notification-2-fill"></i>
            <span>通知</span>
            <?php
            // 检查是否有未读通知
            if ($userdlzt == 1 && $total_unread > 0) {
                echo '<div class="notification-dot">' . ($total_unread > 99 ? '99+' : $total_unread) . '</div>';
            }
            ?>
        </a>
        <a href="./home.php" class="footer-menu-item">
            <i class="ri-user-3-line"></i>
            <span>我的</span>
        </a>
    </div>

<script charset="UTF-8" id="LA_COLLECT" src="//sdk.51.la/js-sdk-pro.min.js"></script>
<script>LA.init({id:"KbK2AGjkzWGBfylr",ck:"KbK2AGjkzWGBfylr"})</script>
<script type="text/javascript" src="./assets/js/home.js?v=<?php echo $resversion;?>"></script>
<script type="text/javascript" src="./assets/js/jquery.min.js"></script>
<script type="text/javascript" src="./assets/mesg/dist/js/sh-noytf.js?v=<?php echo $resversion;?>"></script>
<script type="text/javascript">
    // 添加滚动监听，实现导航背景透明度变化
    window.addEventListener('scroll', function() {
        var header = document.querySelector('.sh-main-head');
        var scrollPosition = window.scrollY;
        
        if (scrollPosition > 30) {
            header.style.backgroundColor = 'var(--background)';
            header.style.boxShadow = '0 2px 10px rgba(0,0,0,0.05)';
        } else {
            header.style.backgroundColor = 'transparent';
            header.style.boxShadow = 'none';
        }
    });
    
    // 为通知项添加动画延迟
    document.querySelectorAll('.notify-item').forEach(function(item, index) {
        item.style.animationDelay = (index * 0.05) + 's';
    });
    
    // 标记通知为已读并跳转到详情页
    function markAsRead(notificationId, contentId) {
        // 使用fetch API代替XMLHttpRequest
        fetch('./api/mark_notification_read.php?id=' + notificationId, {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok: ' + response.status);
            }
            return response.json();
        })
        .then(data => {
            if (data[0] && data[0].code === "200") {
                console.log("通知标记已读成功");
                
                // 更新UI，移除未读样式
                var notificationItems = document.querySelectorAll('.notify-item');
                notificationItems.forEach(function(item) {
                    if (item.getAttribute('onclick').includes('markAsRead(' + notificationId + ',')) {
                        item.classList.remove('unread');
                    }
                });
                
                // 减少未读通知数量
                var badge = document.querySelector('.notify-badge');
                if (badge) {
                    var count = parseInt(badge.textContent);
                    if (count > 1) {
                        badge.textContent = count - 1;
                    } else {
                        badge.remove();
                    }
                }
                
                // 减少导航栏通知数量
                var tabCount = document.querySelector('.notify-tab.active .notify-tab-count');
                if (tabCount) {
                    var count = parseInt(tabCount.textContent);
                    if (count > 1) {
                        tabCount.textContent = count - 1;
                    } else {
                        tabCount.remove();
                    }
                }
                
                // 更新底部导航小红点
                var updateNotificationDot = function() {
                    var allCount = 0;
                    document.querySelectorAll('.notify-tab-count').forEach(function(el) {
                        allCount += parseInt(el.textContent || 0);
                    });
                    
                    var dot = document.querySelector('.footer-menu .notification-dot');
                    if (dot && allCount === 0) {
                        dot.remove();
                    }
                };
                
                updateNotificationDot();
            } else {
                console.error("标记已读失败:", data);
            }
        })
        .catch(error => {
            console.error('标记已读出错:', error);
        })
        .finally(() => {
            // 无论成功与否，短暂延迟后跳转到帖子详情页
            setTimeout(function() {
                window.location.href = './view.php?cid=' + contentId;
            }, 300); // 300毫秒延迟，让用户看到已读状态变化
        });
    }
</script>
<?php echo "<script type=\"text/javascript\">" . $filtercujs . "</script>";?></body>
</html> 