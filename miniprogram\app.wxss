/* 0717谦友圈小程序全局样式 */

/* 全局变量定义 */
page {
  --primary-color: #007AFF;
  --secondary-color: #5856D6;
  --success-color: #34C759;
  --warning-color: #FF9500;
  --error-color: #FF3B30;
  --text-primary: #000000;
  --text-secondary: #8E8E93;
  --text-tertiary: #C7C7CC;
  --background-primary: #FFFFFF;
  --background-secondary: #F2F2F7;
  --background-tertiary: #FFFFFF;
  --separator-color: #C6C6C8;
  --border-radius: 12rpx;
  --shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);
}

/* 基础样式重置 */
* {
  box-sizing: border-box;
}

page {
  background-color: var(--background-secondary);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  line-height: 1.4;
  color: var(--text-primary);
}

/* 通用容器 */
.container {
  padding: 32rpx;
}

.safe-area {
  padding-bottom: env(safe-area-inset-bottom);
}

/* 卡片样式 */
.card {
  background-color: var(--background-primary);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  margin-bottom: 24rpx;
  overflow: hidden;
}

.card-header {
  padding: 32rpx 32rpx 0;
}

.card-body {
  padding: 32rpx;
}

.card-footer {
  padding: 0 32rpx 32rpx;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 48rpx;
  border-radius: var(--border-radius);
  font-size: 32rpx;
  font-weight: 500;
  text-align: center;
  transition: all 0.3s ease;
  border: none;
  outline: none;
}

.btn-primary {
  background-color: var(--primary-color);
  color: #FFFFFF;
}

.btn-primary:active {
  background-color: #0056CC;
}

.btn-secondary {
  background-color: var(--background-secondary);
  color: var(--text-primary);
}

.btn-secondary:active {
  background-color: #E5E5EA;
}

.btn-ghost {
  background-color: transparent;
  color: var(--primary-color);
  border: 2rpx solid var(--primary-color);
}

.btn-ghost:active {
  background-color: var(--primary-color);
  color: #FFFFFF;
}

.btn-small {
  padding: 16rpx 32rpx;
  font-size: 28rpx;
}

.btn-large {
  padding: 32rpx 64rpx;
  font-size: 36rpx;
}

.btn-block {
  width: 100%;
}

.btn-disabled {
  opacity: 0.5;
  pointer-events: none;
}

/* 输入框样式 */
.input-group {
  margin-bottom: 32rpx;
}

.input-label {
  display: block;
  margin-bottom: 16rpx;
  font-size: 28rpx;
  color: var(--text-secondary);
}

.input {
  width: 100%;
  padding: 24rpx 32rpx;
  border: 2rpx solid var(--separator-color);
  border-radius: var(--border-radius);
  font-size: 32rpx;
  background-color: var(--background-primary);
  color: var(--text-primary);
}

.input:focus {
  border-color: var(--primary-color);
}

.textarea {
  min-height: 200rpx;
  resize: vertical;
}

/* 头像样式 */
.avatar {
  border-radius: 50%;
  overflow: hidden;
}

.avatar-small {
  width: 60rpx;
  height: 60rpx;
}

.avatar-medium {
  width: 80rpx;
  height: 80rpx;
}

.avatar-large {
  width: 120rpx;
  height: 120rpx;
}

/* 文本样式 */
.text-primary {
  color: var(--text-primary);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-tertiary {
  color: var(--text-tertiary);
}

.text-success {
  color: var(--success-color);
}

.text-warning {
  color: var(--warning-color);
}

.text-error {
  color: var(--error-color);
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

/* 字体大小 */
.text-xs {
  font-size: 24rpx;
}

.text-sm {
  font-size: 28rpx;
}

.text-base {
  font-size: 32rpx;
}

.text-lg {
  font-size: 36rpx;
}

.text-xl {
  font-size: 40rpx;
}

.text-2xl {
  font-size: 48rpx;
}

/* 布局工具类 */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.flex-row {
  flex-direction: row;
}

.items-center {
  align-items: center;
}

.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.flex-1 {
  flex: 1;
}

/* 间距工具类 */
.m-0 { margin: 0; }
.m-1 { margin: 8rpx; }
.m-2 { margin: 16rpx; }
.m-3 { margin: 24rpx; }
.m-4 { margin: 32rpx; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 8rpx; }
.mt-2 { margin-top: 16rpx; }
.mt-3 { margin-top: 24rpx; }
.mt-4 { margin-top: 32rpx; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 8rpx; }
.mb-2 { margin-bottom: 16rpx; }
.mb-3 { margin-bottom: 24rpx; }
.mb-4 { margin-bottom: 32rpx; }

.p-0 { padding: 0; }
.p-1 { padding: 8rpx; }
.p-2 { padding: 16rpx; }
.p-3 { padding: 24rpx; }
.p-4 { padding: 32rpx; }

/* 分割线 */
.divider {
  height: 2rpx;
  background-color: var(--separator-color);
  margin: 32rpx 0;
}

/* 加载状态 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 64rpx;
  color: var(--text-secondary);
}

/* 空状态 */
.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 128rpx 64rpx;
  color: var(--text-secondary);
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
}

/* 动画 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

/* 响应式 */
@media (max-width: 750rpx) {
  .container {
    padding: 24rpx;
  }
  
  .card-body {
    padding: 24rpx;
  }
}
