<?php

//decode by nige112
if ($_SERVER["REQUEST_METHOD"] === "POST") {
	$arr = [["code" => "201", "msg" => "非法请求"]];
	exit(json_encode($arr, JSON_UNESCAPED_UNICODE));
}
$iteace = "0";
if (is_file("./config.php")) {
	require "./config.php";
} else {
	exit("未检测到配置文件：<span style=\"color: red;\">config.php</span>，若您是首次使用请先进行安装,删除文件夹 <span style=\"color: red;\">[install/]</span> 下的 <span style=\"color: red;\">[ins.bak]</span> 文件后进行安装。");
}
require "./api/wz.php";
$useke = addslashes(htmlspecialchars($_GET["useke"]));
$sql = "select * from user where username = '{$useke}'";
$result = $conn->query($sql);

?><!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>网站公告 - <?php echo $name;?></title>
    <!--为搜索引擎定义关键词-->
    <meta name="keywords" content="<?php echo $name;?>">
    <!--为网页定义描述内容 用于告诉搜索引擎，你网站的主要内容-->
    <meta name="description" content="<?php echo $name . "," . $subtitle;?>">
    <meta name="author" content="<?php echo $name;?>">
    <meta name="copyright" content="<?php echo $name;?>">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta http-equiv="cache-control" content="no-cache">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0,minimum-scale=1.0, user-scalable=no minimal-ui">
    <link rel="apple-touch-icon" sizes="57x57" href="<?php echo $icon;?>" /><!-- Standard iPhone -->
    <link rel="apple-touch-icon" sizes="114x114" href="<?php echo $icon;?>" /><!-- Retina iPhone -->
    <link rel="apple-touch-icon" sizes="72x72" href="<?php echo $icon;?>" /><!-- Standard iPad -->
    <link rel="apple-touch-icon" sizes="144x144" href="<?php echo $icon;?>" /><!-- Retina iPad -->
    <link rel="icon" href="<?php echo $icon;?>" type="image/x-icon" />
    <meta name="robots" content="index,follow">
    <meta name="format-detection" content="telephone=no">
    <meta http-equiv="x-rim-auto-match" content="none">
    <style>
  body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 0;
    }

   .timeline-wrapper {
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
    }

   .timeline {
      list-style: none;
      padding: 0;
    }

   .timeline-item {
      position: relative;
      padding-left: 40px;
      margin-bottom: 30px;
    }

   .timeline-item::before {
      content: '';
      width: 4px;
      height: 100%;
      background-color: #03a9f4;
      position: absolute;
      left: 12px;
    }

   .timeline-dot {
      width: 16px;
      height: 16px;
      background-color: #03a9f4;
      border-radius: 50%;
      position: absolute;
      left: 6px;
      top: 0;
    }

   .timeline-date {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 10px;
    }

   .timeline-content {
      font-size: 14px;
    }

    @media (max-width: 767px) {
     .timeline-wrapper {
        padding: 10px;
      }

     .timeline-item {
        padding-left: 30px;
      }

     .timeline-item::before {
        left: 8px;
      }

     .timeline-dot {
        width: 12px;
        height: 12px;
        left: 4px;
      }
    }

  </style>
    <?php 
if ($rosdomain == 1) {
	?><meta name="referrer" content="no-referrer"><?php 
}
?>    <link rel="stylesheet" href="<?php echo $iconurl_url;?>">
    <link rel="stylesheet" type="text/css" href="./assets/css/style.css?v=<?php echo $resversion;?>">
    <link rel="stylesheet" type="text/css" href="./assets/mesg/dist/css/style.css?v=<?php echo $resversion;?>"><!--引入弹窗对话框-->
    <?php echo $scfontzt;?>    <?php 
$folderPath = "./user/bobg/";
if (!file_exists($folderPath)) {
	mkdir($folderPath, 511, true);
}
if (file_exists("./user/bobg/bobg.jpg")) {
	?><!--背景图--><style>body{background-image: url(./user/bobg/bobg.jpg);}</style><?php 
} elseif (file_exists("./user/bobg/bobg.png")) {
	?><!--背景图--><style>body{background-image: url(./user/bobg/bobg.png);}</style><?php 
} elseif (file_exists("./user/bobg/bobg.jpeg")) {
	?><!--背景图--><style>body{background-image: url(./user/bobg/bobg.jpeg);}</style><?php 
} elseif (file_exists("./user/bobg/bobg.gif")) {
	?><!--背景图--><style>body{background-image: url(./user/bobg/bobg.gif);}</style><?php 
}
?>    <?php echo "<style>" . $filtercucss . "</style>";?></head>
<body class="<?php 
if (isset($_COOKIE["dark_theme"])) {
	if ($_COOKIE["dark_theme"] == "dark-theme") {
		echo "dark-theme";
	}
}
echo "\">
    ";
if ($pagepass != "") {
	if (isset($_COOKIE["pagepass"])) {
		if ($_COOKIE["pagepass"] != md5(md5($pagepass))) {
			include "./site/pagepass.php";
		}
	} else {
		include "./site/pagepass.php";
	}
}
?>    <!--网页主体框架-->
    <div class="centent">
        <!-- 页面载体 -->
        <div class="sh-main setup-main">
            <!-- 头部 -->
            <div class="sh-main-head setup-main-head">
                <!-- 顶部菜单 -->
                <div class="sh-main-head-top setup-main-top" id="sh-main-head-top">
                    <!-- 左边 -->
                    <div class="sh-main-head-top-left">
                        <div class="sh-main-head-top-left-s" onclick="location.href='./index.php'">
                            <i class="iconfont icon-weibiaoti al-sxbh" id="top-left-1"></i>
                        </div>
                        <div class="sh-main-head-top-left-s setup-main-head-top-left-s" style="width:80px">
                            <span class="setup-main-title" >网站公告</span>
                        </div>
                    </div>
                    <!-- 右边 -->
                    <!--div class="sh-main-head-top-right">
                    </div-->
                </div>

            </div>
            <!-- 头部 end -->
            
            <!-- 设置内容区域 -->

            <div class="sh-login-main-con" style="margin-top: 0px;background: var(--cobg);padding-top: 10px;">
 <div class="timeline-wrapper">
    <ul class="timeline">
    <li class="timeline-item">
        <div class="timeline-dot"></div>
        <div class="timeline-date">2024 年 11 月 22 日</div>
        <div class="timeline-content">
          <p>1.《租购》新歌上线，紧急修复发布音乐资源没有封面无法选择音乐问题，新增自定义选择音乐发布.</p>
          <p>2.修复上传视频无法正常播放问题.</p>
          <p>3.新增图片压缩通道，可解决图片超过2M无法上传问题.</p>
        </div>
      </li>
     <li class="timeline-item">
        <div class="timeline-dot"></div>
        <div class="timeline-date">2024 年 11 月 18 日</div>
        <div class="timeline-content">
          <p>1.根据谦友后台留言建议增加了朋友圈置顶功能,可使用签到积分进行置顶.</p>
          <p>2.为避免频繁置顶,采用积分进行限制（7个积分置顶一次，一天后自动取消置顶）积分可在首页右侧签到获得.</p>
          <p>2.如果有BUG请公众号留言反馈,感谢支持.</p>
        </div>
      </li>
     <li class="timeline-item">
        <div class="timeline-dot"></div>
        <div class="timeline-date">2024 年 11 月 17 日</div>
        <div class="timeline-content">
          <p>1.修复【是否继续上次浏览的朋友圈】频繁弹出问题,设置6小时弹出一次.</p>
        </div>
      </li>
     <li class="timeline-item">
        <div class="timeline-dot"></div>
        <div class="timeline-date">2024 年 11 月 09 日</div>
        <div class="timeline-content">
          <p>1.谦友圈各项功能已经稳定,有问题可公众号进行留言反馈,最近可能不会有新的更新了,我也要好好工作啦.</p>
        </div>
      </li>
     <li class="timeline-item">
        <div class="timeline-dot"></div>
        <div class="timeline-date">2024 年 11 月 04 日</div>
        <div class="timeline-content">
          <p>1.新增谦友创作排行榜,根据发圈数量进行排名.</p>
          <p>2.为了减轻运营圈子成本,小程序加入适量广告，麻烦各位谦友能够帮忙点点.</p>
        </div>
      </li>
     <li class="timeline-item">
        <div class="timeline-dot"></div>
        <div class="timeline-date">2024 年 10 月 30 日</div>
        <div class="timeline-content">
          <p>1.修复聊天室部分用户不显示头像问题.</p>
        </div>
      </li>
     <li class="timeline-item">
        <div class="timeline-dot"></div>
        <div class="timeline-date">2024 年 10 月 29 日</div>
        <div class="timeline-content">
          <p>1.谦友聊天室和小程序正式上线.</p>
          <p>2.增加聊天室聊天记录的分页查看.</p>
        </div>
      </li>
     <li class="timeline-item">
        <div class="timeline-dot"></div>
        <div class="timeline-date">2024 年 10 月 27 日</div>
        <div class="timeline-content">
          <p>1.新增可自助找回账号功能(太多谦友找不到自己的账号啦).</p>
          <p>2.最近太忙了,没太多时间更新网站,聊天室和小程序月底再上线吧.</p>
        </div>
      </li>
     <li class="timeline-item">
        <div class="timeline-dot"></div>
        <div class="timeline-date">2024 年 10 月 24 日</div>
        <div class="timeline-content">
          <p>1.新增可进入朋友圈详情删除自己的评论.</p>
          <p>2.新增可还原个人中心封面图片,可在<a href="/setup.php" style="color:blue">设置中心</a>进行还原.</p>
          <p>2.应各位谦友的要求，小程序版本即将上线，在审核啦，网站打开不方便 小程序下拉即可访问！.</p>
        </div>
      </li>
     <li class="timeline-item">
        <div class="timeline-dot"></div>
        <div class="timeline-date">2024 年 10 月 23 日</div>
        <div class="timeline-content">
          <p>1.修复部分用户无法登录.</p>
          <p>2.限制注册用户名可用中文和特殊符号(防止中英文混淆导致无法登录).</p>
        </div>
      </li>
     <li class="timeline-item">
        <div class="timeline-dot"></div>
        <div class="timeline-date">2024 年 10 月 21 日</div>
        <div class="timeline-content">
          <p>1.修复消息盒子不显示内容.</p>
          <p>2.谦友太强大了,网站媒体资源访问超过120W次啦.</p>
        </div>
      </li>
     <li class="timeline-item">
        <div class="timeline-dot"></div>
        <div class="timeline-date">2024 年 10 月 20 日</div>
        <div class="timeline-content">
          <p>1.优化暗黑模式下字体看不到问题.</p>
          <p>2.优化发布朋友圈获取定位问题.</p>
          <p>3.修复消息盒子中图片头像不显示问题.</p>
          <p>4.修复封面上传图片不显示问题.</p>
          <p>5.今日流量费用成本54.5</p>
        </div>
      </li>
      <li class="timeline-item">
        <div class="timeline-dot"></div>
        <div class="timeline-date">2024 年 10 月 19 日</div>
        <div class="timeline-content">
          <p>1.日常维护继续优化访问速度</p>
          <p>2.新增发布音乐功能，只需要输入歌名即可自动搜索.</p>
          <p>3.新增网站更新日志记录.</p>
          <p>4.今日流量费用成本59.5元.</p>
        </div>
      </li>
      <li class="timeline-item">
        <div class="timeline-dot"></div>
        <div class="timeline-date">2024 年 10 月 18 日</div>
        <div class="timeline-content">
          <p>1.因运营成本问题网站更换图片和视频储存接口.</p>
          <p>2.修复网站登录后进入卡顿BUG.</p>
          <p>3.优化上传图片和视频速度.</p>
        </div>
      </li>
    </ul>
    
  </div>
        </div>
        
        <div style="background: #fff;margin: 10px 0;padding: 20px;font-size: 14px;">
    <h4>问：为什么网站运营会有成本？</h4><br>
    <p>答：为了大家上传和观看图片/视频流畅所以选择了把图片和视频储存到了腾讯云的云储存，每次访问一次图片和视频腾讯云就会进行收费计算，根据每天的访问量进行统计收费，目前来看成本在0.5元/GB，而且不包括每个月的服务器费用，我也只是普通的一个打工人谦友，所以尽可能给大家服务做到最好，当一天支出超出我的能力范围的时候可能我要靠大家众筹或者我会对接一些平台的广告来维护谦友圈了，也希望各位谦友能理解，感谢支持！</p>
    <div>
        <!-- 页面载体 end -->
      
      



 
<!--版权-->
<div class="sh-copyright">
    <span class="sh-copyright-banquan" id="sh-copyright-banquan"><?php echo $copyright;?></span>&nbsp;
    <?php 
if ($beian != "") {
	echo "<span class=\"sh-copyright-banquan\">" . html_entity_decode($beian) . "</span>";
}
?></div>
<!--版权-->


    </div>

    <script type="text/javascript" src="./assets/js/repass.js?v=<?php echo $resversion;?>"></script>
    <script type="text/javascript" src="./assets/mesg/dist/js/sh-noytf.js?v=<?php echo $resversion;?>"></script><!--引入弹窗对话框-->
    <?php echo "<script type=\"text/javascript\">" . $filtercujs . "</script>";?></body>
</html>