<?php
//decode by nige112
$user_zh = $_COOKIE["username"];
$user_passid = $_COOKIE["passid"];
if ($user_zh == "" || $user_passid == "") {
    exit("请先登录!");
}

include "../config.php";
$conn = new mysqli($servername, $username, $password, $dbname);
if ($conn->connect_error) {
    exit("连接失败: ". $conn->connect_error);
}

// Fetch user info
$user_zhsg = addslashes(htmlspecialchars($user_zh));
$sqls = "SELECT * FROM user WHERE username='{$user_zhsg}'";
$data_results = mysqli_query($conn, $sqls);
$data2s_row = mysqli_fetch_array($data_results);
$user_zh = $data2s_row["username"];
$user_name = $data2s_row["name"];
$user_img = $data2s_row["img"];
$user_url = $data2s_row["url"];

$sql = "SELECT * FROM admin";
$result = $conn->query($sql);
while ($row = $result->fetch_assoc()) {
    $glyzhuser = $row["username"];
}

$data_result = mysqli_query($conn, "SELECT * FROM user WHERE username='{$user_zh}'");
$data2_row = mysqli_fetch_array($data_result);
$zjzhq = $data2_row["username"];
$zjnc = $data2_row["name"];
$zjimg = $data2_row["img"];
$zjhomeimg = $data2_row["homeimg"];
$zjsign = $data2_row["sign"];
$zjemail = $data2_row["email"];
$zjurl = $data2_row["url"];
$zjfbqx = $data2_row["essqx"];
$zjemtz = $data2_row["esseam"];
$passid = $data2_row["passid"];
$zid = $data2_row["id"];
$zjpassword = $data2_row["password"];

if ($user_passid!= $passid) {
    setcookie("username", "", time() - 3600, "/");
    setcookie("passid", "", time() - 3600, "/");
    exit("账号信息异常,请重新登录!");
}

// 处理上传的文件
$file = $_FILES["file"];
$allowedExts = ["gif", "jpeg", "jpg", "png", "webp"];
$temp = explode(".", $_FILES["file"]["name"]);
$extension = end($temp);

if ($extension == "") {
    exit("未选择图片!");
}
if (!in_array($extension, $allowedExts)) {
    exit("文件类型错误,请上传图片!");
}
if ($file["size"] > 2 * 1024 * 1024) {
     $oldLocalPath = "../user". $zjhomeimg;
     if (file_exists($oldLocalPath)) {
            unlink($oldLocalPath);
        }
    exit("请上传 2MB 以内的图片!");
}

// 腾讯云配置参数
require '../tencent-php/vendor/autoload.php'; // 包含腾讯云 SDK 自动加载文件
use Qcloud\Cos\Client;
use Qcloud\Cos\Exception\ServiceResponseException;

$secretId = 'AKIDtb8VfbKrxqWzHzXv65Nl5EAtQ3YxFNbh';
$secretKey = 'HXeZLwMgornYBg2BPSp23tcBdt9HVfpr';
$bucketName = '0717-1314972303';
$region = 'ap-guangzhou';
$operatorurl = 'https://0717-1314972303.cos.ap-guangzhou.myqcloud.com';

// 初始化腾讯云 COS 客户端
$cosClient = new Client([
    'region' => $region,
    'credentials' => [
        'secretId' => $secretId,
        'secretKey' => $secretKey,
    ]
]);

// 上传图片到腾讯云 COS
try {
    $cow = "/cover/";
    $imageName = mt_rand(). str_replace(".", "", microtime(true)). substr(md5($zjzhq), 0, 12). $file["name"];
    $imagePath = $file["tmp_name"];

    // 上传文件到腾讯云 COS
    $result = $cosClient->putObject([
        'Bucket' => $bucketName,
        'Key' => $cow. $imageName,
        'Body' => fopen($imagePath, 'rb'),
    ]);

    // 获取文件 URL
    $userimg = $operatorurl. $cow. $imageName;

    // Remove old image if necessary
    if (strstr($zjhomeimg, "/cover")) {
        $oldLocalPath = "../user". $zjhomeimg;
        if (file_exists($oldLocalPath)) {
            unlink($oldLocalPath);
        }
    }

    // Update user image in database
    $sql = "UPDATE user SET homeimg='{$userimg}' WHERE username='{$zjzhq}'";
    if (!$conn->query($sql)) {
        die('数据库更新失败: '. $conn->error);
    }

    exit("修改封面成功");

} catch (ServiceResponseException $e) {
    exit("上传失败: ". $e->getMessage());
}

$conn->close();
?>
