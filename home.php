<?php

//decode by nige112
if ($_SERVER["REQUEST_METHOD"] === "POST") {
	$arr = [["code" => "201", "msg" => "非法请求"]];
	exit(json_encode($arr, JSON_UNESCAPED_UNICODE));
}
$iteace = "0";
if (is_file("./config.php")) {
	require "./config.php";
} else {
	exit("未检测到配置文件：<span style=\"color: red;\">config.php</span>，若您是首次使用请先进行安装,删除文件夹 <span style=\"color: red;\">[install/]</span> 下的 <span style=\"color: red;\">[ins.bak]</span> 文件后进行安装。");
}
require "./api/wz.php";



// 添加wz函数用于显示帖子内容
function wz($cid, $ptptime, $ptptext, $ptpimag, $ptpvideo, $ptpvideofm, $ptpmusic, $ptpuser, $ptpname, $ptplx, $ptpdw, $ptpsd, $ptpys, $givenDate, $givenDates, $month, $day, $wzsdbs, $shougbsx) {
    // 根据帖子类型生成不同的HTML内容
    if ($ptplx == "img") {
        $imgar = explode("(+@+)", $ptpimag);
        $coun = count($imgar);
        if ($coun == 1) {
            echo "<div class=\"sh-content\">
                <div class=\"sh-content-left\">
                    <img src=\"{$ptpimag}\" alt=\"图片内容\">
                </div>
                <div class=\"sh-content-right\">
                    <div class=\"sh-content-right-head\">
                        <div class=\"sh-content-right-head-title\">
                            <p>{$ptpname}</p>
                        </div>
                        <div>{$ptptext}</div>
                    </div>
                    <div class=\"sh-content-right-time\">
                        <div class=\"sh-content-right-time-left\"><span>{$day} {$month}</span></div>
                    </div>
                </div>
            </div>";
        } else {
            echo "<div class=\"sh-content\">
                <div class=\"sh-content-left\">
                    <img src=\"{$ptpimag}\" alt=\"图片内容\">
                </div>
                <div class=\"sh-content-right\">
                    <div class=\"sh-content-right-head\">
                        <div class=\"sh-content-right-head-title\">
                            <p>{$ptpname}</p>
                        </div>
                        <div>{$ptptext}</div>
                        <div class=\"sh-content-right-img\">
                            <div class=\"image-gallery\">多图内容 ({$coun}张)</div>
                        </div>
                    </div>
                    <div class=\"sh-content-right-time\">
                        <div class=\"sh-content-right-time-left\"><span>{$day} {$month}</span></div>
                    </div>
                </div>
            </div>";
        }
    } elseif ($ptplx == "video") {
        echo "<div class=\"sh-content\">
            <div class=\"sh-content-left\">
                <img src=\"{$ptpvideofm}\" alt=\"视频封面\">
            </div>
            <div class=\"sh-content-right\">
                <div class=\"sh-content-right-head\">
                    <div class=\"sh-content-right-head-title\">
                        <p>{$ptpname}</p>
                    </div>
                    <div>{$ptptext}</div>
                    <div class=\"sh-content-right-video\">视频内容</div>
                </div>
                <div class=\"sh-content-right-time\">
                    <div class=\"sh-content-right-time-left\"><span>{$day} {$month}</span></div>
                </div>
            </div>
        </div>";
    } elseif ($ptplx == "music") {
        echo "<div class=\"sh-content\">
            <div class=\"sh-content-left\">
                <i class=\"ri-music-fill\"></i>
            </div>
            <div class=\"sh-content-right\">
                <div class=\"sh-content-right-head\">
                    <div class=\"sh-content-right-head-title\">
                        <p>{$ptpname}</p>
                    </div>
                    <div>{$ptptext}</div>
                    <div class=\"sh-content-right-music\">音乐内容</div>
                </div>
                <div class=\"sh-content-right-time\">
                    <div class=\"sh-content-right-time-left\"><span>{$day} {$month}</span></div>
                </div>
            </div>
        </div>";
    } else {
        echo "<div class=\"sh-content\">
            <div class=\"sh-content-left\">
                <img src=\"./assets/img/default-avatar.png\" alt=\"默认头像\">
            </div>
            <div class=\"sh-content-right\">
                <div class=\"sh-content-right-head\">
                    <div class=\"sh-content-right-head-title\">
                        <p>{$ptpname}</p>
                    </div>
                    <div>{$ptptext}</div>
                </div>
                <div class=\"sh-content-right-time\">
                    <div class=\"sh-content-right-time-left\"><span>{$day} {$month}</span></div>
                </div>
            </div>
        </div>";
    }
}

$sql = "select * from user where username= '{$user_zh}'";
$result = mysqli_query($conn, $sql);
if (mysqli_num_rows($result) > 0) {
	while ($row = mysqli_fetch_assoc($result)) {
		$zjnc = $row["name"];
		$zjimg = $row["img"];
		$zjhomeimgy = $row["homeimg"];
		$zjsign = $row["sign"];
	}
} else {
	header("Location:./index.php");
}
if ($zjhomeimgy == -1) {
	$zjhomeimgy = $homimg;
}
?><!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>个人中心 - <?php echo $name;?></title>
    <!--为搜索引擎定义关键词-->
    <meta name="keywords" content="<?php echo $name;?>">
    <!--为网页定义描述内容 用于告诉搜索引擎，你网站的主要内容-->
    <meta name="description" content="<?php echo $name . "," . $subtitle;?>">
    <meta name="author" content="<?php echo $name;?>">
    <meta name="copyright" content="<?php echo $name;?>">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta http-equiv="cache-control" content="no-cache">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0,minimum-scale=1.0, user-scalable=no minimal-ui">
    <link rel="apple-touch-icon" sizes="57x57" href="<?php echo $icon;?>" /><!-- Standard iPhone -->
    <link rel="apple-touch-icon" sizes="114x114" href="<?php echo $icon;?>" /><!-- Retina iPhone -->
    <link rel="apple-touch-icon" sizes="72x72" href="<?php echo $icon;?>" /><!-- Standard iPad -->
    <link rel="apple-touch-icon" sizes="144x144" href="<?php echo $icon;?>" /><!-- Retina iPad -->
    <link rel="icon" href="<?php echo $icon;?>" type="image/x-icon" />
    <meta name="robots" content="index,follow">
    <meta name="format-detection" content="telephone=no">
    <meta http-equiv="x-rim-auto-match" content="none">
    <?php 
if ($rosdomain == 1) {
	?><meta name="referrer" content="no-referrer"><?php 
}
?>    <link rel="stylesheet" href="<?php echo $iconurl_url;?>">
    <link rel="stylesheet" type="text/css" href="./assets/css/style.css?v=<?php echo $resversion;?>">
    <link rel="stylesheet" type="text/css" href="./assets/css/footer-menu.css?v=<?php echo $resversion;?>">
    <link rel="stylesheet" type="text/css" href="./assets/mesg/dist/css/style.css?v=<?php echo $resversion;?>"><!--引入弹窗对话框-->
    <style>
        /* iOS风格个人页面 */
        .profile-container {
            padding-bottom: 20px;
        }
        
        /* 改善头部区域适配，降低高度 */
        .sh-main-head {
            height: auto;
            min-height: 40px;
            position: relative;
            z-index: 10;
            background-color: transparent;
            padding: 5px 0;
        }
        
        /* 背景图片和头像区域 */
        .profile-header {
            position: relative;
            height: 150px; /* 降低高度 */
            background-size: cover;
            background-position: center;
            display: flex;
            align-items: flex-end;
            padding: 20px;
            overflow: hidden;
            border-radius: 0 0 25px 25px; /* 圆角底部 */
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
            margin-top: -10px; /* 上移覆盖部分头部 */
            transition: all 0.4s cubic-bezier(0.215, 0.61, 0.355, 1);
        }
        
        .profile-header:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(to bottom, rgba(0,0,0,0.1), rgba(0,0,0,0.7));
            z-index: 1;
        }
        
        .profile-header-content {
            position: relative;
            z-index: 2;
            width: 100%;
            display: flex;
            align-items: center;
            animation: fadeIn 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(15px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .profile-avatar {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            border: 3px solid rgba(255,255,255,0.9);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            overflow: hidden;
            margin-right: 15px;
            flex-shrink: 0;
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }
        
        .profile-avatar:active {
            transform: scale(0.95);
        }
        
        .profile-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }
        
        .profile-avatar:hover img {
            transform: scale(1.1);
        }
        
        .profile-info {
            color: white;
            text-shadow: 0 1px 3px rgba(0,0,0,0.3);
        }
        
        .profile-name {
            font-size: 22px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .profile-bio {
            font-size: 14px;
            opacity: 0.9;
            max-width: 200px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .edit-cover-btn {
            position: absolute;
            bottom: 20px;
            right: 20px;
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: rgba(255,255,255,0.25);
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            z-index: 2;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            border: 1px solid rgba(255,255,255,0.3);
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }
        
        .edit-cover-btn:active {
            transform: scale(0.9) rotate(10deg);
            background-color: rgba(255,255,255,0.4);
        }
        
        /* 统计信息区域 */
        .profile-stats {
            display: flex;
            justify-content: space-around;
            padding: 15px 10px;
            background-color: white;
            border-radius: 20px;
            margin: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
            transform: translateY(0);
            animation: slideUp 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
            animation-delay: 0.1s;
            opacity: 0;
        }
        
        @keyframes slideUp {
            from { transform: translateY(20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        
        .dark-theme .profile-stats {
            background-color: #272727;
            box-shadow: 0 5px 15px rgba(0,0,0,0.15);
        }
        
        .stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            padding: 5px 12px;
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }
        
        .stat-item:active {
            transform: scale(0.95);
        }
        
        .stat-item:not(:last-child):after {
            content: '';
            position: absolute;
            right: -5px;
            top: 15%;
            height: 70%;
            width: 1px;
            background-color: rgba(0,0,0,0.1);
        }
        
        .dark-theme .stat-item:not(:last-child):after {
            background-color: rgba(255,255,255,0.1);
        }
        
        .stat-value {
            font-size: 20px;
            font-weight: bold;
            color: #1e88e5;
            margin-bottom: 5px;
            position: relative;
        }
        
        .stat-value:before {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 2px;
            background-color: #1e88e5;
            transition: width 0.3s ease;
        }
        
        .stat-item:hover .stat-value:before {
            width: 80%;
        }
        
        .dark-theme .stat-value {
            color: #42a5f5;
        }
        
        .stat-label {
            font-size: 12px;
            color: #999;
            font-weight: 500;
        }
        
        /* 提示区域 */
        .profile-notice {
            margin: 20px 15px;
            padding: 18px;
            background: linear-gradient(135deg, #1e88e5, #42a5f5);
            border-radius: 18px;
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 8px 25px rgba(30, 136, 229, 0.25);
            position: relative;
            overflow: hidden;
            animation: slideUp 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
            animation-delay: 0.2s;
            opacity: 0;
            transform: translateY(0);
        }
        
        .profile-notice:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNTAiIGhlaWdodD0iMTUwIj48Y2lyY2xlIGN4PSIxMCIgY3k9IjEwIiByPSIyIiBmaWxsPSIjZmZmIiBmaWxsLW9wYWNpdHk9IjAuMSIvPjwvc3ZnPg==');
            opacity: 0.2;
            z-index: 0;
        }
        
        .profile-notice-content {
            position: relative;
            z-index: 1;
        }
        
        .profile-notice-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 5px;
            display: flex;
            align-items: center;
        }
        
        .profile-notice-title i {
            margin-right: 8px;
            font-size: 18px;
        }
        
        .profile-notice-desc {
            font-size: 12px;
            opacity: 0.9;
        }
        
        .profile-notice-btn {
            background-color: rgba(255,255,255,0.25);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 13px;
            font-weight: bold;
            position: relative;
            z-index: 1;
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.3);
        }
        
        .profile-notice-btn:active {
            background-color: rgba(255,255,255,0.4);
            transform: scale(0.95) translateY(2px);
            box-shadow: 0 1px 5px rgba(0,0,0,0.1);
        }
        
        /* 功能菜单区域 */
        .profile-menu {
            background-color: white;
            border-radius: 20px;
            margin: 15px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0,0,0,0.06);
            animation: slideUp 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
            animation-delay: 0.3s;
            opacity: 0;
            transform: translateY(0);
        }
        
        .dark-theme .profile-menu {
            background-color: #272727;
            box-shadow: 0 5px 15px rgba(0,0,0,0.15);
        }
        
        .menu-section-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            padding: 15px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
        }
        
        .menu-section-title i {
            margin-right: 8px;
            font-size: 18px;
            color: #1e88e5;
        }
        
        .dark-theme .menu-section-title {
            color: #eee;
            border-bottom: 1px solid #333;
        }
        
        .menu-item {
            display: flex;
            align-items: center;
            padding: 15px;
            border-bottom: 1px solid #f0f0f0;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .menu-item:after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background-color: rgba(30, 136, 229, 0.05);
            transition: all 0.5s ease;
            z-index: 0;
        }
        
        .menu-item:active:after {
            left: 0;
        }
        
        .dark-theme .menu-item {
            border-bottom: 1px solid #333;
        }
        
        .menu-item:last-child {
            border-bottom: none;
        }
        
        .menu-item:active {
            transform: translateX(3px);
        }
        
        .dark-theme .menu-item:active {
            background-color: #333;
        }
        
        .menu-icon {
            width: 40px;
            height: 40px;
            border-radius: 12px;
            background-color: rgba(30, 136, 229, 0.1);
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 15px;
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            z-index: 1;
        }
        
        .menu-item:active .menu-icon {
            transform: scale(0.9) rotate(-5deg);
            background-color: rgba(30, 136, 229, 0.2);
        }
        
        .menu-icon i {
            font-size: 20px;
            color: #1e88e5;
        }
        
        .menu-content {
            flex: 1;
            position: relative;
            z-index: 1;
        }
        
        .menu-label {
            font-size: 16px;
            color: #333;
            margin-bottom: 4px;
        }
        
        .dark-theme .menu-label {
            color: #eee;
        }
        
        .menu-desc {
            font-size: 12px;
            color: #999;
        }
        
        .menu-arrow {
            color: #ccc;
            margin-left: 10px;
            transition: transform 0.3s ease;
            position: relative;
            z-index: 1;
        }
        
        .menu-item:active .menu-arrow {
            transform: translateX(5px);
            color: #1e88e5;
        }
        
        /* 内容区域的样式调整 */
        .sh-homecontent {
            margin-top: 20px;
            padding: 0 10px;
        }
        
        .sh-homecontent-timed {
            padding: 0 15px;
            margin: 20px 0 10px;
            color: #1e88e5;
            font-size: 16px;
            font-weight: 600;
            display: flex;
            align-items: center;
        }
        
        .sh-homecontent-timed:before {
            content: '';
            height: 18px;
            width: 4px;
            background: #1e88e5;
            display: inline-block;
            margin-right: 8px;
            border-radius: 2px;
        }
        
        .dark-theme .sh-homecontent-timed {
            color: #42a5f5;
        }
        
        .dark-theme .sh-homecontent-timed:before {
            background: #42a5f5;
        }
        
        /* 底部菜单徽章位置调整 */
        .footer-menu .notification-dot {
            position: absolute;
            top: 5px;
            right: 25%;
            width: 8px;
            height: 8px;
            background-color: #ff3b30;
            border-radius: 50%;
            animation: pulse 1.5s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.2); opacity: 0.8; }
            100% { transform: scale(1); opacity: 1; }
        }
        
        /* 调整图标大小 */
        .footer-menu-item i {
            font-size: 24px;
        }
        
        /* 底部填充区 */
        .footer-padding {
            height: 70px;
        }
        
        /* 适应不同尺寸 */
        @media (max-width: 340px) {
            .profile-header {
                height: 140px;
            }
            
            .profile-avatar {
                width: 60px;
                height: 60px;
            }
            
            .profile-name {
                font-size: 18px;
            }
            
            .profile-bio {
                font-size: 12px;
            }
        }
        
        /* 标签切换样式 */
        .profile-tabs {
            display: flex;
            background-color: white;
            border-radius: 15px;
            margin: 15px;
            padding: 5px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            position: relative;
            z-index: 2;
        }
        
        .dark-theme .profile-tabs {
            background-color: #272727;
        }
        
        .profile-tab {
            flex: 1;
            text-align: center;
            padding: 12px 0;
            border-radius: 10px;
            font-size: 14px;
            font-weight: 500;
            color: #666;
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
        }
        
        .dark-theme .profile-tab {
            color: #bbb;
        }
        
        .profile-tab i {
            font-size: 20px;
            margin-bottom: 5px;
        }
        
        .profile-tab.active {
            background-color: #1e88e5;
            color: white;
            box-shadow: 0 5px 15px rgba(30, 136, 229, 0.2);
        }
        
        .profile-tab:active {
            transform: scale(0.95);
        }
        
        .profile-content {
            display: none;
            animation: fadeIn 0.5s ease-out;
        }
        
        .profile-content.active {
            display: block;
        }
        
        /* 空内容状态 */
        .empty-content {
            text-align: center;
            padding: 40px 20px;
            color: #999;
        }
        
        .empty-icon {
            font-size: 50px;
            margin-bottom: 15px;
            color: #ddd;
        }
        
        .dark-theme .empty-icon {
            color: #444;
        }
        
        .empty-text {
            font-size: 16px;
            margin-bottom: 15px;
        }
        
        .empty-action {
            display: inline-block;
            background-color: #1e88e5;
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            box-shadow: 0 5px 15px rgba(30, 136, 229, 0.2);
            transition: all 0.3s ease;
        }
        
        .empty-action:active {
            transform: scale(0.95);
            box-shadow: 0 3px 10px rgba(30, 136, 229, 0.1);
        }
    </style>
    
    <!-- 引入更丰富的图标库 -->
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <?php echo $scfontzt;?>    <?php 
$folderPath = "./user/bobg/";
if (!file_exists($folderPath)) {
	mkdir($folderPath, 511, true);
}
if (file_exists("./user/bobg/bobg.jpg")) {
	?><!--背景图--><style>body{background-image: url(./user/bobg/bobg.jpg);}</style><?php 
} elseif (file_exists("./user/bobg/bobg.png")) {
	?><!--背景图--><style>body{background-image: url(./user/bobg/bobg.png);}</style><?php 
} elseif (file_exists("./user/bobg/bobg.jpeg")) {
	?><!--背景图--><style>body{background-image: url(./user/bobg/bobg.jpeg);}</style><?php 
} elseif (file_exists("./user/bobg/bobg.gif")) {
	?><!--背景图--><style>body{background-image: url(./user/bobg/bobg.gif);}</style><?php 
}
?>    <?php echo "<style>" . $filtercucss . "</style>";?></head>
<body class="<?php 
if (isset($_COOKIE["dark_theme"])) {
	if ($_COOKIE["dark_theme"] == "dark-theme") {
		echo "dark-theme";
	}
}
echo "\">
    ";
if ($pagepass != "") {
	if (isset($_COOKIE["pagepass"])) {
		if ($_COOKIE["pagepass"] != md5(md5($pagepass))) {
			include "./site/pagepass.php";
		}
	} else {
		include "./site/pagepass.php";
	}
}
?>    <div class="centent">
        <div class="sh-main">
           
            <!-- iOS风格个人页面 -->
            <div class="profile-container">
                <!-- 背景图片和头像区域 -->
                <div class="profile-header" style="background-image: url(<?php echo $zjhomeimgy;?>)">
                    <div class="edit-cover-btn" onclick="reckq()">
                        <i class="ri-image-add-line"></i>
                    </div>
                    <div class="profile-header-content">
                        <div class="profile-avatar">
                            <img src="<?php echo $zjimg;?>" alt="头像">
                        </div>
                        <div class="profile-info">
                            <div class="profile-name"><?php echo $zjnc;?></div>
                            <div class="profile-bio"><?php echo $zjsign;?></div>
                        </div>
                    </div>
                </div>
                
                <!-- 个人数据统计区域 -->
                <div class="profile-stats">
                    <?php
                    // 获取发帖数量
                    $sql_posts = "SELECT COUNT(*) as post_count FROM essay WHERE ptpuser = '{$user_zh}'";
                    $result_posts = $conn->query($sql_posts);
                    $post_count = 0;
                    if ($result_posts && $row_posts = $result_posts->fetch_assoc()) {
                        $post_count = $row_posts['post_count'];
                    }
                    
                    // 获取粉丝数量
                    $sql_followers = "SELECT COUNT(*) as follower_count FROM followers WHERE followed_user = '{$user_zh}'";
                    $result_followers = $conn->query($sql_followers);
                    $follower_count = 0;
                    if ($result_followers && $row_followers = $result_followers->fetch_assoc()) {
                        $follower_count = $row_followers['follower_count'];
                    }
                    
                    // 获取关注数量
                    $sql_following = "SELECT COUNT(*) as following_count FROM followers WHERE follower_user = '{$user_zh}'";
                    $result_following = $conn->query($sql_following);
                    $following_count = 0;
                    if ($result_following && $row_following = $result_following->fetch_assoc()) {
                        $following_count = $row_following['following_count'];
                    }
                    ?>
                    <div class="stat-item">
                        <div class="stat-value"><?php echo $post_count; ?></div>
                        <div class="stat-label">帖子</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value"><?php echo $follower_count; ?></div>
                        <div class="stat-label">粉丝</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value"><?php echo $following_count; ?></div>
                        <div class="stat-label">关注</div>
                    </div>
                </div>
                
                <!-- 提示区域 -->
                <div class="profile-notice">
                    <div class="profile-notice-content">
                        <div class="profile-notice-title"><i class="ri-calendar-check-fill"></i>每日签到</div>
                        <div class="profile-notice-desc">签到获取积分和奖励</div>
                    </div>
                    <div class="profile-notice-btn" onclick="location.href='./sign.php'">去签到</div>
                </div>
                
                <!-- 功能菜单区域 -->
                <div class="profile-menu">
                    <div class="menu-section-title"><i class="ri-user-settings-fill"></i>账号管理</div>
                    <div class="menu-item" onclick="location.href='./setup.php'">
                        <div class="menu-icon">
                            <i class="ri-edit-line"></i>
                        </div>
                        <div class="menu-content">
                            <div class="menu-label">编辑资料</div>
                            <div class="menu-desc">修改你的个人信息</div>
                        </div>
                        <i class="ri-arrow-right-s-line menu-arrow"></i>
                    </div>
                    <div class="menu-item" onclick="toggleDarkMode()">
                        <div class="menu-icon">
                            <i class="ri-contrast-2-line"></i>
                        </div>
                        <div class="menu-content">
                            <div class="menu-label">主题切换</div>
                            <div class="menu-desc">切换明亮和暗黑模式</div>
                        </div>
                        <i class="ri-arrow-right-s-line menu-arrow"></i>
                    </div>
                                </div>
                
                <!-- 内容标签切换 -->
                <div class="profile-tabs">
                    <div class="profile-tab active" data-tab="my-posts">
                        <i class="ri-file-list-3-line"></i>
                        <span>我的帖子</span>
                    </div>
                    <div class="profile-tab" data-tab="my-favorites">
                        <i class="ri-star-line"></i>
                        <span>我的收藏</span>
                    </div>
                </div>
            </div>
            
            <!-- 内容区域 -->
            <div class="profile-content active" id="my-posts-content">
                <div class="sh-homecontent" id="sh-nrbk">
                <?php 
                require "./config.php";
                $conn = new mysqli($servername, $username, $password, $dbname);
                if ($conn->connect_error) {
                    exit("连接失败: " . $conn->connect_error);
                }
                include "./api/topeshome.php";
                $prevYear = $prevMonth = $prevDay = "";
                $timebss = "";
                $wzsl = 0;
                if ($user_zh == $glyadmin) {
                    $query = "SELECT * FROM essay WHERE ptpaud='1'";
                } else {
                    $query = "SELECT * FROM essay WHERE ptpaud='1' and ptpuser = '{$user_zh}'";
                }
                $result = mysqli_query($conn, $query);
                $ptptime_values = [];
                while ($row = mysqli_fetch_assoc($result)) {
                    $ptptime = date("Y", strtotime($row["ptptime"])) . date("m", strtotime($row["ptptime"])) . date("d", strtotime($row["ptptime"])) . date("H", strtotime($row["ptptime"])) . date("i", strtotime($row["ptptime"])) . date("s", strtotime($row["ptptime"]));
                    if (strpos($ptptime, "-") !== false) {
                        $timestamp = strtotime($ptptime);
                    } else {
                        $timestamp = DateTime::createFromFormat("YmdHis", $ptptime)->getTimestamp();
                    }
                    $ptptime_values[$timestamp] = $row;
                }
                krsort($ptptime_values);
                foreach ($ptptime_values as $row) {
                    $wzsl = $wzsl + 1;
                    if ($essgs < $wzsl) {
                        $wzsl = $wzsl - 1;
                        break;
                    }
                    $ptpuser = $row["ptpuser"];
                    $ptpimg = $row["ptpimg"];
                    $ptpname = $row["ptpname"];
                    $ptptext = $row["ptptext"];
                    $ptpimag = $row["ptpimag"];
                    $ptpvideo = $row["ptpvideo"];
                    $ptpmusic = $row["ptpmusic"];
                    $ptplx = $row["ptplx"];
                    $ptpdw = $row["ptpdw"];
                    $ptptime = $row["ptptime"];
                    $ptpgg = $row["ptpgg"];
                    $ptpggurl = $row["ptpggurl"];
                    $ptpys = $row["ptpys"];
                    $commauth = $row["commauth"];
                    $ptpaud = $row["ptpaud"];
                    $ptpip = $row["ip"];
                    $cid = $row["cid"];
                    $wid = $row["id"];
                    $partssp = explode("|", $ptpvideo);
                    $ptpvideo = $partssp[0];
                    $ptpvideofm = $partssp[1];
                    $year = date("Y", strtotime($ptptime));
                    $month = date("m", strtotime($ptptime));
                    $day = date("d", strtotime($ptptime));
                    $hour = date("H", strtotime($ptptime));
                    $minute = date("i", strtotime($ptptime));
                    $second = date("s", strtotime($ptptime));
                    $givenDates = $year . "-" . $month . "-" . $day;
                    $given_time = $year . $month . $day;
                    if ($given_time == date("Ymd")) {
                        $day = "今天";
                        $month = "";
                    } elseif ($given_time == date("Ymd", strtotime("-1 day"))) {
                        $day = "昨天";
                        $month = "";
                    } else {
                        $day = $day;
                        $month = $month . "月";
                    }
                    if ($year != $prevYear) {
                        if (date("Y") != $year) {
                            echo "<h2 class=\"sh-homecontent-timed\">" . $year . "<span class=\"sh-homecontent-timed-n\">年</span></h2>";
                        }
                        $prevYear = $year;
                    }
                    if ($ptpys == 0) {
                        $wzsdbs = "<i class=\"iconfont icon-suoding sh-homecontent-wzsbs\" title=\"文章已设为隐私\"></i>";
                    } else {
                        if ($ptpaud == 0) {
                            $wzsdbs = "<i class=\"iconfont icon-suoding sh-homecontent-wzsbs\" style=\"color:var(--protext)\" title=\"文章未通过审核\"></i>";
                        } else {
                            $wzsdbs = "";
                        }
                    }
                    if ($ptplx == "img") {
                        $imgar = explode("(+@+)", $ptpimag);
                        $coun = count($imgar);
                        if ($coun == 1) {
                            $wzbank = "<div class=\"sh-homecontent-lie\">
                            <!-- 左边 -->
                            <div class=\"sh-homecontent-left\">
                                <!-- 日期 -->
                                <div class=\"sh-homecontent-left-time\" lang=\"year-" . $givenDates . "\" style=\"" . $shougbsx . "\">
                                    <span class=\"homecontent-left-time-h\">" . $day . "</span>
                                    <span class=\"homecontent-left-time-y\">" . $month . "</span>
                                </div>
                                <!-- 地址 -->
                                <div class=\"sh-homecontent-left-time-dw\">" . $ptpdw . "</div>
                            </div>
                            <!-- 右边内容框架 -->
                            <div class=\"sh-homecontent-right-wk\">

                                <!-- 右边内容主体 -->
                                <div class=\"sh-homecontent-right-lie\" onclick=\"window.location.href = './view.php?cid=" . $cid . "';\">
                                    <!-- 图片 -->
                                    <div class=\"homecontent-right-tw\">
                                        <!-- 图片 -->
                                        <div class=\"homecontent-right-tw-img\" style=\"grid-template-columns: 1fr;grid-template-rows: 1fr;\">
                                            <div class=\"homecontent-right-tw-img-wk\"><img src=\"./assets/img/thumbnail.svg\" data-src=\"" . $imgar[0] . "\" alt=\"\"></div>
                                            " . $wzsdbs . "
                                        </div>
                                    </div>
                                    <!-- 文字内容 -->
                                    <div class=\"homecontent-right-nr\">
                                        <div class=\"homecontent-right-nr-text\">" . $ptptext . "</div>
                                        <p class=\"homecontent-right-nr-tus\">共" . $coun . "张</p>
                                    </div>
                                </div>
                            </div>
                        </div>";
                        } elseif ($coun == 2) {
                            $wzbank = "<div class=\"sh-homecontent-lie\">
                            <!-- 左边 -->
                            <div class=\"sh-homecontent-left\">
                                <!-- 日期 -->
                                <div class=\"sh-homecontent-left-time\" lang=\"year-" . $givenDates . "\" style=\"" . $shougbsx . "\">
                                    <span class=\"homecontent-left-time-h\">" . $day . "</span>
                                    <span class=\"homecontent-left-time-y\">" . $month . "</span>
                                </div>
                                <!-- 地址 -->
                                <div class=\"sh-homecontent-left-time-dw\">" . $ptpdw . "</div>
                            </div>
                            <!-- 右边内容框架 -->
                            <div class=\"sh-homecontent-right-wk\">

                                <!-- 右边内容主体 -->
                                <div class=\"sh-homecontent-right-lie\" onclick=\"window.location.href = './view.php?cid=" . $cid . "';\">
                                    <!-- 图片 -->
                                    <div class=\"homecontent-right-tw\">
                                        <!-- 图片 -->
                                        <div class=\"homecontent-right-tw-img\">
                                            <div class=\"homecontent-right-tw-img-wk\" style=\"grid-row: 1 / 3;\"><img src=\"./assets/img/thumbnail.svg\" data-src=\"" . $imgar[0] . "\" alt=\"\"></div>
                                            <div class=\"homecontent-right-tw-img-wk\" style=\"grid-row: 1 / 3;\"><img src=\"./assets/img/thumbnail.svg\" data-src=\"" . $imgar[1] . "\" alt=\"\"></div>
                                            " . $wzsdbs . "
                                        </div>
                                    </div>
                                    <!-- 文字内容 -->
                                    <div class=\"homecontent-right-nr\">
                                        <div class=\"homecontent-right-nr-text\">" . $ptptext . "</div>
                                        <p class=\"homecontent-right-nr-tus\">共" . $coun . "张</p>
                                    </div>
                                </div>
                            </div>
                        </div>";
                        } elseif ($coun == 3) {
                            $wzbank = "<div class=\"sh-homecontent-lie\">
                            <!-- 左边 -->
                            <div class=\"sh-homecontent-left\">
                                <!-- 日期 -->
                                <div class=\"sh-homecontent-left-time\" lang=\"year-" . $givenDates . "\" style=\"" . $shougbsx . "\">
                                    <span class=\"homecontent-left-time-h\">" . $day . "</span>
                                    <span class=\"homecontent-left-time-y\">" . $month . "</span>
                                </div>
                                <!-- 地址 -->
                                <div class=\"sh-homecontent-left-time-dw\">" . $ptpdw . "</div>
                            </div>
                            <!-- 右边内容框架 -->
                            <div class=\"sh-homecontent-right-wk\">

                                <!-- 右边内容主体 -->
                                <div class=\"sh-homecontent-right-lie\" onclick=\"window.location.href = './view.php?cid=" . $cid . "';\">
                                    <!-- 图片 -->
                                    <div class=\"homecontent-right-tw\">
                                        <!-- 图片 -->
                                        <div class=\"homecontent-right-tw-img\">
                                            <div class=\"homecontent-right-tw-img-wk\" style=\"grid-row: 1 / 3;\"><img src=\"./assets/img/thumbnail.svg\" data-src=\"" . $imgar[0] . "\" alt=\"\"></div>
                                            <div class=\"homecontent-right-tw-img-wk\"><img src=\"./assets/img/thumbnail.svg\" data-src=\"" . $imgar[1] . "\" alt=\"\"></div>
                                            <div class=\"homecontent-right-tw-img-wk\"><img src=\"./assets/img/thumbnail.svg\" data-src=\"" . $imgar[2] . "\" alt=\"\"></div>
                                            " . $wzsdbs . "
                                        </div>
                                    </div>
                                    <!-- 文字内容 -->
                                    <div class=\"homecontent-right-nr\">
                                        <div class=\"homecontent-right-nr-text\">" . $ptptext . "</div>
                                        <p class=\"homecontent-right-nr-tus\">共" . $coun . "张</p>
                                    </div>
                                </div>
                            </div>
                        </div>";
                        } elseif ($coun == 4 || $coun >= 4) {
                            $wzbank = "<div class=\"sh-homecontent-lie\">
                            <!-- 左边 -->
                            <div class=\"sh-homecontent-left\">
                                <!-- 日期 -->
                                <div class=\"sh-homecontent-left-time\" lang=\"year-" . $givenDates . "\" style=\"" . $shougbsx . "\">
                                    <span class=\"homecontent-left-time-h\">" . $day . "</span>
                                    <span class=\"homecontent-left-time-y\">" . $month . "</span>
                                </div>
                                <!-- 地址 -->
                                <div class=\"sh-homecontent-left-time-dw\">" . $ptpdw . "</div>
                            </div>
                            <!-- 右边内容框架 -->
                            <div class=\"sh-homecontent-right-wk\">

                                <!-- 右边内容主体 -->
                                <div class=\"sh-homecontent-right-lie\" onclick=\"window.location.href = './view.php?cid=" . $cid . "';\">
                                    <!-- 图片 -->
                                    <div class=\"homecontent-right-tw\">
                                        <!-- 图片 -->
                                        <div class=\"homecontent-right-tw-img\">
                                            <div class=\"homecontent-right-tw-img-wk\"><img src=\"./assets/img/thumbnail.svg\" data-src=\"" . $imgar[0] . "\" alt=\"\"></div>
                                            <div class=\"homecontent-right-tw-img-wk\"><img src=\"./assets/img/thumbnail.svg\" data-src=\"" . $imgar[1] . "\" alt=\"\"></div>
                                            <div class=\"homecontent-right-tw-img-wk\"><img src=\"./assets/img/thumbnail.svg\" data-src=\"" . $imgar[2] . "\" alt=\"\"></div>
                                            <div class=\"homecontent-right-tw-img-wk\"><img src=\"./assets/img/thumbnail.svg\" data-src=\"" . $imgar[3] . "\" alt=\"\"></div>
                                            " . $wzsdbs . "
                                        </div>
                                    </div>
                                    <!-- 文字内容 -->
                                    <div class=\"homecontent-right-nr\">
                                        <div class=\"homecontent-right-nr-text\">" . $ptptext . "</div>
                                        <p class=\"homecontent-right-nr-tus\">共" . $coun . "张</p>
                                    </div>
                                </div>
                            </div>
                        </div>";
                        }
                    } elseif ($ptplx == "video") {
                        if ($videoauplay == 1) {
                            $videobf = "autoplay";
                            $videobfplas = "";
                        } else {
                            $videobf = "";
                            $videobfplas = "<i class=\"iconfont icon-sa4f56\" id=\"sh-content-video-videobfb-" . $cid . "\" style=\"width: fit-content;height: fit-content;grid-column: 1;grid-row: 1;z-index: 5;position: absolute;top: 50%;left: 50%;transform: translate(-50%, -50%);font-size: 30px;color: #ffffff;display: flex;cursor: pointer;padding: 15px;pointer-events: none;\"></i>";
                        }
                        $wzbank = "<div class=\"sh-homecontent-lie\">
                            <!-- 左边 -->
                            <div class=\"sh-homecontent-left\">
                                <!-- 日期 -->
                                <div class=\"sh-homecontent-left-time\" lang=\"year-" . $givenDates . "\" style=\"" . $shougbsx . "\">
                                    <span class=\"homecontent-left-time-h\">" . $day . "</span>
                                    <span class=\"homecontent-left-time-y\">" . $month . "</span>
                                </div>
                                <!-- 地址 -->
                                <div class=\"sh-homecontent-left-time-dw\">" . $ptpdw . "</div>
                            </div>
                            <!-- 右边内容框架 -->
                            <div class=\"sh-homecontent-right-wk\">
                                <!-- 右边内容主体 -->
                                <div class=\"sh-homecontent-right-lie\" onclick=\"window.location.href = './view.php?cid=" . $cid . "';\">
                                    <!-- 视频 -->
                                    <div class=\"homecontent-right-tw\">
                                        <!-- 视频 -->
                                        <div class=\"homecontent-right-tw-video\">
                                            <video class=\"homecontent-right-tw-videoau\" poster=\"" . $ptpvideofm . "\" src=\"" . $ptpvideo . "\" playsinline=\"\" webkit-playsinline=\"\" preload=\"metadata\" " . $videobf . " muted=\"\" loop=\"\"></video>
                                                " . $videobfplas . "
                                                <span class=\"sh-video-span\" style=\"left: 4px;bottom: 4px;\">MP4</span>
                                                " . $wzsdbs . "
                                        </div>
                                    </div>
                                    <!-- 文字内容 -->
                                    <div class=\"homecontent-right-nr\">
                                        <div class=\"homecontent-right-nr-text\">" . $ptptext . "</div>
                                    </div>
                                </div>
                            </div>
                        </div>";
                    } elseif ($ptplx == "music") {
                        $mus = explode("|", $ptpmusic);
                        if ($mus[3] == "") {
                            $musimg = "./assets/img/musicba.jpg";
                        } else {
                            $musimg = $mus[3];
                        }
                        if ($ptptext == "") {
                            $ptpnr = "";
                        } else {
                            $ptpnr = "<div class=\"sh-homecontent-right-lie-music-title\">" . $ptptext . "</div>";
                        }
                        $wzbank = "<div class=\"sh-homecontent-lie\">
                            <!-- 左边 -->
                            <div class=\"sh-homecontent-left\">
                                <!-- 日期 -->
                                <div class=\"sh-homecontent-left-time\" lang=\"year-" . $givenDates . "\" style=\"" . $shougbsx . "\">
                                    <span class=\"homecontent-left-time-h\">" . $day . "</span>
                                    <span class=\"homecontent-left-time-y\">" . $month . "</span>
                                </div>
                                <!-- 地址 -->
                                <div class=\"sh-homecontent-left-time-dw\">" . $ptpdw . "</div>
                            </div>
                            <!-- 右边内容框架 -->
                            <div class=\"sh-homecontent-right-wk\">
                                <!-- 右边内容主体 -->
                                <div class=\"sh-homecontent-right-lie-musicwk\" onclick=\"window.location.href = './view.php?cid=" . $cid . "';\">" . $wzsdbs . "
                                    " . $ptpnr . "
                                    <div class=\"sh-homecontent-right-lie sh-homecontent-right-lie-music\">
                                        <!-- 音乐 -->
                                        <div class=\"homecontent-right-tw homecontent-right-tw-music\">
                                            <!-- 图片 -->
                                            <div class=\"homecontent-right-tw-img\" style=\"grid-template-columns: 1fr;grid-template-rows: 1fr;\">
                                                <div class=\"homecontent-right-tw-img-wk homecontent-right-tw-img-wk-music\"><img src=\"./assets/img/thumbnail.svg\" data-src=\"" . $musimg . "\" alt=\"\"><i class=\"iconfont icon-sa4f56\"></i></div>
                                            </div>
                                        </div>
                                        <!-- 文字内容 -->
                                        <div class=\"homecontent-right-nr homecontent-right-nr-music\">
                                            <div class=\"homecontent-right-nr-text homecontent-right-nr-text-music\" style=\"color: var(--thetitle);\">" . $mus[1] . "</div>
                                            <p class=\"homecontent-right-nr-text-music-p homecontent-right-nr-text-music\">" . $mus[2] . "</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>";
                    } elseif ($ptplx == "only") {
                        $wzbank = "<div class=\"sh-homecontent-lie\">
                            <!-- 左边 -->
                            <div class=\"sh-homecontent-left\">
                                <!-- 日期 -->
                                <div class=\"sh-homecontent-left-time\" lang=\"year-" . $givenDates . "\" style=\"" . $shougbsx . "\">
                                    <span class=\"homecontent-left-time-h\">" . $day . "</span>
                                    <span class=\"homecontent-left-time-y\">" . $month . "</span>
                                </div>
                                <!-- 地址 -->
                                <div class=\"sh-homecontent-left-time-dw\">" . $ptpdw . "</div>
                            </div>
                            <div class=\"sh-homecontent-right-wk\">
                                <!-- 右边内容主体 -->
                                <div class=\"sh-homecontent-right-lie\" onclick=\"window.location.href = './view.php?cid=" . $cid . "';\">
                                    <!-- 仅文字 -->
                                    <!-- 文字内容 -->
                                    <div class=\"homecontent-right-nr\" style=\"min-height: 10px;background: var(--fgxys);position:relative;\">
                                        <div class=\"homecontent-right-nr-text homecontent-right-nr-textjw\" style=\"margin: 10px;\">" . $ptptext . "</div>
                                        " . $wzsdbs . "
                                    </div>
                                </div>
                            </div>
                        </div>";
                    }
                    echo $wzbank;
                }
                ?></div>
            </div>

            <!-- 我的收藏内容区域 -->
            <div class="profile-content" id="my-favorites-content">
                <div class="sh-homecontent">
                    <?php
                    // 获取我的收藏
                    $sql_favorites = "SELECT e.* FROM essay e 
                                     INNER JOIN favorites f ON e.cid = f.cid 
                                     WHERE f.userid = '{$user_zh}' AND e.ptpaud = 1 
                                     ORDER BY f.addtime DESC LIMIT 0,10";
                    $result_favorites = $conn->query($sql_favorites);
                    
                    if ($result_favorites && $result_favorites->num_rows > 0) {
                        while ($row = $result_favorites->fetch_assoc()) {
                            $cid = $row["cid"];
                            $ptptime = $row["ptptime"];
                            $ptptext = $row["ptptext"];
                            $ptpimag = $row["ptpimag"];
                            $ptpvideo = $row["ptpvideo"];
                            $ptpvideofm = $row["ptpvideofm"];
                            $ptpmusic = $row["ptpmusic"];
                            $ptpuser = $row["ptpuser"];
                            $ptpname = $row["ptpname"];
                            $ptplx = $row["ptplx"];
                            $ptpdw = $row["ptpdw"];
                            $ptpsd = $row["ptpsd"];
                            $ptpys = $row["ptpys"];
                            if ($ptpys == 0) {
                                $wzsdbs = "<div class=\"sh-homecontent-right-lie-sd\"><i class=\"iconfont icon-suoding\"></i></div>";
                            } else {
                                $wzsdbs = "";
                            }
                            
                            // 时间处理
                            $givenDate = $ptptime;
                            $givenDates = date("Y", strtotime($givenDate));
                            $month = date("m月", strtotime($givenDate));
                            $day = date("d", strtotime($givenDate));
                            if ($givenDates == date("Y")) {
                                $shougbsx = "display: none;";
                            } else {
                                $shougbsx = "";
                            }
                            
                            // 显示日期头
                            echo "<div class=\"sh-homecontent-timed\" style=\"{$shougbsx}\">{$givenDates}</div>";
                            echo "<div class=\"sh-homecontent-lie\">";
                            
                            // 左侧日期
                            echo "<div class=\"sh-homecontent-left\" lang=\"{$month}{$day}\">";
                            echo "<div class=\"sh-homecontent-left-time\">";
                            echo "<div class=\"homecontent-left-time-h\">{$month}</div>";
                            echo "<div class=\"homecontent-left-time-y\">{$day}</div>";
                            echo "</div>";
                            echo "</div>";
                            
                            // 右侧内容
                            echo "<div class=\"sh-homecontent-right-wk\">";
                            echo "<div class=\"sh-homecontent-right-lie\">";
                            echo "{$wzsdbs}";
                            
                            // 根据类型显示不同内容
                            if ($ptplx == "img") {
                                // 图片内容
                                $imgar = explode("(+@+)", $ptpimag);
                                $coun = count($imgar);
                                
                                echo "<div class=\"homecontent-right-tw\">";
                                echo "<a href=\"./view.php?cid={$cid}\">";
                                echo "<div class=\"homecontent-right-tw-img\">";
                                
                                // 显示图片
                                if ($coun == 1) {
                                    echo "<div class=\"homecontent-right-tw-img-wk\"><img src=\"{$ptpimag}\"></div>";
                                } else {
                                    $i = 0;
                                    foreach ($imgar as $img) {
                                        if ($i < 3) { // 最多显示3张
                                            echo "<div class=\"homecontent-right-tw-img-wk\"><img src=\"{$img}\"></div>";
                                            $i++;
                                        }
                                    }
                                    if ($coun > 3) {
                                        echo "<div class=\"homecontent-right-tw-img-wk\" style=\"display:flex;align-items:center;justify-content:center;background:rgba(0,0,0,0.5);color:white;\">+".($coun-3)."</div>";
                                    }
                                }
                                
                                echo "</div>";
                                echo "<div class=\"homecontent-right-nr\">";
                                echo "<div class=\"homecontent-right-nr-text\">{$ptptext}</div>";
                                echo "</div>";
                                echo "</a>";
                                echo "</div>";
                            } elseif ($ptplx == "video") {
                                // 视频内容
                                echo "<div class=\"homecontent-right-tw\">";
                                echo "<a href=\"./view.php?cid={$cid}\">";
                                echo "<div class=\"homecontent-right-tw-video\">";
                                echo "<img src=\"{$ptpvideofm}\">";
                                echo "<div class=\"homecontent-right-tw-videoau\"><i class=\"iconfont icon-bofang\"></i></div>";
                                echo "</div>";
                                echo "<div class=\"homecontent-right-nr\">";
                                echo "<div class=\"homecontent-right-nr-text\">{$ptptext}</div>";
                                echo "</div>";
                                echo "</a>";
                                echo "</div>";
                            } elseif ($ptplx == "music") {
                                // 音乐内容
                                echo "<div class=\"sh-homecontent-right-lie-musicwk\">";
                                echo "<i class=\"iconfont icon-yinle\"></i>";
                                echo "<div class=\"sh-homecontent-right-lie-music-title\">";
                                echo "<a href=\"./view.php?cid={$cid}\">{$ptptext}</a>";
                                echo "</div>";
                                echo "</div>";
                            } else {
                                // 纯文本内容
                                echo "<div class=\"homecontent-right-nr\">";
                                echo "<div class=\"homecontent-right-nr-textjw\">";
                                echo "<a href=\"./view.php?cid={$cid}\">{$ptptext}</a>";
                                echo "</div>";
                                echo "</div>";
                            }
                            
                            echo "</div>";
                            echo "</div>";
                            echo "</div>";
                        }
                    } else {
                        echo '<div class="empty-content">
                                <div class="empty-icon"><i class="ri-star-line"></i></div>
                                <div class="empty-text">暂无收藏内容</div>
                                <a href="./index.php" class="empty-action">浏览内容</a>
                              </div>';
                    }
                    ?>
                </div>
            </div>
            
            <div class="footer">
                <div class="footer-text" id="footer-text-zt" onclick="hqgd()">点击加载更多..</div>
                <p style="display:none" id="footer-text-hqgd"><?php echo $wzsl;?></p>
            </div>



            </div>

     
     
     
    <!--右下角悬浮菜单-->
    <div class="sh-menu" id="sh-menu">
        <?php 
    $darktheme = $_COOKIE["dark_theme"];
    if ($darktheme == "dark-theme") {
        ?><div class="sh-menu-k" id="day" lang="0"><i class="iconfont icon-yueliang" id="day-i"></i></div><?php 
    } else {
        ?><div class="sh-menu-k" id="day" lang="1"><i class="iconfont icon-ai250" id="day-i"></i></div><?php 
    }
    ?>    <div class="sh-menu-k" id="scrtop" onclick="scrollToTop()"><i class="iconfont icon-weibiaoti-x-copy"></i></div>
    </div>



    <?php 
    if ($musplay == 0) {
        include "./site/musicbk.php";
    } elseif ($musplay == 1) {
        include "./site/musicbkcla.php";
    }
    ?>


    <div class="sh-copyright">
        <span class="sh-copyright-banquan" id="sh-copyright-banquan"><?php echo $copyright;?></span>&nbsp;
        <?php 
    if ($beian != "") {
        echo "<span class=\"sh-copyright-banquan\">" . html_entity_decode($beian) . "</span>";
    }
    ?></div>


    </div>

    <!-- 底部菜单 -->
    <div class="footer-menu">
        <a href="./index.php" class="footer-menu-item">
            <i class="ri-home-4-line"></i>
            <span>首页</span>
        </a>
        <a href="./discover.php" class="footer-menu-item">
            <i class="ri-compass-3-line"></i>
            <span>发现</span>
        </a>
        <a href="./notify.php" class="footer-menu-item">
            <i class="ri-notification-2-line"></i>
            <span>通知</span>
            <?php
            // 检查是否有未读通知
            if ($userdlzt == 1) {
                $sql_unread = "SELECT COUNT(*) as count FROM notifications WHERE user_to = '{$user_zh}' AND is_read = 0";
                $result_unread = $conn->query($sql_unread);
                $unread_count = 0;
                
                if ($result_unread && $row_unread = $result_unread->fetch_assoc()) {
                    $unread_count = $row_unread['count'];
                }
                
                if ($unread_count > 0) {
                    echo '<div class="notification-dot"></div>';
                }
            }
            ?>
        </a>
        <a href="./home.php" class="footer-menu-item active">
            <i class="ri-user-3-fill"></i>
            <span>我的</span>
        </a>
    </div>



<script charset="UTF-8" id="LA_COLLECT" src="//sdk.51.la/js-sdk-pro.min.js"></script>
<script>LA.init({id:"KbK2AGjkzWGBfylr",ck:"KbK2AGjkzWGBfylr"})</script>
    <script type="text/javascript" src="./assets/js/home.js?v=<?php echo $resversion;?>"></script>
    <script type="text/javascript" src="./assets/js/jquery.min.js"></script>
    <script type="text/javascript" src="./assets/mesg/dist/js/sh-noytf.js?v=<?php echo $resversion;?>"></script>
    <script type="text/javascript">
        // 切换暗黑模式函数
        function toggleDarkMode() {
            var body = document.body;
            if (body.classList.contains('dark-theme')) {
                body.classList.remove('dark-theme');
                localStorage.setItem('theme', 'light');
                document.cookie = "dark_theme=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
            } else {
                body.classList.add('dark-theme');
                localStorage.setItem('theme', 'dark');
                document.cookie = "dark_theme=dark-theme; path=/; max-age=" + 60 * 60 * 24 * 30;
            }
        }
        
        // 根据存储的主题设置应用主题
        document.addEventListener('DOMContentLoaded', function() {
            var savedTheme = localStorage.getItem('theme');
            if (savedTheme === 'dark') {
                document.body.classList.add('dark-theme');
            }
        });
        
        //删除重复年
        var timedElements = document.getElementsByClassName('sh-homecontent-timed');
        var timedElementsArray = Array.from(timedElements);

        timedElementsArray.forEach((element, index) => {
            if (index > 0 && element.innerHTML === timedElementsArray[index - 1].innerHTML) {
                element.remove();
            }
        });
        //隐藏重复日期
        var elements = document.getElementsByClassName('sh-homecontent-left-time');
        var langValues = [];
        var duplicateIndexes = [];
        for (var i = 0; i < elements.length; i++) {
            var lang = elements[i].lang;

            if (langValues.includes(lang)) {
                duplicateIndexes.push(i);
            } else {
                langValues.push(lang);
            }
        }
        for (var j = 0; j < elements.length; j++) {
            if (duplicateIndexes.includes(j)) {
                elements[j].style.display = 'none';
                elements[j].lang = '';
            }
        }

        //设置每个不同月日的顶部间距
        // 获取所有class为sh-homecontent-left-time的元素
        var elements = document.querySelectorAll('.sh-homecontent-left-time');
        // 遍历元素
        for (var i = 0; i < elements.length; i++) {
            // 判断元素是否有display: none;属性
            if (window.getComputedStyle(elements[i]).display !== 'none') {
                // 给父元素的父元素添加属性 margin-top: 20px;
                elements[i].parentNode.parentNode.style.marginTop = '25px';
            }
        }
        
        //恢复时间颜色
        document.querySelectorAll(".homecontent-left-time-h, .homecontent-left-time-y").forEach(function(element) {
            element.style.color = "var(--textqh)";
        });
        
        // 添加滚动监听，实现导航背景透明度变化
        window.addEventListener('scroll', function() {
            var header = document.querySelector('.sh-main-head');
            var scrollPosition = window.scrollY;
            
            if (scrollPosition > 30) {
                header.style.backgroundColor = 'var(--background)';
                header.style.boxShadow = '0 2px 10px rgba(0,0,0,0.05)';
            } else {
                header.style.backgroundColor = 'transparent';
                header.style.boxShadow = 'none';
            }
        });

        // 标签切换功能
        document.addEventListener('DOMContentLoaded', function() {
            const tabs = document.querySelectorAll('.profile-tab');
            const contents = document.querySelectorAll('.profile-content');
            
            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const tabId = this.getAttribute('data-tab');
                    
                    // 移除所有标签的激活状态
                    tabs.forEach(t => t.classList.remove('active'));
                    
                    // 激活当前标签
                    this.classList.add('active');
                    
                    // 隐藏所有内容
                    contents.forEach(content => content.classList.remove('active'));
                    
                    // 显示对应内容
                    document.getElementById(tabId + '-content').classList.add('active');
                });
            });
        });
    </script>
    <?php echo "<script type=\"text/javascript\">" . $filtercujs . "</script>";?></body>
</html>