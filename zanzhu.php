<?php

//decode by nige112
if ($_SERVER["REQUEST_METHOD"] === "POST") {
	$arr = [["code" => "201", "msg" => "非法请求"]];
	exit(json_encode($arr, JSON_UNESCAPED_UNICODE));
}
$iteace = "0";
if (is_file("./config.php")) {
	require "./config.php";
} else {
	exit("未检测到配置文件：<span style=\"color: red;\">config.php</span>，若您是首次使用请先进行安装,删除文件夹 <span style=\"color: red;\">[install/]</span> 下的 <span style=\"color: red;\">[ins.bak]</span> 文件后进行安装。");
}
require "./api/wz.php";
$useke = addslashes(htmlspecialchars($_GET["useke"]));
$sql = "select * from user where username = '{$useke}'";
$result = $conn->query($sql);

?><!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>赞助众筹 - <?php echo $name;?></title>
    <!--为搜索引擎定义关键词-->
    <meta name="keywords" content="<?php echo $name;?>">
    <!--为网页定义描述内容 用于告诉搜索引擎，你网站的主要内容-->
    <meta name="description" content="<?php echo $name . "," . $subtitle;?>">
    <meta name="author" content="<?php echo $name;?>">
    <meta name="copyright" content="<?php echo $name;?>">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta http-equiv="cache-control" content="no-cache">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0,minimum-scale=1.0, user-scalable=no minimal-ui">
    <link rel="apple-touch-icon" sizes="57x57" href="<?php echo $icon;?>" /><!-- Standard iPhone -->
    <link rel="apple-touch-icon" sizes="114x114" href="<?php echo $icon;?>" /><!-- Retina iPhone -->
    <link rel="apple-touch-icon" sizes="72x72" href="<?php echo $icon;?>" /><!-- Standard iPad -->
    <link rel="apple-touch-icon" sizes="144x144" href="<?php echo $icon;?>" /><!-- Retina iPad -->
    <link rel="icon" href="<?php echo $icon;?>" type="image/x-icon" />
    <meta name="robots" content="index,follow">
    <meta name="format-detection" content="telephone=no">
    <meta http-equiv="x-rim-auto-match" content="none">
    <style>
  body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 0;
    }

   .timeline-wrapper {
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
    }

   .timeline {
      list-style: none;
      padding: 0;
    }

   .timeline-item {
      position: relative;
      padding-left: 40px;
      margin-bottom: 30px;
    }

   .timeline-item::before {
      content: '';
      width: 4px;
      height: 100%;
      background-color: #03a9f4;
      position: absolute;
      left: 12px;
    }

   .timeline-dot {
      width: 16px;
      height: 16px;
      background-color: #03a9f4;
      border-radius: 50%;
      position: absolute;
      left: 6px;
      top: 0;
    }

   .timeline-date {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 10px;
    }

   .timeline-content {
      font-size: 14px;
    }

    @media (max-width: 767px) {
     .timeline-wrapper {
        padding: 10px;
      }

     .timeline-item {
        padding-left: 30px;
      }

     .timeline-item::before {
        left: 8px;
      }

     .timeline-dot {
        width: 12px;
        height: 12px;
        left: 4px;
      }
    }

       .sponsors-list {
          display: flex;
    flex-direction: column;
    gap: 10px;
    height: 300px;
    overflow: auto;
        }

       .sponsor-item {
            display: flex;
            justify-content: space-between;
            background-color: #e5f7ff;
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
  </style>
    <?php 
if ($rosdomain == 1) {
	?><meta name="referrer" content="no-referrer"><?php 
}
?>    <link rel="stylesheet" href="<?php echo $iconurl_url;?>">
    <link rel="stylesheet" type="text/css" href="./assets/css/style.css?v=<?php echo $resversion;?>">
    <link rel="stylesheet" type="text/css" href="./assets/mesg/dist/css/style.css?v=<?php echo $resversion;?>"><!--引入弹窗对话框-->
     <link rel="stylesheet" type="text/css" href="./assets/css/jquery.fancybox.min.css?v=<?php echo $resversion;?>">
         <script type="text/javascript" src="./assets/js/jquery.min.js"></script>
    <script type="text/javascript" src="./assets/js/jquery.fancybox.min.js?v=<?php echo $resversion;?>"></script>
    <?php echo $scfontzt;?>    <?php 
$folderPath = "./user/bobg/";
if (!file_exists($folderPath)) {
	mkdir($folderPath, 511, true);
}
if (file_exists("./user/bobg/bobg.jpg")) {
	?><!--背景图--><style>body{background-image: url(./user/bobg/bobg.jpg);}</style><?php 
} elseif (file_exists("./user/bobg/bobg.png")) {
	?><!--背景图--><style>body{background-image: url(./user/bobg/bobg.png);}</style><?php 
} elseif (file_exists("./user/bobg/bobg.jpeg")) {
	?><!--背景图--><style>body{background-image: url(./user/bobg/bobg.jpeg);}</style><?php 
} elseif (file_exists("./user/bobg/bobg.gif")) {
	?><!--背景图--><style>body{background-image: url(./user/bobg/bobg.gif);}</style><?php 
}
?>    <?php echo "<style>" . $filtercucss . "</style>";?></head>
<body class="<?php 
if (isset($_COOKIE["dark_theme"])) {
	if ($_COOKIE["dark_theme"] == "dark-theme") {
		echo "dark-theme";
	}
}
echo "\">
    ";
if ($pagepass != "") {
	if (isset($_COOKIE["pagepass"])) {
		if ($_COOKIE["pagepass"] != md5(md5($pagepass))) {
			include "./site/pagepass.php";
		}
	} else {
		include "./site/pagepass.php";
	}
}
?>    <!--网页主体框架-->
    <div class="centent">
        <!-- 页面载体 -->
        <div class="sh-main setup-main">
            <!-- 头部 -->
            <div class="sh-main-head setup-main-head">
                <!-- 顶部菜单 -->
                <div class="sh-main-head-top setup-main-top" id="sh-main-head-top">
                    <!-- 左边 -->
                    <div class="sh-main-head-top-left">
                        <div class="sh-main-head-top-left-s" onclick="location.href='./index.php'">
                            <i class="iconfont icon-weibiaoti al-sxbh" id="top-left-1"></i>
                        </div>
                        <div class="sh-main-head-top-left-s setup-main-head-top-left-s" style="width:80px">
                            <span class="setup-main-title" >赞助众筹</span>
                        </div>
                    </div>
                    <!-- 右边 -->
                    <!--div class="sh-main-head-top-right">
                    </div-->
                </div>

            </div>
            <!-- 头部 end -->
            
            <!-- 设置内容区域 -->

            <div class="sh-login-main-con" style="margin-top: 0px;background: var(--cobg);padding-top: 10px;">
 <div class="timeline-wrapper">
   <img src="qrwx.png" width="100%">
    <p style="font-size:14px">1.赞助后请备注姓名,我会公开赞助名单.</p>
   <p style="font-size:14px">2.并非强制性赞助,金额不限,随着网站用户增多网站开销也越来越大了,所以感谢各位谦友的理解！</p>
  
   <p style="font-size:14px">3.所有来自赞赏的钱都将投入到网站的开销当中,感谢大家的支持!</p>
   <!--<p style="font-size:14px">4.有时间大家也可以帮忙刷刷广告支持谦友圈,可以反复看，辛苦啦</p>-->
  </div>
  
   <!--<a href="https://ydy.aizy.site/ydy" style="background: #07c160;padding: 10px;margin: 10px;">点击免费看广告赞助谦友圈</a>-->
   
        </div>
       
        <div style="background: #fff;margin: 10px 0;padding: 20px;font-size: 14px;">
    <h4>问：为什么网站运营会有成本？</h4><br>
    <p>答：为了大家上传和观看图片/视频流畅所以选择了把图片和视频储存到了腾讯云的云储存，每次访问一次图片和视频腾讯云就会进行收费计算，根据每天的访问量进行统计收费，目前来看成本在0.5元/GB，而且不包括每个月的服务器费用，我也只是普通的一个打工人谦友，所以尽可能给大家服务做到最好，当一天支出超出我的能力范围的时候可能我要靠大家众筹或者我会对接一些平台的广告来维护谦友圈了，也希望各位谦友能理解，我们四巡见啦，感谢支持！</p>
    </div>
    
    <div style="background: #fff;margin-top:10px;padding:20px;font-size: 14px;">
   <h4>开支截图</h4>
   <br>
<a href="/upload/zd/1.png" class="fancybox"><img width="100%" src="/upload/zd/1.png"></a>
<a href="/upload/zd/2.png" class="fancybox"><img width="100%" src="/upload/zd/2.png"></a>
<a href="/upload/zd/3.png" class="fancybox"><img width="100%" src="/upload/zd/3.png"></a>
<a href="/upload/zd/4.png" class="fancybox"><img width="100%" src="/upload/zd/4.png"></a>
<a href="/upload/zd/5.png" class="fancybox"><img width="100%" src="/upload/zd/5.png"></a>
<a href="/upload/zd/6.png" class="fancybox"><img width="100%" src="/upload/zd/6.png"></a>
   </div>
    
    
        <div style="background: #fff;margin-top:10px;padding:20px;font-size: 14px;">
   <h4>赞助名单</h4>
   <br>
    <div class="sponsors-list">
        <div class="sponsor-item">
            <span>谦谦in</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>*</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>爱吃土豆豆的羊</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>李霁</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>眼睛</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>苏格拉面</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>0717执行官</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>*哦</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>冰阔落</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>b*e</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>冬海</span>
            <span>￥17.17</span>
        </div>
        <div class="sponsor-item">
            <span>小洋</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>song 0717</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>舔狗日记</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>*迟</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>我薛爸爸</span>
            <span>￥17.17</span>
        </div>
        <div class="sponsor-item">
            <span>*光</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>Cc</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>*山</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>小薯片吖0717</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>*空</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>h*</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>谦毓</span>
            <span>￥17.17</span>
        </div>
        <div class="sponsor-item">
            <span>爱不走</span>
            <span>￥17.17</span>
        </div>
        <div class="sponsor-item">
            <span>张广舜</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>请叫我谦友</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>*木</span>
            <span>￥17.17</span>
        </div>
        <div class="sponsor-item">
            <span>橘子</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>*</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>0717号小行星</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>小雪</span>
            <span>￥17.17</span>
        </div>
        <div class="sponsor-item">
            <span>*乐</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>Yq7</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>桃子米面</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>安乖乖</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>君君</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>*心</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>J*i</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>Zzo</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>*H</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>7*.</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>值钱的端午</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>Coco杨</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>薛值得</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>匿名谦友</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>x毅楠</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>谦友</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>yuxuan</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>啃啃薛鱼饼</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>*</span>
            <span>￥17.17</span>
        </div>
        <div class="sponsor-item">
            <span>薛抱~</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>*我</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>lhy</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>*_</span>
            <span>￥17.17</span>
        </div>
        <div class="sponsor-item">
            <span>Guovo</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>*.</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>薛之谦的一半717</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>小薛人</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>6*8</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>薛宝宝</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>风的语言</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>谦友西瓜</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>*幸</span>
            <span>￥17.17</span>
        </div>
        <div class="sponsor-item">
            <span>*念</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>*雨</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>世界和平</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>*前</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>*.</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>*家</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>*薯条</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>*.</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>绵绵</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>L*Y</span>
            <span>￥7.17</span>
        </div>
        <div class="sponsor-item">
            <span>小羊与薛</span>
            <span>￥7.17</span>
        </div>
    </div>
    </div>
      <script>
    // 获取包含赞助名单的容器元素
    const sponsorsList = document.querySelector('.sponsors-list');
    // 滚动速度，可根据需要调整
    const scrollSpeed = 3;
    // 每隔一定时间执行一次滚动操作
    setInterval(() => {
      sponsorsList.scrollTop += scrollSpeed;
      // 当滚动到底部时，重新回到顶部
      if (sponsorsList.scrollTop + sponsorsList.clientHeight >= sponsorsList.scrollHeight) {
        sponsorsList.scrollTop = 0;
      }
    }, 30);
  </script>
     
        <!-- 页面载体 end -->
      
      



 
<!--版权-->
<div class="sh-copyright">
    <span class="sh-copyright-banquan" id="sh-copyright-banquan"><?php echo $copyright;?></span>&nbsp;
    <?php 
if ($beian != "") {
	echo "<span class=\"sh-copyright-banquan\">" . html_entity_decode($beian) . "</span>";
}
?></div>
<!--版权-->


    </div>
<script>
$(document).ready(function() {
    $('.fancybox').fancybox();
});
</script>
    <script type="text/javascript" src="./assets/js/repass.js?v=<?php echo $resversion;?>"></script>
    <script type="text/javascript" src="./assets/mesg/dist/js/sh-noytf.js?v=<?php echo $resversion;?>"></script><!--引入弹窗对话框-->
    <?php echo "<script type=\"text/javascript\">" . $filtercujs . "</script>";?></body>
</html>