<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible"="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>免费在线压缩图片_在线无损压缩图片_图片压缩源码开源</title>
    <link href="dist/js/css?family=Fira+Sans:400,400i,500,700" rel="stylesheet">
    <link rel="stylesheet" href="dist/css/style.css">
    <script src="dist/js/scrollreveal.min.js"></script>
    <style>
        /* 删除上传按钮后面文字 */
        input[type="file"] {
            color: transparent;
        }

       .inputstyle {
            width: 144px;
            height: 41px;
            cursor: pointer;
            font-size: 30px;
            outline: medium none;
            position: absolute;
            filter: alpha(opacity=0);
            -moz-opacity: 0;
            opacity: 0;
            left: 0px;
            top: 0px;
        }

        display: inline-block;
        max-width: 750px;
    }

    select {
        border: solid 1px #888;
        border-radius: 5px;
        padding: 10px;
    }

    #fileinput {
        border: solid 1px #888;
        width: 500px;
        border-radius: 5px;
        padding: 10px;
    }

    #downloadBtn {
        background-color: #e6e6e6;
        border: solid 1px #e6e6e6;
        color: #FFF!important;
        display: inline-block;
        width: 150px;
        padding: 10px;
        border-radius: 5px;
        cursor: pointer;
        outline: none;
    }
    </style>
</head>

<body class="is-boxed has-animations">
    <div class="body-wrap boxed-container">
        <header class="site-header">
            <div class="container">
                <div class="site-header-large-bg"><span></span></div>
                <div class="site-header-inner">
                    <div class="brand header-brand">
                        <h1 class="m-0">
                            <a href="#" onclick="window.history.back(); return false;">
                                <svg t="1732217162925" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5897" width="50" height="50"><path d="M512 512m-491.52 0a491.52 491.52 0 1 0 983.04 0 491.52 491.52 0 1 0-983.04 0Z" fill="#376CF6" opacity=".3" p-id="5898"></path><path d="M512 512m-389.12 0a389.12 389.12 0 1 0 778.24 0 389.12 389.12 0 1 0-778.24 0Z" fill="#376CF6" opacity=".4" p-id="5899"></path><path d="M746.7008 532.48H277.2992a20.48 20.48 0 0 1-13.5168-35.84l150.1184-131.4816a20.48 20.48 0 0 1 27.0336 30.9248L331.776 491.52h414.9248a20.48 20.48 0 0 1 0 40.96z" fill="#FFFFFF" p-id="5900"></path><path d="M430.08 664.9856a20.48 20.48 0 0 1-13.5168-5.12L266.24 528.384a20.48 20.48 0 0 1-1.8432-28.8768 20.48 20.48 0 0 1 28.8768-1.8432l149.9136 131.2768a20.48 20.48 0 0 1 1.8432 29.0816 20.48 20.48 0 0 1-14.9504 6.9632z" fill="#FFFFFF" p-id="5901"></path></svg>
                            </a>
                        </h1>
                    </div>
                </div>
            </div>
        </header>

        <main>
            <section class="hero">
                <div class="container">
                    <div class="hero-inner">
                        <div class="hero-copy">
                            <div class="container-sm">
                                <h1 class="hero-title h2-mobile mt-0 is-revealing">免费在线图片压缩</h1>
                                <p class="hero-paragraph is-revealing">0717谦友圈</p>
                                <div>
                                    <div class="configuration">
                                        <span>压缩比例：</span>
                                        <select id="quality">
                                            <option value="0.5">50%</option>
                                            <option value="0.4">60%</option>
                                            <option value="0.2">80%</option>
                                        </select>
                                        <!--&nbsp;&nbsp;&nbsp;-->
                                        <!--<span>最大宽度：</span>-->
                                        <!--<select id="maxwidth">-->
                                        <!--    <option value="750">750</option>-->
                                        <!--    <option value="640">640</option>-->
                                        <!--    <option value="375">375</option>-->
                                        <!--    <option value="320">320</option>-->
                                        <!--</select>-->
                                        <!--px-->
                                    </div>
                                    <br />
                                </div>
                                <div class="hero-form newsletter-form field field-grouped is-revealing">
                                    <div class="control control-expanded">
                                        <div class=" button button-primary button-block button-shadow">
                                            <div class="">上传图片</div>
                                            <input type="file" class="inputstyle" multiple="multiple" id="fileinput" ref="input">
                                        </div>
                                    </div>
                                    <div class="control">
                                        <button type="button" id="downloadBtn" class=" button  button-block button-shadow">下载</button>
                                    </div>
                                </div>
                            </div>
                            <div id="loading"></div>
                            <br>
                            <div id="imgbox" class="frbkw"></div>
                        </div>

                    </div>
                </div>
            </section>
        </main>


    </div>

    <script src="dist/js/main.min.js"></script>
    <script src="dist/js/jszip.min.js"></script>
    <script src="dist/js/FileSaver.js"></script>
    <script src="dist/js/JCompressor.js"></script>
    <script>
       document.getElementById("fileinput").addEventListener("change", inputchange);

function inputchange() {
    var quality = document.getElementById("quality").value; // 获取压缩比例
    var maxWidth = '750'; // 最大宽度
    var loadingDom = document.getElementById("loading");
    var imgbox = document.getElementById("imgbox");
    imgbox.innerHTML = ""; // 清空之前的显示内容

    var files = this.files; // 获取文件列表
    if (files.length === 0) return; // 如果没有文件，直接返回

    loadingDom.innerHTML = "处理中，请稍候...";
    var compressedImages = []; // 存储压缩后的图片数据

    // 遍历每个文件进行处理
    Array.from(files).forEach((file, index) => {
        var fileReader = new FileReader();

        fileReader.onload = function (event) {
            var img = new Image();
            img.src = event.target.result;

            img.onload = function () {
                var canvas = document.createElement("canvas");
                var ctx = canvas.getContext("2d");

                // 设置缩放比例
                var scale = Math.min(maxWidth / img.width, 1);
                canvas.width = img.width * scale;
                canvas.height = img.height * scale;

                // 绘制图片
                ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

                // 获取压缩后的图片 Blob
                canvas.toBlob(function (blob) {
                    compressedImages.push(blob); // 保存压缩后的 Blob

                    // 将图片显示到页面
                    var imgElement = document.createElement("img");
                    imgElement.src = URL.createObjectURL(blob);
                    imgElement.style.maxWidth = "100%"; // 显示小尺寸预览
                    imgElement.style.margin = "10px";
                    imgbox.appendChild(imgElement);

                    // 检查是否处理完所有图片
                    if (compressedImages.length === files.length) {
                        loadingDom.innerHTML = "全部处理完成！";
                        enableDownload(compressedImages); // 启用下载按钮
                    }
                }, "image/jpeg", Number(quality)); // 压缩比例
            };
        };

        fileReader.readAsDataURL(file); // 读取文件
    });
}

function enableDownload(images) {
    var downloadBtn = document.getElementById("downloadBtn");
    downloadBtn.style.color = "white";
    downloadBtn.style.borderColor = "#6a62f0";
    downloadBtn.style.backgroundColor = "#6a62f0";

    // 如果在小程序中，提示长按保存
    if (navigator.userAgent.toLowerCase().indexOf("micromessenger") !== -1 &&
        navigator.userAgent.toLowerCase().indexOf("miniprogram") !== -1) {
        downloadBtn.onclick = function () {
            alert("请长按下方图片进行保存");
        };
    } else {
        // 普通浏览器环境下，批量下载
        downloadBtn.onclick = function () {
            var zip = new JSZip(); // 创建一个 JSZip 实例

            // 添加图片到 ZIP 文件
            images.forEach((blob, index) => {
                zip.file(`compressed_image_${index + 1}.jpg`, blob);
            });

            // 生成 ZIP 并下载
            zip.generateAsync({ type: "blob" }).then(function (content) {
                saveAs(content, "compressed_images.zip"); // 下载文件
            });
        };
    }
}

    </script>
</html>