<?php

//decode by nige112
if ($_SERVER["REQUEST_METHOD"] === "POST") {
	$arr = [["code" => "201", "msg" => "非法请求"]];
	exit(json_encode($arr, JSON_UNESCAPED_UNICODE));
}
$iteace = "0";
if (is_file("./config.php")) {
	require "./config.php";
} else {
	exit("未检测到配置文件：<span style=\"color: red;\">config.php</span>，若您是首次使用请先进行安装,删除文件夹 <span style=\"color: red;\">[install/]</span> 下的 <span style=\"color: red;\">[ins.bak]</span> 文件后进行安装。");
}
require "./api/wz.php";
$useke = addslashes(htmlspecialchars($_GET["useke"]));
$sql = "select * from user where username = '{$useke}'";
$result = $conn->query($sql);
$result = $result->fetch_assoc();
// print_r($result);die;

// 检查签到表结构
$check_tables_url = "./api/create_sign_tables.php";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $check_tables_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_exec($ch);
curl_close($ch);

// 获取用户签到状态和信息
$today_signed = false;
$continuous_days = 0;
$today_date = date('Y-m-d');

if ($userdlzt == 1) {
	// 检查今天是否已签到
	$sql_check_today = "SELECT id FROM sign WHERE name = '{$user_zh}' AND time LIKE '{$today_date}%'
					   UNION
					   SELECT id FROM user_sign WHERE username = '{$user_zh}' AND DATE(sign_date) = '{$today_date}'";
	$result_check_today = $conn->query($sql_check_today);
	$today_signed = ($result_check_today && $result_check_today->num_rows > 0);
	
	// 获取连续签到天数
	$sql_continuous = "SELECT continuous_days FROM user_sign_stats WHERE username = '{$user_zh}'";
	$result_continuous = $conn->query($sql_continuous);
	if ($result_continuous && $result_continuous->num_rows > 0) {
		$continuous_days = $result_continuous->fetch_assoc()['continuous_days'];
	}
}

?><!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>签到中心 - <?php echo $name;?></title>
    <!--为搜索引擎定义关键词-->
    <meta name="keywords" content="<?php echo $name;?>">
    <!--为网页定义描述内容 用于告诉搜索引擎，你网站的主要内容-->
    <meta name="description" content="<?php echo $name . "," . $subtitle;?>">
    <meta name="author" content="<?php echo $name;?>">
    <meta name="copyright" content="<?php echo $name;?>">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta http-equiv="cache-control" content="no-cache">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0,minimum-scale=1.0, user-scalable=no minimal-ui">
    <link rel="apple-touch-icon" sizes="57x57" href="<?php echo $icon;?>" /><!-- Standard iPhone -->
    <link rel="apple-touch-icon" sizes="114x114" href="<?php echo $icon;?>" /><!-- Retina iPhone -->
    <link rel="apple-touch-icon" sizes="72x72" href="<?php echo $icon;?>" /><!-- Standard iPad -->
    <link rel="apple-touch-icon" sizes="144x144" href="<?php echo $icon;?>" /><!-- Retina iPad -->
    <link rel="icon" href="<?php echo $icon;?>" type="image/x-icon" />
    <meta name="robots" content="index,follow">
    <meta name="format-detection" content="telephone=no">
    <meta http-equiv="x-rim-auto-match" content="none">
    <!-- 引入更丰富的图标库 -->
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <style>
body {
    font-family: Arial, sans-serif;
    background-color: #f4f4f4 !important;
    margin: 0;
    height: auto !important;
    scroll-behavior: smooth;
}
ul{
	list-style:none;
	padding: 0;
	margin: 0;
}
.hide{
    display: none;
}
.lf{
	float: left;
}
.lr{
	float: right;
}
.red, .red a,.red a h5, .red a p{
    color:#FF0000 !important;
}
.h20{
	height: 20px
}

/* 移除sh-main-head的高度限制 */
.sh-main-head {
    height: auto;
    min-height: 40px;
    position: sticky;
    top: 0;
    z-index: 100;
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(0,0,0,0.05);
    padding: 5px 0;
}

.dark-theme .sh-main-head {
    background-color: rgba(30, 30, 30, 0.95);
    border-bottom: 1px solid rgba(255,255,255,0.05);
}

.sign-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
}

.sign-page-title {
    font-size: 24px;
    font-weight: bold;
    background: linear-gradient(135deg, #1e88e5, #42a5f5);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    display: flex;
    align-items: center;
}

.sign-page-title i {
    margin-right: 8px;
    font-size: 26px;
    background: linear-gradient(135deg, #1e88e5, #42a5f5);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

/* 积分卡片 */
.points-card {
    background: linear-gradient(135deg, #1e88e5, #42a5f5);
    border-radius: 20px;
    padding: 25px;
    margin: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: white;
    box-shadow: 0 10px 30px rgba(30, 136, 229, 0.25);
    position: relative;
    overflow: hidden;
}

.points-card::before {
    content: '';
    position: absolute;
    top: -20px;
    right: -20px;
    width: 150px;
    height: 150px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
}

.points-card::after {
    content: '';
    position: absolute;
    bottom: -30px;
    left: -30px;
    width: 100px;
    height: 100px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
}

.points-info {
    position: relative;
    z-index: 1;
}

.points-title {
    font-size: 16px;
    opacity: 0.9;
    margin-bottom: 5px;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.points-value {
    font-size: 32px;
	font-weight: bold;
    text-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

.points-icon {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    z-index: 1;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.points-icon i {
    font-size: 32px;
    color: white;
}

/* 日历容器 */
.calendar-container {
    margin: 15px 15px 20px;
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
}

.dark-theme .calendar-container {
    background: #272727;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.calendar-header {
    background: linear-gradient(135deg, #1e88e5, #42a5f5);
    color: white;
    padding: 20px;
	text-align: center;
    position: relative;
}

.calendar-month {
    font-size: 20px;
	font-weight: bold;
    margin-bottom: 5px;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.calendar-year {
    font-size: 14px;
    opacity: 0.9;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    padding: 15px;
    gap: 8px;
}

.calendar-day-header {
    text-align: center;
    font-weight: 500;
    color: #666;
    font-size: 14px;
    padding: 8px 0;
}

.dark-theme .calendar-day-header {
    color: #bbb;
}

.calendar-day {
    aspect-ratio: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    font-size: 14px;
    font-weight: 500;
    position: relative;
    transition: all 0.3s ease;
}

.calendar-day.empty {
    background: none;
}

.calendar-day.past {
    color: #999;
    background-color: #f5f5f5;
}

.dark-theme .calendar-day.past {
    color: #777;
    background-color: #333;
}

.calendar-day.today {
    background-color: #1e88e5;
    color: white;
	font-weight: bold;
    box-shadow: 0 4px 10px rgba(30, 136, 229, 0.3);
}

.calendar-day.signed {
    background-color: #4CAF50;
    color: white;
}

/* 连续签到信息 */
.streak-info {
    text-align: center;
    margin: 15px;
    padding: 15px;
    background-color: rgba(30, 136, 229, 0.1);
    border-radius: 15px;
    color: #333;
    font-size: 15px;
}

.dark-theme .streak-info {
    background-color: rgba(30, 136, 229, 0.15);
    color: #eee;
}

.streak-days {
    font-size: 18px;
    font-weight: bold;
    color: #1e88e5;
    margin: 0 5px;
}

/* 签到按钮 */
.sign-btn-container {
    text-align: center;
    margin: 20px 15px;
}

.sign-btn {
    background: linear-gradient(to right, #ff7e5f, #feb47b);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: bold;
    display: inline-flex;
    align-items: center;
    box-shadow: 0 8px 20px rgba(255, 126, 95, 0.3);
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    cursor: pointer;
}

.sign-btn:active {
    transform: scale(0.95);
    box-shadow: 0 5px 15px rgba(255, 126, 95, 0.2);
}

.sign-btn i {
    margin-right: 8px;
    font-size: 18px;
}

/* 任务列表 */
.tasks-container {
    margin: 15px;
    background: white;
    border-radius: 20px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
}

.dark-theme .tasks-container {
    background: #272727;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.tasks-title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}

.dark-theme .tasks-title {
    color: #eee;
}

.tasks-title i {
    margin-right: 8px;
    font-size: 22px;
    color: #1e88e5;
}

.task-item {
    display: flex;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #f0f0f0;
}

.dark-theme .task-item {
    border-bottom: 1px solid #333;
}

.task-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.task-icon {
    width: 50px;
    height: 50px;
    background: rgba(30, 136, 229, 0.1);
    border-radius: 15px;
	display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 15px;
    flex-shrink: 0;
}

.task-icon i {
    font-size: 24px;
    color: #1e88e5;
}

.task-info {
    flex: 1;
}

.task-name {
	font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 5px;
}

.dark-theme .task-name {
    color: #eee;
}

.task-desc {
    font-size: 14px;
    color: #999;
}

.task-action {
    margin-left: 15px;
}

.task-btn {
    background-color: #1e88e5;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;
    box-shadow: 0 5px 15px rgba(30, 136, 229, 0.2);
}

.task-btn:active {
    transform: scale(0.95);
    box-shadow: 0 3px 10px rgba(30, 136, 229, 0.1);
}

.task-btn.completed {
    background-color: #4CAF50;
}

.msginfo {
    margin: 20px 15px;
    background: white;
    padding: 20px;
    border-radius: 20px;
    font-size: 14px;
    line-height: 1.5;
    color: #666;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
}

.dark-theme .msginfo {
    background: #272727;
    color: #bbb;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.msginfo h5 {
    margin: 0 0 10px 0;
    font-size: 16px;
    color: #333;
    font-weight: 600;
}

.dark-theme .msginfo h5 {
    color: #eee;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.points-card, .calendar-container, .streak-info, .sign-btn-container, .tasks-container, .msginfo {
    animation: fadeIn 0.6s ease-in-out;
}

/* 添加旋转动画 */
@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.rotating {
    animation: rotate 1.5s linear infinite;
    display: inline-block;
}

.sign-btn.loading {
    opacity: 0.7;
    cursor: not-allowed;
}
  </style>
    <?php 
if ($rosdomain == 1) {
	?><meta name="referrer" content="no-referrer"><?php 
}
?>    <link rel="stylesheet" href="<?php echo $iconurl_url;?>">
    <link rel="stylesheet" type="text/css" href="./assets/css/style.css?v=<?php echo $resversion;?>">
    <link rel="stylesheet" type="text/css" href="./assets/css/footer-menu.css?v=<?php echo $resversion;?>">
    <link rel="stylesheet" type="text/css" href="./assets/mesg/dist/css/style.css?v=<?php echo $resversion;?>"><!--引入弹窗对话框-->
     <link rel="stylesheet" type="text/css" href="./assets/css/jquery.fancybox.min.css?v=<?php echo $resversion;?>">
         <script type="text/javascript" src="./assets/js/jquery.min.js"></script>
    <script type="text/javascript" src="./assets/js/jquery.fancybox.min.js?v=<?php echo $resversion;?>"></script>
    <?php echo $scfontzt;?>    <?php 
$folderPath = "./user/bobg/";
if (!file_exists($folderPath)) {
	mkdir($folderPath, 511, true);
}
if (file_exists("./user/bobg/bobg.jpg")) {
	?><!--背景图--><style>body{background-image: url(./user/bobg/bobg.jpg);}</style><?php 
} elseif (file_exists("./user/bobg/bobg.png")) {
	?><!--背景图--><style>body{background-image: url(./user/bobg/bobg.png);}</style><?php 
} elseif (file_exists("./user/bobg/bobg.jpeg")) {
	?><!--背景图--><style>body{background-image: url(./user/bobg/bobg.jpeg);}</style><?php 
} elseif (file_exists("./user/bobg/bobg.gif")) {
	?><!--背景图--><style>body{background-image: url(./user/bobg/bobg.gif);}</style><?php 
}
?>    <?php echo "<style>" . $filtercucss . "</style>";?></head>
<body class="<?php 
if (isset($_COOKIE["dark_theme"])) {
	if ($_COOKIE["dark_theme"] == "dark-theme") {
		echo "dark-theme";
	}
}
echo "\">
    ";
if ($pagepass != "") {
	if (isset($_COOKIE["pagepass"])) {
		if ($_COOKIE["pagepass"] != md5(md5($pagepass))) {
			include "./site/pagepass.php";
		}
	} else {
		include "./site/pagepass.php";
	}
}
?>    <!--网页主体框架-->
    <div class="centent">
        <div class="sh-main">
           
            
            <!-- 签到页面头部 -->
            <div class="sign-header">
                <div class="sign-page-title">
                    <i class="ri-calendar-check-line"></i>签到中心
                        </div>
                        </div>
            
            <!-- 积分信息卡片 -->
            <div class="points-card">
                <div class="points-info">
                    <div class="points-title">我的积分</div>
                    <div class="points-value">
                        <?php
                        // 获取用户积分
                 
                            $sql_points = "SELECT jifen FROM user WHERE username = '{$user_zh}'";
                            $result_points = $conn->query($sql_points);
                            $user_points = 0;
                            
                            if ($result_points && $row_points = $result_points->fetch_assoc()) {
                                $user_points = $row_points['jifen'] ?: 0;
                            }
                            
                            echo $user_points;
                     
                        ?>
                    </div>
                </div>
                <div class="points-icon">
                    <i class="ri-coin-line"></i>
                </div>
            </div>
            
            <!-- 日历签到容器 -->
            <div class="calendar-container">
                <div class="calendar-header">
                    <div class="calendar-month" id="currentMonth"><?php echo date('n月'); ?></div>
                    <div class="calendar-year" id="currentYear"><?php echo date('Y年'); ?></div>
                </div>

                <div class="calendar-grid" id="calendarGrid">
                    <!-- 星期标题 -->
                    <div class="calendar-day-header">日</div>
                    <div class="calendar-day-header">一</div>
                    <div class="calendar-day-header">二</div>
                    <div class="calendar-day-header">三</div>
                    <div class="calendar-day-header">四</div>
                    <div class="calendar-day-header">五</div>
                    <div class="calendar-day-header">六</div>
                    
                    <?php
                    // 生成日历
                    $month = date('n');
                    $year = date('Y');
                    $daysInMonth = date('t', strtotime("$year-$month-01"));
                    $firstDayOfWeek = date('w', strtotime("$year-$month-01"));
                    
                    // 填充月初空白
                    for ($i = 0; $i < $firstDayOfWeek; $i++) {
                        echo '<div class="calendar-day empty"></div>';
                    }
                    
                    // 获取用户签到记录
                    $signed_days = array();
                    if ($userdlzt == 1) {
                        $current_month_start = date('Y-m-01');
                        $current_month_end = date('Y-m-t');
                        
                        $sql_sign = "SELECT DATE_FORMAT(sign_date, '%d') as day 
                                    FROM user_sign 
                                    WHERE username = '{$user_zh}' 
                                    AND sign_date BETWEEN '{$current_month_start}' AND '{$current_month_end}'";
                        $result_sign = $conn->query($sql_sign);
                        
                        if ($result_sign) {
                            while ($row_sign = $result_sign->fetch_assoc()) {
                                $signed_days[] = (int)$row_sign['day'];
                            }
                        }
                    }
                    
                    // 填充日期
                    $today = date('j');
                    for ($day = 1; $day <= $daysInMonth; $day++) {
                        $class = 'calendar-day';
                        
                        // 如果是已签到的日期
                        if (in_array($day, $signed_days)) {
                            $class .= ' signed';
                        }
                        // 如果是今天
                        elseif ($day == $today) {
                            $class .= ' today';
                        }
                        // 如果是过去的日期但未签到
                        elseif ($day < $today) {
                            $class .= ' past';
                        }
                        
                        echo "<div class=\"$class\">$day</div>";
                    }
                    
                    // 填充月末空白
                    $lastDayOfWeek = date('w', strtotime("$year-$month-$daysInMonth"));
                    for ($i = $lastDayOfWeek; $i < 6; $i++) {
                        echo '<div class="calendar-day empty"></div>';
                    }
                    ?>
                </div>
            </div>
            
            <!-- 连续签到信息 -->
            <div class="streak-info">
                您已连续签到 <span class="streak-days" id="continuousDays">
                <?php
                // 获取用户连续签到天数
                if ($userdlzt == 1) {
                    $sql_streak = "SELECT continuous_days FROM user_sign_stats WHERE username = '{$user_zh}'";
                    $result_streak = $conn->query($sql_streak);
                    $continuous_days = 0;
                    
                    if ($result_streak && $row_streak = $result_streak->fetch_assoc()) {
                        $continuous_days = $row_streak['continuous_days'] ?: 0;
                    }
                    
                    echo $continuous_days;
                } else {
                    echo '0';
                }
                ?>
                </span> 天，继续保持！
            </div>
            
            <!-- 签到按钮 -->
            <div class="sign-btn-container">
                <button class="sign-btn" id="sign">
                    <i class="ri-check-double-line"></i>
                    <?php
                    // 检查今天是否已签到
                    $today_signed = false;
                    if ($userdlzt == 1) {
                        $today_date = date('Y-m-d');
                        $sql_today = "SELECT id FROM user_sign 
                                      WHERE username = '{$user_zh}' 
                                      AND DATE(sign_date) = '{$today_date}'";
                        $result_today = $conn->query($sql_today);
                        
                        if ($result_today && $result_today->num_rows > 0) {
                            $today_signed = true;
                        }
                    }
                    
                    if ($today_signed) {
                        echo '今日已签到';
                    } else {
                        echo '签到领积分';
                    }
                    ?>
                </button>
            </div>
 
            <!-- 任务列表 -->
            <div class="tasks-container">
                <div class="tasks-title"><i class="ri-task-line"></i>每日任务</div>
                
                <div class="task-item">
                    <div class="task-icon">
                        <i class="ri-chat-3-line"></i>
				</div>
                    <div class="task-info">
                        <div class="task-name">发表动态</div>
                        <div class="task-desc">发布一篇动态，获得10积分</div>
				</div>
                    <div class="task-action">
                        <button class="task-btn" onclick="location.href='./edit.php'">去完成</button>
				</div>
			</div>
                
                <div class="task-item">
                    <div class="task-icon">
                        <i class="ri-thumb-up-line"></i>
                    </div>
                    <div class="task-info">
                        <div class="task-name">给好友点赞</div>
                        <div class="task-desc">给3位好友点赞，获得5积分</div>
                    </div>
                    <div class="task-action">
                        <button class="task-btn" onclick="location.href='./index.php'">去完成</button>
				</div>
		</div>
                
                <div class="task-item">
                    <div class="task-icon">
                        <i class="ri-share-forward-line"></i>
                    </div>
                    <div class="task-info">
                        <div class="task-name">分享内容</div>
                        <div class="task-desc">分享一篇内容到社交平台，获得15积分</div>
							</div>
                    <div class="task-action">
                        <button class="task-btn" onclick="location.href='./index.php'">去完成</button>
						</div>
							</div>
						</div>
					
			<div class="msginfo">
			  <h5>积分的用途？</h5>
              <p>积分能用于朋友圈置顶，置顶后的动态会在众多内容中优先显示，更容易被看到，从而增加曝光度和关注度。还可以用于兑换特权、礼品等。</p>
			</div>
			<div class="h20"></div>
		</div>
	</div>

	
 
<!--版权-->
<div class="sh-copyright">
    <span class="sh-copyright-banquan" id="sh-copyright-banquan"><?php echo $copyright;?></span>&nbsp;
    <?php 
if ($beian != "") {
	echo "<span class=\"sh-copyright-banquan\">" . html_entity_decode($beian) . "</span>";
}
?></div>
<!--版权-->


    </div>
    
    <!-- 底部菜单 -->
    <div class="footer-menu">
        <a href="./index.php" class="footer-menu-item">
            <i class="ri-home-4-line"></i>
            <span>首页</span>
        </a>
        <a href="./discover.php" class="footer-menu-item active">
            <i class="ri-compass-3-line"></i>
            <span>发现</span>
        </a>
        <a href="./notify.php" class="footer-menu-item">
            <i class="ri-notification-2-line"></i>
            <span>通知</span>
            <?php
            // 检查是否有未读通知
            if ($userdlzt == 1) {
                $sql_unread = "SELECT COUNT(*) as count FROM notifications WHERE user_to = '{$user_zh}' AND is_read = 0";
                $result_unread = $conn->query($sql_unread);
                $unread_count = 0;
                
                if ($result_unread && $row_unread = $result_unread->fetch_assoc()) {
                    $unread_count = $row_unread['count'];
                }
                
                if ($unread_count > 0) {
                    echo '<div class="notification-dot"></div>';
                }
            }
            ?>
        </a>
        <a href="./home.php" class="footer-menu-item">
            <i class="ri-user-3-line"></i>
            <span>我的</span>
        </a>
    </div>
<script>
// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 签到按钮点击事件
    const signBtn = document.getElementById('sign');
    if (signBtn) {
        signBtn.addEventListener('click', function() {
            // 检查用户是否已登录
            <?php if ($userdlzt != 1): ?>
            warnpop('请先登录后再进行签到');
            setTimeout(function() {
                window.location.href = './login.php';
            }, 1500);
            return;
            <?php endif; ?>
            
            // 检查今天是否已签到
            <?php if ($today_signed): ?>
            warnpop('您今天已经签到过了，明天再来吧！');
            return;
            <?php endif; ?>
            
            // 禁用按钮，防止重复点击
            signBtn.disabled = true;
            signBtn.classList.add('loading');
            signBtn.innerHTML = '<i class="ri-loader-2-line rotating"></i>签到中...';
            
            // 发送签到请求
            fetch('./api/sign_action.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    // 签到成功
                    successpop(data.msg || '签到成功，获得积分！');
                    
                    // 更新今日日期为已签到
                    const today = new Date().getDate();
                    const calendarDays = document.querySelectorAll('.calendar-day');
                    calendarDays.forEach(day => {
                        const dayNum = parseInt(day.textContent);
                        if (dayNum === today && day.classList.contains('today')) {
                            day.classList.add('signed');
                            // 保留today类，只添加signed类
                        }
                    });
                    
                    // 更新连续签到天数
                    const continuousDays = document.getElementById('continuousDays');
                    if (continuousDays && data.continuous_days) {
                        continuousDays.textContent = data.continuous_days;
                    }
                    
                    // 更新积分显示
                    const pointsValue = document.querySelector('.points-value');
                    if (pointsValue && data.points) {
                        pointsValue.textContent = data.points;
                    }
                    
                    // 更新按钮文字
                    signBtn.innerHTML = '<i class="ri-check-double-line"></i>今日已签到';
                    
                    // 添加动画效果
                    signBtn.classList.add('completed');
                    signBtn.classList.remove('loading');
                    
                    // 禁用按钮
                    signBtn.disabled = true;
                    
                    // 刷新页面以更新所有状态
                    setTimeout(function() {
                        location.reload();
                    }, 1500);
                    
                } else {
                    // 签到失败
                    warnpop(data.msg || '签到失败，请稍后再试');
                    
                    // 恢复按钮状态
                    signBtn.disabled = false;
                    signBtn.classList.remove('loading');
                    signBtn.innerHTML = '<i class="ri-calendar-check-line"></i>签到领积分';
                }
            })
            .catch(error => {
                console.error('签到请求出错:', error);
                warnpop('网络错误，请稍后再试');
                
                // 恢复按钮状态
                signBtn.disabled = false;
                signBtn.classList.remove('loading');
                signBtn.innerHTML = '<i class="ri-calendar-check-line"></i>签到领积分';
            });
        });
    }
    
    // 添加滚动监听，实现导航背景透明度变化
    window.addEventListener('scroll', function() {
        var header = document.querySelector('.sh-main-head');
        if (!header) return;
        
        var scrollPosition = window.scrollY;
        
        if (scrollPosition > 30) {
            header.style.backgroundColor = 'var(--background)';
            header.style.boxShadow = '0 2px 10px rgba(0,0,0,0.05)';
        } else {
            header.style.backgroundColor = 'transparent';
            header.style.boxShadow = 'none';
        }
    });
    
    // 为任务项添加动画效果
    const taskItems = document.querySelectorAll('.task-item');
    taskItems.forEach((item, index) => {
        item.style.animationDelay = (index * 0.1) + 's';
    });
});

// 为任务按钮添加点击效果
document.querySelectorAll('.task-btn').forEach(btn => {
    btn.addEventListener('click', function(e) {
        // 如果按钮有onclick属性，不阻止默认行为
        if (!this.hasAttribute('onclick')) {
            e.preventDefault();
            const taskName = this.closest('.task-item').querySelector('.task-name').textContent;
            warnpop('正在开发中：' + taskName + ' 任务');
        }
    });
});
</script>
    <script type="text/javascript" src="./assets/js/repass.js?v=<?php echo $resversion;?>"></script>
    <script type="text/javascript" src="./assets/mesg/dist/js/sh-noytf.js?v=<?php echo $resversion;?>"></script><!--引入弹窗对话框-->
    <?php echo "<script type=\"text/javascript\">" . $filtercujs . "</script>";?></body>
</html>