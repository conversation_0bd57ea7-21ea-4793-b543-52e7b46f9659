<?php
// decode by nige112
if ($_SERVER["REQUEST_METHOD"] === "POST") {
    $arr = [["code" => "201", "msg" => "非法请求"]];
    exit(json_encode($arr, JSON_UNESCAPED_UNICODE));
}

// 载入配置文件
$iteace = "0";
if (is_file("./config.php")) {
    require "./config.php";
} else {
    exit("未检测到配置文件：<span style=\"color: red;\">config.php</span>，若您是首次使用请先进行安装,删除文件夹 <span style=\"color: red;\">[install/]</span> 下的 <span style=\"color: red;\">[ins.bak]</span> 文件后进行安装。");
}

require "./api/wz.php";

// 获取当前用户名
$usernameb = $_COOKIE["username"];
// 配置项
$merchant_id = '1000';  // 商户ID
$merchant_key = 'Roc33qMn9730G0soO8mUe7eOVKh82vsm';  // 商户密钥（替换为你的密钥）
$notify_url = 'https://0717.aizy.site/notify_url.php';  // 异步通知地址
$return_url = 'https://0717.aizy.site';  // 页面跳转通知地址
$sitename = '0717';  // 网站名称

$out_trade_no = date("YmdHis") . rand(1000, 9999);

// 支付请求参数
$name = '赞助积分';  // 商品名称
$money = '5.17';  // 商品金额
$type = 'alipay';  // 支付方式（alipay: 支付宝, wxpay: 微信支付, qqpay: QQ钱包）
$sign_type = 'MD5';  // 签名方式

$currentTime = date("Y-m-d H:i:s");



// 生成签名字符串
$sign_str = "money=$money&name=$name&notify_url=$notify_url&out_trade_no=$out_trade_no&pid=$merchant_id&return_url=$return_url&sitename=$sitename&type=$type$merchant_key";
$sign = md5($sign_str);

$sqlInsert = "INSERT INTO orders (pid, type, out_trade_no, name, money,sign ,create_time, update_time)
              VALUES ('$merchant_id', '$type', '$out_trade_no', '$usernameb', '$money','$sign', '$currentTime', '$currentTime')";
$conn->query($sqlInsert);

// 生成支付请求URL
$pay_url = "https://pay.aizy.site/submit.php?pid=$merchant_id&type=$type&out_trade_no=$out_trade_no&notify_url=$notify_url&return_url=$return_url&name=$name&money=$money&sitename=$sitename&sign=$sign&sign_type=$sign_type";

// 重定向到支付页面
header("Location: $pay_url");
exit();
?>
