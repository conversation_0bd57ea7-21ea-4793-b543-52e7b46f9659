<?php

//decode by nige112
if ($_SERVER["REQUEST_METHOD"]!== "POST") {
    $arr = [["code" => "201", "msg" => "非法请求"]];
    exit(json_encode($arr, JSON_UNESCAPED_UNICODE));
}

// 获取用户信息，这里假设你已经通过某种方式（如登录态）获取到了当前登录用户的相关信息
// 示例中假设通过 $_COOKIE 获取用户的标识（如用户名或用户ID），你需要根据实际情况修改
$usernameb = $_COOKIE["username"]; 

if (empty($usernameb)) {
    exit(json_encode(["code" => "401", "msg" => "未获取到用户信息，请先登录"], JSON_UNESCAPED_UNICODE));
}

include "../config.php";
$conn = new mysqli($servername, $username, $password, $dbname);
if ($conn->connect_error) {
    exit(json_encode(["code" => "500", "msg" => "连接数据库失败"], JSON_UNESCAPED_UNICODE));
}
// print_r($usernameb);die;
// 查询今日是否已经签到过
$today = date("Y-m-d");
$sql = "SELECT * FROM user WHERE username = '{$usernameb}' AND signtime LIKE '{$today}%'";
$result = $conn->query($sql);

if (mysqli_num_rows($result) > 0) {
    exit(json_encode(["code" => "400", "msg" => "今日已签到过，请勿重复签到"], JSON_UNESCAPED_UNICODE));
}

// 执行签到操作，给予积分并记录签到时间
$sqlUpdate = "UPDATE user SET jifen = jifen + 1, signtime = NOW() WHERE username = '{$usernameb}'";
$resultUpdate = $conn->query($sqlUpdate);

if ($resultUpdate) {
$currentTime = date("Y-m-d H:i:s");
    
$sqlInsert = "INSERT INTO sign (name, time)
              VALUES ('$usernameb','$currentTime')";
$conn->query($sqlInsert);

    exit(json_encode(["code" => "200", "msg" => "签到成功，获得1积分"], JSON_UNESCAPED_UNICODE));
} else {
    exit(json_encode(["code" => "500", "msg" => "签到失败，请稍后重试"], JSON_UNESCAPED_UNICODE));
}

$conn->close();