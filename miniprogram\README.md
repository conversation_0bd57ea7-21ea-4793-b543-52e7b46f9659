# 0717谦友圈微信小程序

## 项目简介
0717谦友圈是一个专为薛之谦粉丝打造的社交圈子小程序，支持帖子发布、表情包分享、积分签到等功能。

## 项目结构

```
miniprogram/
├── app.js                 # 小程序入口文件
├── app.json              # 小程序配置文件
├── app.wxss              # 全局样式文件
├── sitemap.json          # 站点地图配置
├── project.config.json   # 项目配置文件
├── README.md             # 项目说明文档
│
├── pages/                # 页面目录
│   ├── home/             # 首页
│   │   ├── home.js
│   │   ├── home.json
│   │   ├── home.wxml
│   │   └── home.wxss
│   ├── login/            # 登录页
│   ├── publish/          # 发布页
│   ├── message/          # 消息页
│   ├── profile/          # 个人中心
│   ├── post-detail/      # 帖子详情
│   ├── search/           # 搜索页
│   ├── emoji/            # 表情包页
│   ├── sign/             # 签到页
│   ├── ranking/          # 排行榜
│   └── settings/         # 设置页
│
├── components/           # 组件目录
│   ├── post-card/        # 帖子卡片组件
│   ├── comment-item/     # 评论项组件
│   ├── emoji-item/       # 表情包项组件
│   ├── loading/          # 加载组件
│   └── empty/            # 空状态组件
│
├── utils/                # 工具函数目录
│   ├── api.js            # API接口封装
│   ├── util.js           # 通用工具函数
│   ├── auth.js           # 认证相关工具
│   ├── storage.js        # 本地存储工具
│   └── constants.js      # 常量定义
│
├── images/               # 图片资源目录
│   ├── tabbar/           # 底部导航图标
│   ├── icons/            # 通用图标
│   └── placeholders/     # 占位图片
│
└── styles/               # 样式文件目录
    ├── variables.wxss    # 样式变量
    └── mixins.wxss       # 样式混入
```

## 功能模块

### 1. 用户认证系统
- 账号密码登录
- 用户状态管理
- 自动登录检查

### 2. 帖子功能模块
- 发布帖子（文字、图片、音乐卡片）
- 九宫格图片展示
- 图片拖拽排序
- 帖子定位功能
- 首页瀑布流展示
- 帖子搜索
- 点赞和评论
- 帖子详情查看
- 文字展开/收起
- 分享海报生成

### 3. 积分签到系统
- 连续签到奖励
- 积分排行榜
- 积分变动记录

### 4. 表情包功能
- 表情包上传
- 表情包下载
- 积分奖励机制
- 防刷机制

### 5. 消息通知系统
- 点赞通知
- 评论通知
- 系统消息

### 6. 个人中心
- 用户信息展示
- 个人帖子统计
- 功能菜单

## 技术特点

### 1. iOS风格设计
- 采用iOS设计规范
- 流畅的动画效果
- 优雅的交互体验

### 2. 组件化开发
- 可复用组件设计
- 统一的组件规范
- 便于维护和扩展

### 3. 数据兼容性
- 完全兼容现有Web端数据库
- 平滑的数据迁移
- 用户无感知切换

### 4. 性能优化
- 懒加载机制
- 图片压缩优化
- 网络请求缓存

## 开发规范

### 1. 命名规范
- 页面文件：kebab-case（如：post-detail）
- 组件文件：kebab-case（如：post-card）
- 变量命名：camelCase
- 常量命名：UPPER_SNAKE_CASE

### 2. 代码规范
- 使用ES6+语法
- 统一的错误处理
- 完善的注释说明
- 代码格式化

### 3. 样式规范
- 使用CSS变量
- 响应式设计
- 统一的间距系统
- 语义化类名

## API接口

所有API接口都基于现有Web端接口进行适配，确保数据的一致性和兼容性。

### 接口地址
- 开发环境：`http://localhost/api/`
- 生产环境：`https://your-domain.com/api/`

### 认证方式
使用用户名和passid进行身份验证，与Web端保持一致。

## 部署说明

1. 配置小程序AppID
2. 修改API接口地址
3. 上传代码到微信开发者工具
4. 提交审核发布

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 实现核心功能模块
- iOS风格界面设计

## 联系方式

如有问题或建议，请联系开发团队。
