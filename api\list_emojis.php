<?php
// 获取表情包列表API
$iteace = "1"; // 设置API状态，避免触发"请设置状态"错误

header('Content-Type: application/json; charset=utf-8');

// 引入配置文件
if (is_file("../config.php")) {
    require "../config.php";
} else {
    exit(json_encode(["code" => 500, "msg" => "配置文件不存在"]));
}

// 引入用户验证
require "../api/wz.php";

// 获取分页参数
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$limit = isset($_GET['limit']) ? intval($_GET['limit']) : 20;
$page = max(1, $page);
$limit = min(50, max(4, $limit)); // 限制每页最少4条，最多50条
$offset = ($page - 1) * $limit;

// 获取搜索关键词
$keyword = isset($_GET['keyword']) ? trim($_GET['keyword']) : '';

// 构建查询条件
$where = "e.status = 1";
$params = [];
$types = "";

if (!empty($keyword)) {
    $keyword = "%$keyword%";
    $where .= " AND (e.name LIKE ? OR e.tags LIKE ?)";
    $params[] = $keyword;
    $params[] = $keyword;
    $types .= "ss";
}

// 检查表情包表是否存在
$sql_check_table = "SHOW TABLES LIKE 'emojis'";
$result_check_table = $conn->query($sql_check_table);

if (!$result_check_table || $result_check_table->num_rows == 0) {
    // 表不存在，返回空数据
    exit(json_encode([
        "code" => 200,
        "msg" => "获取成功",
        "data" => [
            "total" => 0,
            "page" => $page,
            "limit" => $limit,
            "list" => []
        ]
    ]));
}

// 获取总记录数
$sql_count = "SELECT COUNT(*) as total FROM emojis e WHERE $where";
$stmt_count = $conn->prepare($sql_count);

if (!empty($types)) {
    $stmt_count->bind_param($types, ...$params);
}

$stmt_count->execute();
$result_count = $stmt_count->get_result();
$total = $result_count->fetch_assoc()['total'];

// 没有数据，直接返回
if ($total == 0) {
    exit(json_encode([
        "code" => 200,
        "msg" => "获取成功",
        "data" => [
            "total" => 0,
            "page" => $page,
            "limit" => $limit,
            "list" => []
        ]
    ]));
}

// 获取表情包列表
$sql_list = "SELECT e.*, u.username as uploader_username, u.name as uploader_name, u.img as uploader_avatar 
            FROM emojis e
            LEFT JOIN user u ON e.user_id = u.id
            WHERE $where
            ORDER BY e.created_at DESC
            LIMIT ?, ?";

$stmt_list = $conn->prepare($sql_list);
$params[] = $offset;
$params[] = $limit;
$types .= "ii";
$stmt_list->bind_param($types, ...$params);
$stmt_list->execute();
$result_list = $stmt_list->get_result();

$list = [];
while ($row = $result_list->fetch_assoc()) {
    // 处理用户头像默认值
    if (empty($row['uploader_avatar'])) {
        $row['uploader_avatar'] = './assets/img/default-avatar.png';
    }
    
    // 格式化上传时间
    $row['upload_time'] = date('Y-m-d H:i', strtotime($row['created_at']));
    
    $list[] = [
        'id' => $row['id'],
        'name' => $row['name'],
        'file_path' => $row['file_path'],
        'download_count' => $row['download_count'],
        'uploader' => [
            'username' => $row['uploader_username'],
            'name' => $row['uploader_name'] ?: $row['uploader_username'],
            'avatar' => $row['uploader_avatar']
        ],
        'upload_time' => $row['upload_time']
    ];
}

// 返回数据
exit(json_encode([
    "code" => 200,
    "msg" => "获取成功",
    "data" => [
        "total" => $total,
        "page" => $page,
        "limit" => $limit,
        "list" => $list
    ]
])); 