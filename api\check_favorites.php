<?php
// 检查用户收藏状态API
$iteace = "1"; // 设置API状态，避免触发"请设置状态"错误

header('Content-Type: application/json; charset=utf-8');

// 引入配置文件
if (is_file("../config.php")) {
    require "../config.php";
} else {
    exit(json_encode(["code" => "500", "msg" => "配置文件不存在"]));
}

// 引入用户验证
require "../api/wz.php";

// 检查用户是否登录
if ($userdlzt != 1) {
    exit(json_encode(["code" => "401", "msg" => "请先登录"]));
}

// 获取参数
$cids = isset($_GET['cids']) ? $_GET['cids'] : '';
if (empty($cids)) {
    exit(json_encode(["code" => "400", "msg" => "参数错误"]));
}

// 分割文章ID
$cid_array = explode(',', $cids);
$placeholders = implode(',', array_fill(0, count($cid_array), '?'));

// 构建查询
$sql = "SELECT cid FROM favorites WHERE userid = ? AND cid IN ($placeholders)";
$stmt = $conn->prepare($sql);

// 绑定参数
$types = 's' . str_repeat('s', count($cid_array));
$params = array_merge([$types, $user_zh], $cid_array);
call_user_func_array([$stmt, 'bind_param'], $params);

// 执行查询
$stmt->execute();
$result = $stmt->get_result();

// 获取收藏的文章ID
$favorites = [];
while ($row = $result->fetch_assoc()) {
    $favorites[] = $row['cid'];
}

// 返回结果
echo json_encode([
    "code" => "200",
    "msg" => "获取成功",
    "data" => $favorites
]); 