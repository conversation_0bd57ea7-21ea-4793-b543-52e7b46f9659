<?php

// decode by nige112
if ($_SERVER["REQUEST_METHOD"] === "POST") {
    $arr = [["code" => "201", "msg" => "非法请求"]];
    exit(json_encode($arr, JSON_UNESCAPED_UNICODE));
}

// 载入配置文件
$iteace = "0";
if (is_file("./config.php")) {
    require "./config.php";
} else {
    exit("未检测到配置文件：<span style=\"color: red;\">config.php</span>，若您是首次使用请先进行安装,删除文件夹 <span style=\"color: red;\">[install/]</span> 下的 <span style=\"color: red;\">[ins.bak]</span> 文件后进行安装。");
}

require "./api/wz.php";

// 获取支付结果通知的参数
$pid = '1';
$trade_no = $_GET["trade_no"];
$out_trade_no = $_GET["out_trade_no"];
$type = $_GET["type"];
$name = $_GET["name"];
$money = $_GET["money"];
$trade_status = $_GET["trade_status"];
$sign = $_GET["sign"];
$sign_type = $_GET["sign_type"];

// 商户密钥，替换为你实际的商户密钥
$merchantKey = "tuOdzJfjiNIbJu7mpg4RyMrGhvtPBOSX";



// // 重新生成签名字符串用于验证
// // 拼接签名字符串，包含 sign_type
// $signData = "pid=$pid&trade_no=$trade_no&out_trade_no=$out_trade_no&type=$type&name=$name&money=$money&trade_status=$trade_status$merchantKey";
// // print_r($signData);die;
// // 计算 MD5 签名
// $expectedSign = md5($signData);
// print_r($sign.'---'.$expectedSign);die;
// // 验证签名是否正确
// if ($sign !== $expectedSign) {
//     $arr = [["code" => "403", "msg" => "签名验证失败"]];
//     exit(json_encode($arr, JSON_UNESCAPED_UNICODE));
// }

// 假设通过用户订单号（out_trade_no）能获取到对应的用户信息，这里先简单示例通过查询用户表获取用户ID
// 实际应用中可能需要根据你的业务逻辑更准确地获取用户信息，比如关联订单表和用户表等操作
$sqlGetUser = "SELECT name FROM orders WHERE out_trade_no = '{$out_trade_no}'";
$resultGetUser = $conn->query($sqlGetUser);

if ($resultGetUser->num_rows > 0) {
    $row = $resultGetUser->fetch_assoc();
    $user_id = $row["name"];
// print_r($row);die;
    // 查询当前用户的积分
    $sqlGetJifen = "SELECT jifen FROM user WHERE username = '{$user_id}'";
    $resultGetJifen = $conn->query($sqlGetJifen);

    if ($resultGetJifen->num_rows > 0) {
        $rowJifen = $resultGetJifen->fetch_assoc();
        $currentJifen = $rowJifen["jifen"];

        // 只有支付状态为TRADE_SUCCESS时才增加积分
        if ($trade_status === "TRADE_SUCCESS") {
            // 更新用户积分，增加17个积分
            $newJifen = $currentJifen + 17;
            $sqlUpdateJifen = "UPDATE user SET jifen = '{$newJifen}' WHERE username = '{$user_id}'";
            $resultUpdateJifen = $conn->query($sqlUpdateJifen);

            if ($resultUpdateJifen) {
                // 支付成功，更新订单状态为成功（假设成功状态用 'SUCCESS' 表示）
                $sqlUpdateOrderStatus = "UPDATE orders SET trade_status = 'SUCCESS', update_time = NOW() WHERE out_trade_no = '{$out_trade_no}'";
                $resultUpdateOrderStatus = $conn->query($sqlUpdateOrderStatus);

                if ($resultUpdateOrderStatus) {
                    echo 'success';
                    // $arr = [["code" => "200", "msg" => "支付成功，已为用户增加17个积分，订单状态已更新"]];
                    // exit(json_encode($arr, JSON_UNESCAPED_UNICODE));
                } else {
                    $arr = [["code" => "500", "msg" => "订单状态更新失败，请稍后重试"]];
                    exit(json_encode($arr, JSON_UNESCAPED_UNICODE));
                }
            } else {
                $arr = [["code" => "500", "msg" => "积分更新失败，请稍后重试"]];
                exit(json_encode($arr, JSON_UNESCAPED_UNICODE));
            }
        } else {
            $arr = [["code" => "200", "msg" => "支付未成功，未增加积分"]];
            exit(json_encode($arr, JSON_UNESCAPED_UNICODE));
        }
    } else {
        $arr = [["code" => "404", "msg" => "未找到用户积分信息"]];
        exit(json_encode($arr, JSON_UNESCAPED_UNICODE));
    }
} else {
    $arr = [["code" => "404", "msg" => "未找到对应的用户信息"]];
    exit(json_encode($arr, JSON_UNESCAPED_UNICODE));
}

$conn->close();
?>
