<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>谦友位置共享</title>
    <script type="text/javascript" src="https://api.map.baidu.com/api?v=3.0&ak=fA0EPXI6EzL9aQjSyUz8Oowpl6fpwhW7"></script>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no minimal-ui">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="./assets/css/footer-menu.css">
    <style>
        body, html {
            margin: 0;
            padding: 0;
            height: 100%;
            width: 100%;
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
            color: #333;
        }
        
        .map-container {
            width: 100%;
            height: 100vh;
            position: relative;
        }
        
        #map {
            width: 100%;
            height: 100%;
        }
        
        .map-header {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background-color: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            display: flex;
            align-items: center;
            padding: 0 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 100;
        }
        
        .map-title {
            flex: 1;
            font-size: 18px;
            font-weight: 600;
            text-align: center;
        }
        
        .map-back {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background-color: #f0f0f0;
            cursor: pointer;
        }
        
        .map-back i {
            font-size: 20px;
        }
        
        .map-controls {
            position: absolute;
            bottom: 80px;
            right: 15px;
            display: flex;
            flex-direction: column;
            gap: 10px;
            z-index: 100;
        }
        
        .map-control-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }
        
        .map-control-btn i {
            font-size: 20px;
            color: #333;
        }
        
        .map-filter {
            position: absolute;
            bottom: 80px;
            left: 15px;
            background-color: white;
            border-radius: 20px;
            padding: 10px 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            z-index: 100;
        }
        
        .map-filter-label {
            margin-right: 10px;
            font-size: 14px;
        }
        
        .map-filter-select {
            border: none;
            background-color: #f0f0f0;
            padding: 5px 10px;
            border-radius: 10px;
            font-size: 14px;
            outline: none;
        }
        
        .map-loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: rgba(0,0,0,0.7);
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            font-size: 14px;
            z-index: 200;
            display: none;
        }
        
        .map-user-info {
            position: absolute;
            bottom: 80px;
            left: 50%;
            transform: translateX(-50%);
            background-color: white;
            border-radius: 15px;
            padding: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            width: 80%;
            max-width: 300px;
            z-index: 150;
            display: none;
        }
        
        .user-info-header {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 10px;
        }
        
        .user-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .user-name {
            font-weight: 600;
            font-size: 16px;
        }
        
        .user-info-content {
            font-size: 14px;
            color: #666;
        }
        
        .user-info-actions {
            display: flex;
            justify-content: flex-end;
            margin-top: 10px;
        }
        
        .user-info-btn {
            padding: 5px 15px;
            border-radius: 15px;
            background-color: #f0f0f0;
            color: #333;
            font-size: 14px;
            margin-left: 10px;
            cursor: pointer;
        }
        
        .user-info-btn.primary {
            background-color: #1e88e5;
            color: white;
        }
        
        .next-batch-btn {
            position: absolute;
            bottom: 140px;
            left: 50%;
            transform: translateX(-50%);
            background-color: #1e88e5;
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            font-size: 14px;
            box-shadow: 0 5px 15px rgba(30, 136, 229, 0.3);
            z-index: 100;
            cursor: pointer;
        }
        
        /* 底部菜单样式 */
        .footer-padding {
            height: 60px;
        }
        
        /* 暗黑模式适配 */
        .dark-theme {
            color: #eee;
            background-color: #121212;
        }
        
        .dark-theme .map-header {
            background-color: rgba(30, 30, 30, 0.9);
        }
        
        .dark-theme .map-back {
            background-color: #333;
        }
        
        .dark-theme .map-back i {
            color: #eee;
        }
        
        .dark-theme .map-control-btn {
            background-color: #333;
        }
        
        .dark-theme .map-control-btn i {
            color: #eee;
        }
        
        .dark-theme .map-filter {
            background-color: #333;
        }
        
        .dark-theme .map-filter-select {
            background-color: #444;
            color: #eee;
        }
        
        .dark-theme .map-user-info {
            background-color: #333;
            color: #eee;
        }
        
        .dark-theme .user-info-content {
            color: #bbb;
        }
        
        .dark-theme .user-info-btn {
            background-color: #444;
            color: #eee;
        }
    </style>
</head>
<body class="<?php if (isset($_COOKIE["dark_theme"]) && $_COOKIE["dark_theme"] == "dark-theme") echo "dark-theme"; ?>">
    <div class="map-container">
        <!-- 地图头部 -->
        <div class="map-header">
            <div class="map-back" onclick="history.back()">
                <i class="ri-arrow-left-s-line"></i>
            </div>
            <div class="map-title">谦友地图</div>
            <div style="width: 40px;"></div>
        </div>
        
        <!-- 地图容器 -->
        <div id="map"></div>
        
        <!-- 地图控制按钮 -->
        <div class="map-controls">
            <div class="map-control-btn" onclick="locateMe()">
                <i class="ri-map-pin-user-fill"></i>
            </div>
            <div class="map-control-btn" onclick="zoomIn()">
                <i class="ri-zoom-in-line"></i>
            </div>
            <div class="map-control-btn" onclick="zoomOut()">
                <i class="ri-zoom-out-line"></i>
            </div>
            <div class="map-control-btn" onclick="refreshMap()">
                <i class="ri-refresh-line"></i>
            </div>
        </div>
        
        <!-- 地图筛选 -->
        <div class="map-filter">
            <span class="map-filter-label">显示范围:</span>
            <select class="map-filter-select" onchange="changeRadius(this.value)">
                <option value="all">全部</option>
                <option value="province">当前省份</option>
                <option value="city">当前城市</option>
                <option value="nearby">附近10km</option>
            </select>
        </div>
        
        <!-- 加载下一批按钮 -->
        <div class="next-batch-btn" onclick="loadNextBatch()">
            <i class="ri-refresh-line"></i> 换一批谦友
        </div>
        
        <!-- 加载提示 -->
        <div class="map-loading" id="loading">加载中...</div>
        
        <!-- 用户信息卡片 -->
        <div class="map-user-info" id="userInfo">
            <div class="user-info-header">
                <div class="user-avatar">
                    <img src="./assets/img/default-avatar.png" id="userAvatar" alt="用户头像">
                </div>
                <div class="user-name" id="userName">用户名</div>
            </div>
            <div class="user-info-content">
                <p id="userLocation">位置: 正在获取...</p>
            </div>
            <div class="user-info-actions">
                <div class="user-info-btn" onclick="closeUserInfo()">关闭</div>
                <div class="user-info-btn primary" onclick="visitUserProfile()">查看主页</div>
            </div>
        </div>
    </div>
    
    <!-- 底部填充，避免内容被底部菜单遮挡 -->
    <div class="footer-padding"></div>
    
    <!-- 底部菜单 -->
    <div class="footer-menu">
        <a href="./index.php" class="footer-menu-item">
            <i class="ri-home-4-line"></i>
            <span>首页</span>
        </a>
        <a href="./discover.php" class="footer-menu-item active">
            <i class="ri-compass-3-line"></i>
            <span>发现</span>
        </a>
        <a href="./notify.php" class="footer-menu-item">
            <i class="ri-notification-2-line"></i>
            <span>通知</span>
            <?php
            // 检查是否有未读通知
            if (isset($userdlzt) && $userdlzt == 1) {
                require "./config.php";
                $conn = new mysqli($servername, $username, $password, $dbname);
                if ($conn->connect_error) {
                    // 连接失败不显示红点
                } else {
                    $sql_unread = "SELECT COUNT(*) as count FROM notifications WHERE user_to = '{$user_zh}' AND is_read = 0";
                    $result_unread = $conn->query($sql_unread);
                    if ($result_unread && $row_unread = $result_unread->fetch_assoc()) {
                        $unread_count = $row_unread['count'];
                        if ($unread_count > 0) {
                            echo '<div class="notification-dot">' . ($unread_count > 99 ? '99+' : $unread_count) . '</div>';
                        }
                    }
                }
            }
            ?>
        </a>
        <a href="./home.php" class="footer-menu-item">
            <i class="ri-user-3-line"></i>
            <span>我的</span>
        </a>
    </div>
    
    <input type="hidden" id="page" value="0">
    
    <script type="text/javascript">
        // 全局变量
        var map;
        var markers = [];
        var markerClusterer = null;
        var currentPage = 0;
        var currentFilter = 'all';
        var myLocation = null;
        var selectedUserId = null;
        var loadingElement = document.getElementById('loading');
        var userInfoElement = document.getElementById('userInfo');
        
        // 初始化地图
        function initMap() {
            map = new BMap.Map("map");
            map.centerAndZoom(new BMap.Point(105, 35), 5); // 初始地图设置为中国中心
            map.enableScrollWheelZoom(true);  // 启用滚轮缩放
            map.addControl(new BMap.NavigationControl({type: BMAP_NAVIGATION_CONTROL_ZOOM}));  // 添加缩放控件
            
            // 设置地图样式，隐藏道路线条并显示省份轮廓
            map.setMapStyle({
                styleJson: [
                    {
                        "featureType": "land", 
                        "elementType": "geometry", 
                        "stylers": {
                            "color": "#f0f0f0"
                        }
                    },
                    {
                        "featureType": "province", 
                        "elementType": "geometry",
                        "stylers": {
                            "color": "#000",
                            "weight": 1,
                        }
                    },
                    {
                        "featureType": "district",
                        "elementType": "labels",
                        "stylers": {
                            "visibility": "off"
                        }
                    },
                    {
                        "featureType": "road",
                        "elementType": "geometry",
                        "stylers": {
                            "visibility": "off"
                        }
                    },
                    {
                        "featureType": "water",
                        "elementType": "geometry",
                        "stylers": {
                            "color": "#d1e9f2"
                        }
                    }
                ]
            });
            
            // 获取当前位置
            locateMe();
            
            // 加载用户数据
            loadUserData(0);
        }
        
        // 定位到我的位置
        function locateMe() {
            showLoading();
            
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(function(position) {
                    // 将GPS坐标转换为百度地图坐标
                    var gpsPoint = new BMap.Point(position.coords.longitude, position.coords.latitude);
                    
                    var convertor = new BMap.Convertor();
                    var pointArr = [];
                    pointArr.push(gpsPoint);
                    convertor.translate(pointArr, 1, 5, function(data) {
                        if (data.status === 0) {
                            var point = data.points[0];
                            myLocation = point;
                            
                            // 移动地图到当前位置
                            map.centerAndZoom(point, 12);
                            
                            // 添加当前位置标记
                            var marker = new BMap.Marker(point, {
                                icon: new BMap.Icon(
                                    'data:image/svg+xml;base64,' + btoa(
                                        '<svg width="30" height="30" xmlns="http://www.w3.org/2000/svg">' +
                                            '<circle cx="15" cy="15" r="12" fill="#4285f4" stroke="#fff" stroke-width="2"/>' +
                                            '<circle cx="15" cy="15" r="5" fill="#fff"/>' +
                                        '</svg>'),
                                    new BMap.Size(30, 30)
                                )
                            });
                            map.addOverlay(marker);
                            
                            // 添加精度圈
                            var circle = new BMap.Circle(point, position.coords.accuracy, {
                                strokeColor: "#4285f4",
                                strokeWeight: 1,
                                strokeOpacity: 0.3,
                                fillColor: "#4285f4",
                                fillOpacity: 0.1
                            });
                            map.addOverlay(circle);
                            
                            // 如果筛选是附近，重新加载数据
                            if (currentFilter === 'nearby') {
                                loadUserData(0);
                            }
                        }
                        hideLoading();
                    });
                }, function(error) {
                    console.error("定位失败: ", error.message);
                    hideLoading();
                    alert("无法获取您的位置，请检查位置权限设置。");
                });
            } else {
                hideLoading();
                alert("您的浏览器不支持地理定位。");
            }
        }
        
        // 加载用户数据并绘制到地图
        function loadUserData(page) {
            showLoading();
            currentPage = page;
            document.getElementById('page').value = page;
            
            // 清除现有标记
            clearMarkers();
            
            var xhr = new XMLHttpRequest();
            xhr.open('GET', './api/getUserLocations.php?page=' + page, true);
            xhr.onreadystatechange = function () {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        try {
                            var userLocations = JSON.parse(xhr.responseText);
                            
                            // 根据筛选条件过滤用户
                            var filteredLocations = filterUsersByLocation(userLocations);
                            
                            // 添加用户标记
                            addUserMarkers(filteredLocations);
                            
                            // 创建点聚合
                            createMarkerClusterer();
                        } catch (e) {
                            console.error("解析数据失败: ", e);
                        }
                    } else {
                        console.error("请求失败: ", xhr.status);
                    }
                    hideLoading();
                }
            };
            xhr.send();
        }
        
        // 根据位置筛选用户
        function filterUsersByLocation(users) {
            if (currentFilter === 'all' || !myLocation) {
                return users;
            }
            
            return users.filter(function(user) {
                var userPoint = new BMap.Point(user.longitude, user.latitude);
                var distance = map.getDistance(myLocation, userPoint);
                
                switch (currentFilter) {
                    case 'nearby':
                        return distance <= 10000; // 10km内
                    case 'city':
                        // 这里简化处理，实际应该根据地理编码判断城市
                        return distance <= 30000; // 30km作为同城近似值
                    case 'province':
                        // 这里简化处理，实际应该根据地理编码判断省份
                        return distance <= 200000; // 200km作为同省近似值
                    default:
                        return true;
                }
            });
        }
        
        // 添加用户标记
        function addUserMarkers(users) {
            users.forEach(function(user) {
                var point = new BMap.Point(user.longitude, user.latitude);
                
                // 创建自定义头像标记
                var avatarIcon = new BMap.Icon(
                    user.avatar,
                    new BMap.Size(40, 40),
                    {
                        imageSize: new BMap.Size(40, 40),
                        anchor: new BMap.Size(20, 20)
                    }
                );
                
                var marker = new BMap.Marker(point, {
                    icon: avatarIcon
                });
                
                // 设置标记的圆形样式
                var style = document.createElement('style');
                style.type = 'text/css';
                style.innerHTML = '.avatar-marker img { border-radius: 50%; border: 2px solid white; box-shadow: 0 2px 5px rgba(0,0,0,0.2); }';
                document.getElementsByTagName('head')[0].appendChild(style);
                
                marker.setZIndex(100);
                marker.customData = user;
                
                // 添加点击事件
                marker.addEventListener('click', function() {
                    showUserInfo(this.customData);
                });
                
                map.addOverlay(marker);
                markers.push(marker);
            });
        }
        
        // 创建标记聚合
        function createMarkerClusterer() {
            if (markerClusterer) {
                markerClusterer.clearMarkers();
            }
            
            // 使用百度地图的MarkerClusterer
            markerClusterer = new BMapLib.MarkerClusterer(map, {
                markers: markers,
                girdSize: 100,
                maxZoom: 13,
                styles: [{
                    url: 'data:image/svg+xml;base64,' + btoa(
                        '<svg width="50" height="50" xmlns="http://www.w3.org/2000/svg">' +
                            '<circle cx="25" cy="25" r="20" fill="#1e88e5" stroke="#fff" stroke-width="2"/>' +
                            '<text x="25" y="30" font-size="14" text-anchor="middle" fill="#fff">$</text>' +
                        '</svg>'
                    ),
                    size: new BMap.Size(50, 50),
                    textSize: '14',
                    textColor: '#fff'
                }]
            });
        }
        
        // 清除所有标记
        function clearMarkers() {
            markers.forEach(function(marker) {
                map.removeOverlay(marker);
            });
            markers = [];
            
            if (markerClusterer) {
                markerClusterer.clearMarkers();
            }
        }
        
        // 显示用户信息
        function showUserInfo(user) {
            selectedUserId = user.id;
            document.getElementById('userName').textContent = user.name;
            document.getElementById('userAvatar').src = user.avatar;
            document.getElementById('userLocation').textContent = '位置: ' + (user.province || '') + ' ' + (user.city || '');
            userInfoElement.style.display = 'block';
        }
        
        // 关闭用户信息
        function closeUserInfo() {
            userInfoElement.style.display = 'none';
            selectedUserId = null;
        }
        
        // 访问用户主页
        function visitUserProfile() {
            if (selectedUserId) {
                window.location.href = './user.php?user=' + selectedUserId;
            }
        }
        
        // 放大地图
        function zoomIn() {
            map.zoomIn();
        }
        
        // 缩小地图
        function zoomOut() {
            map.zoomOut();
        }
        
        // 刷新地图
        function refreshMap() {
            loadUserData(currentPage);
        }
        
        // 加载下一批用户
        function loadNextBatch() {
            loadUserData(currentPage + 1);
        }
        
        // 改变显示范围
        function changeRadius(value) {
            currentFilter = value;
            loadUserData(0);
        }
        
        // 显示加载中
        function showLoading() {
            loadingElement.style.display = 'block';
        }
        
        // 隐藏加载中
        function hideLoading() {
            loadingElement.style.display = 'none';
        }
        
        // 初始化
        window.onload = function() {
            // 加载百度地图聚合插件
            var script = document.createElement('script');
            script.src = 'https://api.map.baidu.com/library/MarkerClusterer/1.2/src/MarkerClusterer_min.js';
            document.body.appendChild(script);
            
            script.onload = function() {
                initMap();
            };
        };
    </script>
</body>
</html>
