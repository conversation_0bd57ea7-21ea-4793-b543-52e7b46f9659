<?php

require dirname(__FILE__, 2) . '/vendor/autoload.php';

$secretId = "SECRETID"; //替换为用户的 secretId，请登录访问管理控制台进行查看和管理，https://console.cloud.tencent.com/cam/capi
$secretKey = "SECRETKEY"; //替换为用户的 secretKey，请登录访问管理控制台进行查看和管理，https://console.cloud.tencent.com/cam/capi
$region = "ap-beijing"; //替换为用户的 region，已创建桶归属的region可以在控制台查看，https://console.cloud.tencent.com/cos5/bucket
$cosClient = new Qcloud\Cos\Client(array(
    'region' => $region,
    'scheme' => 'https', //协议头部，默认为http
    'credentials'=> array(
        'secretId'  => $secretId,
        'secretKey' => $secretKey
    )
));
try { 
    $result = $cosClient->selectObjectContent(array(
        'Bucket' => 'examplebucket-125000000', //存储桶名称，由BucketName-Appid 组成，可以在COS控制台查看 https://console.cloud.tencent.com/cos5/bucket
        'Key' => 'exampleobject',
        'Expression' => 'Select * from COSObject s', 
        'ExpressionType' => 'SQL', 
        'InputSerialization' => array( 
            'CompressionType' => 'None', 
            'CSV' => array( 
                'FileHeaderInfo' => 'NONE', 
                'RecordDelimiter' => '\n', 
                'FieldDelimiter' => ',', 
                'QuoteEscapeCharacter' => '"', 
                'Comments' => '#', 
                'AllowQuotedRecordDelimiter' => 'FALSE' 
                )   
            ),  
        'OutputSerialization' => array( 
            'CSV' => array( 
                'QuoteField' => 'ASNEEDED', 
                'RecordDelimiter' => '\n', 
                'FieldDelimiter' => ',', 
                'QuoteCharacter' => '"', 
                'QuoteEscapeCharacter' => '"' 
                )   
            ),  
        'RequestProgress' => array( 
                'Enabled' => 'FALSE' 
        )   
    ));  
    // 请求成功
    foreach ($result['Data'] as $data) { 
        // 迭代遍历select结果
        print_r($data); 
    }
} catch (\Exception $e) {
    // 请求失败
    echo($e); 
}

try { 
    $result = $cosClient->selectObjectContent(array(
        'Bucket' => 'examplebucket-125000000', //存储桶名称，由BucketName-Appid 组成，可以在COS控制台查看 https://console.cloud.tencent.com/cos5/bucket
        'Key' => 'exampleobject',
        'Expression' => 'Select * from COSObject s', 
        'ExpressionType' => 'SQL', 
        'InputSerialization' => array( 
            'CompressionType' => 'None', 
            'JSON' => array( 
                'Type' => 'DOCUMENT'
                )   
            ),  
        'OutputSerialization' => array( 
            'JSON' => array( 
                'RecordDelimiter' => '\n', 
                )   
            ),  
        'RequestProgress' => array( 
            'Enabled' => 'FALSE' 
        )   
    ));  
    // 请求成功
    foreach ($result['Data'] as $data) { 
        // 迭代遍历select结果
        print_r($data); 
    }
} catch (\Exception $e) {
    // 请求失败
    echo($e); 
}
