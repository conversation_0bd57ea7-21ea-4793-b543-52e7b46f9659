<?php
// 设置响应头
header('Content-Type: application/json');

// 检查请求方法是否为POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // 获取POST数据
    $data = json_decode(file_get_contents('php://input'), true);
    $songName = isset($data['song_name'])? trim($data['song_name']) : '';

    // 检查歌曲名称是否为空
    if (empty($songName)) {
        echo json_encode(['error' => '请提供歌曲名称.']);
        exit;
    }
 
    // URL编码歌曲名称
    $encodedSongName = urlencode($songName);
 
    // API请求URL，用于搜索歌曲
    $searchUrl = "http://music.163.com/api/search/get/web?csrf_token=&hlpretag=&hlposttag=&s={$encodedSongName}&type=1&offset=0&total=true&limit=3";

    // 使用cURL发起搜索请求
    $chSearch = curl_init();
    curl_setopt($chSearch, CURLOPT_URL, $searchUrl);
    curl_setopt($chSearch, CURLOPT_RETURNTRANSFER, true);

    // 执行搜索请求
    $searchResponse = curl_exec($chSearch);

    // 检查搜索请求是否成功
    if ($searchResponse === false) {
        echo json_encode(['error' => '搜索请求失败: '. curl_error($chSearch)]);
        curl_close($chSearch);
        exit;
    }

    // 关闭搜索请求的cURL会话
    curl_close($chSearch);

    // 解析搜索结果，获取歌曲ID列表
    $searchResult = json_decode($searchResponse, true);
    if (isset($searchResult['result']['songs']) &&!empty($searchResult['result']['songs'])) {
        $songIds = array_column($searchResult['result']['songs'], 'id');

        // 用于存储所有歌曲详细信息的数组
        $allSongInfo = [];

        // 遍历每一个歌曲ID，获取详细信息
        foreach ($songIds as $songId) {
            // API请求URL，用于获取歌曲详细信息（包括封面等）
            $detailUrl = "http://music.163.com/api/song/detail/?id={$songId}&ids=%5B{$songId}%5D";

            // 使用cURL发起获取歌曲详细信息的请求
            $chDetail = curl_init();
            curl_setopt($chDetail, CURLOPT_URL, $detailUrl);
            curl_setopt($chDetail, CURLOPT_RETURNTRANSFER, true);

            // 执行获取歌曲详细信息的请求
            $detailResponse = curl_exec($chDetail);

            // 检查获取歌曲详细信息的请求是否成功
            if ($detailResponse === false) {
                echo json_encode(['error' => '获取歌曲详细信息请求失败: '. curl_error($chDetail)]);
                curl_close($chDetail);
                exit;
            }

            // 关闭获取歌曲详细信息的请求的cURL会话
            curl_close($chDetail);

            // 解析获取歌曲详细信息的结果，提取封面URL（blurPicUrl）等相关信息
            $detailResult = json_decode($detailResponse, true);
            if (isset($detailResult['songs']) &&!empty($detailResult['songs'])) {
                $songInfo = [
                    'id' => $detailResult['songs'][0]['id'],
                    'name' => $detailResult['songs'][0]['name'],
                    'artists' => $detailResult['songs'][0]['artists'],
                    'blurPicUrl' => $detailResult['songs'][0]['album']['blurPicUrl'],
                ];

                $allSongInfo[] = $songInfo;
            } else {
                echo json_encode(['error' => '未获取到歌曲详细信息.']);
            }
        }

        // 输出包含所有歌曲详细信息的数组
        echo json_encode(['result' => $allSongInfo]);
    } else {
        echo json_encode(['error' => '未搜索到相关歌曲.']);
    }
} else {
    echo json_encode(['error' => '仅支持POST请求.']);
}
?>