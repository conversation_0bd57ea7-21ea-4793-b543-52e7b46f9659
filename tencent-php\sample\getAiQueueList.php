<?php

require dirname(__FILE__, 2) . '/vendor/autoload.php';

$secretId = "SECRETID"; //替换为用户的 secretId，请登录访问管理控制台进行查看和管理，https://console.cloud.tencent.com/cam/capi
$secretKey = "SECRETKEY"; //替换为用户的 secretKey，请登录访问管理控制台进行查看和管理，https://console.cloud.tencent.com/cam/capi
$region = "ap-beijing"; //替换为用户的 region，已创建桶归属的region可以在控制台查看，https://console.cloud.tencent.com/cos5/bucket
$cosClient = new Qcloud\Cos\Client(
    array(
        'region' => $region,
        'scheme' => 'https', // 万象接口必须为https
        'credentials' => array(
            'secretId' => $secretId,
            'secretKey' => $secretKey)));
try {
    // 搜索 AI 内容识别队列 https://cloud.tencent.com/document/product/460/79394
    $result = $cosClient->getAiQueueList(array(
        'Bucket' => 'examplebucket-1250000000', //存储桶名称，由BucketName-Appid 组成，可以在COS控制台查看 https://console.cloud.tencent.com/cos5/bucket
//        'QueueIds' => 'xxx', // 队列 ID，以“,”符号分割字符串
//        'State' => 'Active', // Active 表示队列内的作业会被媒体处理服务调度执行, Paused 表示队列暂停，作业不再会被媒体处理调度执行，队列内的所有作业状态维持在暂停状态，已经执行中的任务不受影响
//        'PageNumber' => '1', // 第几页
//        'PageSize' => '10', // 每页个数
    ));
    print_r($result);
} catch (\Exception $e) {
    // 请求失败
    echo($e);
}
