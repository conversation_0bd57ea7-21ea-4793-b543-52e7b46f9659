<?php
// 签到功能处理文件
$iteace = "1"; // 设置API状态，避免触发"请设置状态"错误

header('Content-Type: application/json; charset=utf-8');

// 检查请求方法
if ($_SERVER["REQUEST_METHOD"] !== "POST") {
    exit(json_encode(["code" => 400, "msg" => "请求方法不正确"]));
}

// 引入配置文件
if (is_file("../config.php")) {
    require "../config.php";
} else {
    exit(json_encode(["code" => 500, "msg" => "配置文件不存在"]));
}

// 引入用户验证
require "../api/wz.php";

// 检查用户是否登录
if ($userdlzt != 1) {
    exit(json_encode(["code" => 401, "msg" => "请先登录"]));
}

$username = $user_zh; // 使用全局用户名变量

// 检查今天是否已签到
$today_date = date('Y-m-d');

// 首先检查sign表中是否已签到（兼容旧表）
$sql_check_old = "SELECT id FROM sign WHERE name = '{$username}' AND time LIKE '{$today_date}%'";
$result_check_old = $conn->query($sql_check_old);

if ($result_check_old && $result_check_old->num_rows > 0) {
    exit(json_encode(["code" => 400, "msg" => "今天已经签到过了"]));
}

// 再检查user表中的签到时间（兼容旧表）
$sql_check_user = "SELECT id FROM user WHERE username = '{$username}' AND signtime LIKE '{$today_date}%'";
$result_check_user = $conn->query($sql_check_user);

if ($result_check_user && $result_check_user->num_rows > 0) {
    exit(json_encode(["code" => 400, "msg" => "今天已经签到过了"]));
}

// 最后检查user_sign表（新表）
// 检查user_sign表是否存在
$sql_check_table = "SHOW TABLES LIKE 'user_sign'";
$result_check_table = $conn->query($sql_check_table);
$user_sign_exists = ($result_check_table && $result_check_table->num_rows > 0);

if ($user_sign_exists) {
    $sql_check = "SELECT id FROM user_sign WHERE username = '{$username}' AND DATE(sign_date) = '{$today_date}'";
    $result_check = $conn->query($sql_check);

    if ($result_check && $result_check->num_rows > 0) {
        exit(json_encode(["code" => 400, "msg" => "今天已经签到过了"]));
    }
}

// 开始事务
$conn->begin_transaction();

try {
    // 获取用户当前积分
    $sql_user = "SELECT jifen FROM user WHERE username = '{$username}'";
    $result_user = $conn->query($sql_user);
    
    if (!$result_user) {
        throw new Exception("数据库查询错误: " . $conn->error);
    }
    
    if ($result_user->num_rows === 0) {
        // 记录错误日志
        error_log("签到失败: 用户 {$username} 不存在");
        throw new Exception("用户不存在，请重新登录后再试");
    }
    
    $user_data = $result_user->fetch_assoc();
    $current_points = intval($user_data['jifen'] ?? 0);
    
    // 计算连续签到天数
    $continuous_days = 1;
    $yesterday = date('Y-m-d', strtotime('-1 day'));
    
    // 检查user_sign_stats表是否存在
    $sql_check_stats_table = "SHOW TABLES LIKE 'user_sign_stats'";
    $result_check_stats_table = $conn->query($sql_check_stats_table);
    $user_sign_stats_exists = ($result_check_stats_table && $result_check_stats_table->num_rows > 0);

    if (!$user_sign_stats_exists) {
        // 创建user_sign_stats表
        $sql_create_stats = "CREATE TABLE user_sign_stats (
            id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(255) NOT NULL,
            continuous_days INT(11) NOT NULL DEFAULT '1',
            last_sign_date DATE NOT NULL,
            created_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX(username(255))
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC";
        
        if (!$conn->query($sql_create_stats)) {
            throw new Exception("创建签到统计表失败: " . $conn->error);
        }
    }

    // 检查user_sign表是否存在
    if (!$user_sign_exists) {
        // 创建user_sign表
        $sql_create_sign = "CREATE TABLE user_sign (
            id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(255) NOT NULL,
            sign_date DATETIME NOT NULL,
            jifen INT(11) NOT NULL DEFAULT '0',
            created_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
            INDEX(username(255))
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC";
        
        if (!$conn->query($sql_create_sign)) {
            throw new Exception("创建签到记录表失败: " . $conn->error);
        }
    }
    
    // 检查用户是否有签到统计记录
    $sql_stats = "SELECT continuous_days, last_sign_date FROM user_sign_stats WHERE username = '{$username}'";
    $result_stats = $conn->query($sql_stats);
    
    if ($result_stats && $result_stats->num_rows > 0) {
        $stats_data = $result_stats->fetch_assoc();
        $last_sign_date = $stats_data['last_sign_date'];
        
        // 如果上次签到是昨天，连续签到天数+1
        if ($last_sign_date === $yesterday) {
            $continuous_days = $stats_data['continuous_days'] + 1;
        } else if ($last_sign_date !== $today_date) {
            // 如果不是昨天也不是今天，重置为1
            $continuous_days = 1;
        } else {
            // 如果是今天，保持不变（不应该走到这里，前面已经检查过了）
            $continuous_days = $stats_data['continuous_days'];
        }
    }
    
    // 计算签到奖励积分
    $base_points = 5; // 基础积分
    $bonus_points = 0; // 连续签到奖励
    
    // 连续签到奖励规则
    if ($continuous_days >= 30) {
        $bonus_points = 20;
    } else if ($continuous_days >= 15) {
        $bonus_points = 15;
    } else if ($continuous_days >= 7) {
        $bonus_points = 10;
    } else if ($continuous_days >= 3) {
        $bonus_points = 5;
    }
    
    $total_points = $base_points + $bonus_points;
    $new_points = $current_points + $total_points;
    
    // 记录签到到新表
    $sql_sign = "INSERT INTO user_sign (username, sign_date, jifen) VALUES ('{$username}', NOW(), {$total_points})";
    if (!$conn->query($sql_sign)) {
        throw new Exception("记录签到失败: " . $conn->error);
    }

    // 同时更新旧表，保持兼容
    $current_time = date("Y-m-d H:i:s");
    $sql_old_sign = "INSERT INTO sign (name, time) VALUES ('{$username}', '{$current_time}')";
    if (!$conn->query($sql_old_sign)) {
        throw new Exception("记录旧签到表失败: " . $conn->error);
    }
    
    // 更新或插入签到统计
    if ($result_stats && $result_stats->num_rows > 0) {
        $sql_update_stats = "UPDATE user_sign_stats SET continuous_days = {$continuous_days}, last_sign_date = '{$today_date}', updated_at = NOW() WHERE username = '{$username}'";
        if (!$conn->query($sql_update_stats)) {
            throw new Exception("更新签到统计失败: " . $conn->error);
        }
    } else {
        $sql_insert_stats = "INSERT INTO user_sign_stats (username, continuous_days, last_sign_date) VALUES ('{$username}', {$continuous_days}, '{$today_date}')";
        if (!$conn->query($sql_insert_stats)) {
            throw new Exception("插入签到统计失败: " . $conn->error);
        }
    }
    
    // 更新用户积分 - 同时更新jifen和points字段
    $sql_update_points = "UPDATE user SET jifen = {$new_points}, points = {$new_points}, signtime = NOW() WHERE username = '{$username}'";
    if (!$conn->query($sql_update_points)) {
        throw new Exception("更新用户积分失败: " . $conn->error);
    }
    
    // 提交事务
    if (!$conn->commit()) {
        throw new Exception("提交事务失败: " . $conn->error);
    }
    
    // 返回成功信息
    $response = [
        "code" => 200,
        "msg" => "签到成功！获得 {$total_points} 积分" . ($bonus_points > 0 ? "（含连续签到奖励 {$bonus_points} 积分）" : ""),
        "jifen" => $new_points,
        "points" => $new_points,
        "continuous_days" => $continuous_days,
        "today_points" => $total_points
    ];
    
    exit(json_encode($response));
    
} catch (Exception $e) {
    // 回滚事务
    $conn->rollback();
    error_log("签到失败: " . $e->getMessage());
    exit(json_encode(["code" => 500, "msg" => "签到失败：" . $e->getMessage()]));
} 