/*!
 * Chart.js v3.0.0-alpha
 * https://www.chartjs.org
 * (c) 2020 Chart.js Contributors
 * Released under the MIT License
 */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t=t||self).Chart=e()}(this,(function(){"use strict";function t(e){return(t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(e)}function e(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function i(t,e){for(var i=0;i<e.length;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function n(t,e,n){return e&&i(t.prototype,e),n&&i(t,n),t}function a(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function r(){return(r=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var i=arguments[e];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(t[n]=i[n])}return t}).apply(this,arguments)}function o(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,n)}return i}function s(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&u(t,e)}function l(t){return(l=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function u(t,e){return(u=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}function c(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function h(t,e){return!e||"object"!=typeof e&&"function"!=typeof e?c(t):e}function d(t,e,i){return(d="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,i){var n=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=l(t)););return t}(t,e);if(n){var a=Object.getOwnPropertyDescriptor(n,e);return a.get?a.get.call(i):a.value}})(t,e,i||t)}function f(t){return function(t){if(Array.isArray(t)){for(var e=0,i=new Array(t.length);e<t.length;e++)i[e]=t[e];return i}}(t)||function(t){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t))return Array.from(t)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}function v(){}var p,g=(p=0,function(){return p++});function y(t){return null==t}function m(t){if(Array.isArray&&Array.isArray(t))return!0;var e=Object.prototype.toString.call(t);return"[object"===e.substr(0,7)&&"Array]"===e.substr(-6)}function b(t){return null!==t&&"[object Object]"===Object.prototype.toString.call(t)}var x=function(t){return("number"==typeof t||t instanceof Number)&&isFinite(+t)};function _(t,e){return void 0===t?e:t}function k(t,e,i){if(t&&"function"==typeof t.call)return t.apply(i,e)}function M(t,e,i,n){var a,r,o;if(m(t))if(r=t.length,n)for(a=r-1;a>=0;a--)e.call(i,t[a],a);else for(a=0;a<r;a++)e.call(i,t[a],a);else if(b(t))for(r=(o=Object.keys(t)).length,a=0;a<r;a++)e.call(i,t[o[a]],o[a])}function w(t){if(m(t))return t.map(w);if(b(t)){for(var e={},i=Object.keys(t),n=i.length,a=0;a<n;++a)e[i[a]]=w(t[i[a]]);return e}return t}function S(t,e,i,n){var a=e[t],r=i[t];b(a)&&b(r)?P(a,r,n):e[t]=w(r)}function P(t,e,i){var n=m(e)?e:[e],a=n.length;if(!b(t))return t;for(var r=(i=i||{}).merger||S,o=0;o<a;++o)if(b(e=n[o]))for(var s=Object.keys(e),l=0,u=s.length;l<u;++l)r(s[l],t,e,i);return t}function D(t,e){return P(t,e,{merger:O})}function O(t,e,i){var n=e[t],a=i[t];b(n)&&b(a)?D(n,a):Object.prototype.hasOwnProperty.call(e,t)||(e[t]=w(a))}function A(t){var e=this,i=t&&Object.prototype.hasOwnProperty.call(t,"constructor")?t.constructor:function(){return e.apply(this,arguments)},n=function(){this.constructor=i};return n.prototype=e.prototype,i.prototype=new n,i.extend=A,t&&r(i.prototype,t),i.__super__=e.prototype,i}var T=Object.freeze({__proto__:null,noop:v,uid:g,isNullOrUndef:y,isArray:m,isObject:b,isFinite:x,valueOrDefault:_,valueAtIndexOrDefault:function(t,e,i){return _(m(t)?t[e]:t,i)},callback:k,each:M,arrayEquals:function t(e,i){var n,a,r,o;if(!e||!i||e.length!==i.length)return!1;for(n=0,a=e.length;n<a;++n)if(r=e[n],o=i[n],r instanceof Array&&o instanceof Array){if(!t(r,o))return!1}else if(r!==o)return!1;return!0},_elementsEqual:function(t,e){var i,n,a,r;if(!t||!e||t.length!==e.length)return!1;for(i=0,n=t.length;i<n;++i)if(a=t[i],r=e[i],a.datasetIndex!==r.datasetIndex||a.index!==r.index)return!1;return!0},clone:w,_merger:S,merge:P,mergeIf:D,_mergerIf:O,inherits:A,_deprecated:function(t,e,i,n){void 0!==e&&console.warn(t+': "'+i+'" is deprecated. Please use "'+n+'" instead')}}),C=Math.PI,F=C/180,E=2*C,I=C/2,L=C/4,R=2*C/3;function z(t,e,i,n,a){var r=e[a];return r||(r=e[a]=t.measureText(a).width,i.push(a)),r>n&&(n=r),n}function B(t,e,i,n){var a=(n=n||{}).data=n.data||{},r=n.garbageCollect=n.garbageCollect||[];n.font!==e&&(a=n.data={},r=n.garbageCollect=[],n.font=e),t.save(),t.font=e;var o,s,l,u,c,h=0,d=i.length;for(o=0;o<d;o++)if(null!=(u=i[o])&&!0!==m(u))h=z(t,a,r,h,u);else if(m(u))for(s=0,l=u.length;s<l;s++)null==(c=u[s])||m(c)||(h=z(t,a,r,h,c));t.restore();var f=r.length/2;if(f>i.length){for(o=0;o<f;o++)delete a[r[o]];r.splice(0,f)}return h}function V(t,e,i){var n=t.currentDevicePixelRatio,a=i/2;return Math.round((e-a)*n)/n+a}function W(e,i,n,a){var r,o,s,l,u,c=i.pointStyle,h=i.rotation,d=i.radius,f=(h||0)*F;if(c&&"object"===t(c)&&("[object HTMLImageElement]"===(r=c.toString())||"[object HTMLCanvasElement]"===r))return e.save(),e.translate(n,a),e.rotate(f),e.drawImage(c,-c.width/2,-c.height/2,c.width,c.height),void e.restore();if(!(isNaN(d)||d<=0)){switch(e.beginPath(),c){default:e.arc(n,a,d,0,E),e.closePath();break;case"triangle":e.moveTo(n+Math.sin(f)*d,a-Math.cos(f)*d),f+=R,e.lineTo(n+Math.sin(f)*d,a-Math.cos(f)*d),f+=R,e.lineTo(n+Math.sin(f)*d,a-Math.cos(f)*d),e.closePath();break;case"rectRounded":l=d-(u=.516*d),o=Math.cos(f+L)*l,s=Math.sin(f+L)*l,e.arc(n-o,a-s,u,f-C,f-I),e.arc(n+s,a-o,u,f-I,f),e.arc(n+o,a+s,u,f,f+I),e.arc(n-s,a+o,u,f+I,f+C),e.closePath();break;case"rect":if(!h){l=Math.SQRT1_2*d,e.rect(n-l,a-l,2*l,2*l);break}f+=L;case"rectRot":o=Math.cos(f)*d,s=Math.sin(f)*d,e.moveTo(n-o,a-s),e.lineTo(n+s,a-o),e.lineTo(n+o,a+s),e.lineTo(n-s,a+o),e.closePath();break;case"crossRot":f+=L;case"cross":o=Math.cos(f)*d,s=Math.sin(f)*d,e.moveTo(n-o,a-s),e.lineTo(n+o,a+s),e.moveTo(n+s,a-o),e.lineTo(n-s,a+o);break;case"star":o=Math.cos(f)*d,s=Math.sin(f)*d,e.moveTo(n-o,a-s),e.lineTo(n+o,a+s),e.moveTo(n+s,a-o),e.lineTo(n-s,a+o),f+=L,o=Math.cos(f)*d,s=Math.sin(f)*d,e.moveTo(n-o,a-s),e.lineTo(n+o,a+s),e.moveTo(n+s,a-o),e.lineTo(n-s,a+o);break;case"line":o=Math.cos(f)*d,s=Math.sin(f)*d,e.moveTo(n-o,a-s),e.lineTo(n+o,a+s);break;case"dash":e.moveTo(n,a),e.lineTo(n+Math.cos(f)*d,a+Math.sin(f)*d)}e.fill(),i.borderWidth>0&&e.stroke()}}function N(t,e){return t.x>e.left-.5&&t.x<e.right+.5&&t.y>e.top-.5&&t.y<e.bottom+.5}function H(t,e){t.save(),t.beginPath(),t.rect(e.left,e.top,e.right-e.left,e.bottom-e.top),t.clip()}function j(t){t.restore()}function Y(t,e,i,n,a){if(!e)return t.lineTo(i.x,i.y);if("middle"===a){var r=(e.x+i.x)/2;t.lineTo(r,e.y),t.lineTo(r,i.y)}else"after"===a!=!!n?t.lineTo(e.x,i.y):t.lineTo(i.x,e.y);t.lineTo(i.x,i.y)}function U(t,e,i,n){if(!e)return t.lineTo(i.x,i.y);t.bezierCurveTo(n?e.controlPointPreviousX:e.controlPointNextX,n?e.controlPointPreviousY:e.controlPointNextY,n?i.controlPointNextX:i.controlPointPreviousX,n?i.controlPointNextY:i.controlPointPreviousY,i.x,i.y)}var X=Object.freeze({__proto__:null,_measureText:z,_longestText:B,_alignPixel:V,clear:function(t){t.ctx.clearRect(0,0,t.width,t.height)},drawPoint:W,_isPointInArea:N,clipArea:H,unclipArea:j,_steppedLineTo:Y,_bezierCurveTo:U}),q=Math.PI,$=2*q,G=$+q;function K(t){var e,i=[],n=Math.sqrt(t);for(e=1;e<n;e++)t%e==0&&(i.push(e),i.push(t/e));return n===(0|n)&&i.push(n),i.sort((function(t,e){return t-e})).pop(),i}var Z=Math.log10||function(t){var e=Math.log(t)*Math.LOG10E,i=Math.round(e);return t===Math.pow(10,i)?i:e};function Q(t){return!isNaN(parseFloat(t))&&isFinite(t)}function J(t,e,i){return Math.abs(t-e)<i}function tt(t,e){var i=Math.round(t);return i-e<=t&&i+e>=t}function et(t,e,i){var n,a,r;for(n=0,a=t.length;n<a;n++)r=t[n][i],isNaN(r)||(e.min=Math.min(e.min,r),e.max=Math.max(e.max,r))}var it=Math.sign?function(t){return Math.sign(t)}:function(t){return 0===(t=+t)||isNaN(t)?t:t>0?1:-1};function nt(t){return t*(q/180)}function at(t){return t*(180/q)}function rt(t){if(x(t)){for(var e=1,i=0;Math.round(t*e)/e!==t;)e*=10,i++;return i}}function ot(t,e){var i=e.x-t.x,n=e.y-t.y,a=Math.sqrt(i*i+n*n),r=Math.atan2(n,i);return r<-.5*q&&(r+=$),{angle:r,distance:a}}function st(t,e){return(t-e+G)%$-q}function lt(t){return(t%$+$)%$}function ut(t,e,i){var n=lt(t),a=lt(e),r=lt(i),o=lt(a-n),s=lt(r-n),l=lt(n-a),u=lt(n-r);return n===a||n===r||o>s&&l<u}function ct(t,e,i){return Math.max(e,Math.min(i,t))}var ht=Object.freeze({__proto__:null,_factorize:K,log10:Z,isNumber:Q,almostEquals:J,almostWhole:tt,_setMinAndMaxByKey:et,sign:it,toRadians:nt,toDegrees:at,_decimalPlaces:rt,getAngleFromPoint:ot,distanceBetweenPoints:function(t,e){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))},_angleDiff:st,_normalizeAngle:lt,_angleBetween:ut,_limitValue:ct}),dt=Number.EPSILON||1e-14;function ft(t,e,i,n){var a=t.skip?e:t,r=e,o=i.skip?e:i,s=Math.sqrt(Math.pow(r.x-a.x,2)+Math.pow(r.y-a.y,2)),l=Math.sqrt(Math.pow(o.x-r.x,2)+Math.pow(o.y-r.y,2)),u=s/(s+l),c=l/(s+l),h=n*(u=isNaN(u)?0:u),d=n*(c=isNaN(c)?0:c);return{previous:{x:r.x-h*(o.x-a.x),y:r.y-h*(o.y-a.y)},next:{x:r.x+d*(o.x-a.x),y:r.y+d*(o.y-a.y)}}}function vt(t){var e,i,n,a,r,o,s,l,u,c=(t||[]).map((function(t){return{model:t,deltaK:0,mK:0}})),h=c.length;for(e=0;e<h;++e)if(!(n=c[e]).model.skip){if(i=e>0?c[e-1]:null,(a=e<h-1?c[e+1]:null)&&!a.model.skip){var d=a.model.x-n.model.x;n.deltaK=0!==d?(a.model.y-n.model.y)/d:0}!i||i.model.skip?n.mK=n.deltaK:!a||a.model.skip?n.mK=i.deltaK:it(i.deltaK)!==it(n.deltaK)?n.mK=0:n.mK=(i.deltaK+n.deltaK)/2}for(e=0;e<h-1;++e)n=c[e],a=c[e+1],n.model.skip||a.model.skip||(J(n.deltaK,0,dt)?n.mK=a.mK=0:(r=n.mK/n.deltaK,o=a.mK/n.deltaK,(l=Math.pow(r,2)+Math.pow(o,2))<=9||(s=3/Math.sqrt(l),n.mK=r*s*n.deltaK,a.mK=o*s*n.deltaK)));for(e=0;e<h;++e)(n=c[e]).model.skip||(i=e>0?c[e-1]:null,a=e<h-1?c[e+1]:null,i&&!i.model.skip&&(u=(n.model.x-i.model.x)/3,n.model.controlPointPreviousX=n.model.x-u,n.model.controlPointPreviousY=n.model.y-u*n.mK),a&&!a.model.skip&&(u=(a.model.x-n.model.x)/3,n.model.controlPointNextX=n.model.x+u,n.model.controlPointNextY=n.model.y+u*n.mK))}function pt(t,e,i){return Math.max(Math.min(t,i),e)}function gt(t,e,i,n){var a,r,o,s;if(e.spanGaps&&(t=t.filter((function(t){return!t.skip}))),"monotone"===e.cubicInterpolationMode)vt(t);else{var l=n?t[t.length-1]:t[0];for(a=0,r=t.length;a<r;++a)s=ft(l,o=t[a],t[Math.min(a+1,r-(n?0:1))%r],e.tension),o.controlPointPreviousX=s.previous.x,o.controlPointPreviousY=s.previous.y,o.controlPointNextX=s.next.x,o.controlPointNextY=s.next.y,l=o}e.capBezierPoints&&function(t,e){var i,n,a;for(i=0,n=t.length;i<n;++i)N(a=t[i],e)&&(i>0&&N(t[i-1],e)&&(a.controlPointPreviousX=pt(a.controlPointPreviousX,e.left,e.right),a.controlPointPreviousY=pt(a.controlPointPreviousY,e.top,e.bottom)),i<t.length-1&&N(t[i+1],e)&&(a.controlPointNextX=pt(a.controlPointNextX,e.left,e.right),a.controlPointNextY=pt(a.controlPointNextY,e.top,e.bottom)))}(t,i)}var yt=Object.freeze({__proto__:null,splineCurve:ft,splineCurveMonotone:vt,_updateBezierControlPoints:gt});function mt(t){return null!=t&&"none"!==t}function bt(t){var e=t.parentNode;return e&&"[object ShadowRoot]"===e.toString()&&(e=e.host),e}function xt(t,e,i){var n;return"string"==typeof t?(n=parseInt(t,10),-1!==t.indexOf("%")&&(n=n/100*e.parentNode[i])):n=t,n}function _t(t,e,i){var n=document.defaultView,a=bt(t),r=n.getComputedStyle(t)[e],o=n.getComputedStyle(a)[e],s=mt(r),l=mt(o),u=Number.POSITIVE_INFINITY;if(s||l)return Math.min(s?xt(r,t,i):u,l?xt(o,a,i):u)}function kt(t,e){return t.currentStyle?t.currentStyle[e]:document.defaultView.getComputedStyle(t,null).getPropertyValue(e)}function Mt(t,e,i){return(e=kt(t,e)).indexOf("%")>-1?i*parseInt(e,10)/100:parseInt(e,10)}function wt(t){var e=bt(t);if(!e)return"number"==typeof t.clientWidth?t.clientWidth:t.width;var i=e.clientWidth,n=i-Mt(e,"padding-left",i)-Mt(e,"padding-right",i),a=function(t){return _t(t,"max-width","clientWidth")}(t);return isNaN(a)?n:Math.min(n,a)}function St(t){var e=bt(t);if(!e)return"number"==typeof t.clientHeight?t.clientHeight:t.height;var i=e.clientHeight,n=i-Mt(e,"padding-top",i)-Mt(e,"padding-bottom",i),a=function(t){return _t(t,"max-height","clientHeight")}(t);return isNaN(a)?n:Math.min(n,a)}var Pt=Object.freeze({__proto__:null,_getParentNode:bt,getStyle:kt,getRelativePosition:function(t,e){var i,n,a=t.originalEvent||t,r=t.target||t.srcElement,o=r.getBoundingClientRect(),s=a.touches;s&&s.length>0?(i=s[0].clientX,n=s[0].clientY):(i=a.clientX,n=a.clientY);var l=parseFloat(kt(r,"padding-left")),u=parseFloat(kt(r,"padding-top")),c=parseFloat(kt(r,"padding-right")),h=parseFloat(kt(r,"padding-bottom")),d=o.right-o.left-l-c,f=o.bottom-o.top-u-h;return{x:i=Math.round((i-o.left-l)/d*r.width/e.currentDevicePixelRatio),y:n=Math.round((n-o.top-u)/f*r.height/e.currentDevicePixelRatio)}},getMaximumWidth:wt,getMaximumHeight:St,retinaScale:function(t,e){var i=t.currentDevicePixelRatio=e||"undefined"!=typeof window&&window.devicePixelRatio||1,n=t.canvas,a=t.width,r=t.height;n.height=r*i,n.width=a*i,t.ctx.setTransform(i,0,0,i,0,0),!n.style||n.style.height||n.style.width||(n.style.height=r+"px",n.style.width=a+"px")}}),Dt={linear:function(t){return t},easeInQuad:function(t){return t*t},easeOutQuad:function(t){return-t*(t-2)},easeInOutQuad:function(t){return(t/=.5)<1?.5*t*t:-.5*(--t*(t-2)-1)},easeInCubic:function(t){return t*t*t},easeOutCubic:function(t){return(t-=1)*t*t+1},easeInOutCubic:function(t){return(t/=.5)<1?.5*t*t*t:.5*((t-=2)*t*t+2)},easeInQuart:function(t){return t*t*t*t},easeOutQuart:function(t){return-((t-=1)*t*t*t-1)},easeInOutQuart:function(t){return(t/=.5)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)},easeInQuint:function(t){return t*t*t*t*t},easeOutQuint:function(t){return(t-=1)*t*t*t*t+1},easeInOutQuint:function(t){return(t/=.5)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)},easeInSine:function(t){return 1-Math.cos(t*(Math.PI/2))},easeOutSine:function(t){return Math.sin(t*(Math.PI/2))},easeInOutSine:function(t){return-.5*(Math.cos(Math.PI*t)-1)},easeInExpo:function(t){return 0===t?0:Math.pow(2,10*(t-1))},easeOutExpo:function(t){return 1===t?1:1-Math.pow(2,-10*t)},easeInOutExpo:function(t){return 0===t?0:1===t?1:(t/=.5)<1?.5*Math.pow(2,10*(t-1)):.5*(2-Math.pow(2,-10*--t))},easeInCirc:function(t){return t>=1?t:-(Math.sqrt(1-t*t)-1)},easeOutCirc:function(t){return Math.sqrt(1-(t-=1)*t)},easeInOutCirc:function(t){return(t/=.5)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)},easeInElastic:function(t){var e,i=0;return 0===t?0:1===t?1:(i||(i=.3),e=i/(2*Math.PI)*Math.asin(1),-1*Math.pow(2,10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/i))},easeOutElastic:function(t){var e,i=0;return 0===t?0:1===t?1:(i||(i=.3),e=i/(2*Math.PI)*Math.asin(1),1*Math.pow(2,-10*t)*Math.sin((t-e)*(2*Math.PI)/i)+1)},easeInOutElastic:function(t){var e,i=0;return 0===t?0:2==(t/=.5)?1:(i||(i=.45),e=i/(2*Math.PI)*Math.asin(1),t<1?1*Math.pow(2,10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/i)*-.5:1*Math.pow(2,-10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/i)*.5+1)},easeInBack:function(t){var e=1.70158;return t*t*((e+1)*t-e)},easeOutBack:function(t){var e=1.70158;return(t-=1)*t*((e+1)*t+e)+1},easeInOutBack:function(t){var e=1.70158;return(t/=.5)<1?t*t*((1+(e*=1.525))*t-e)*.5:.5*((t-=2)*t*((1+(e*=1.525))*t+e)+2)},easeInBounce:function(t){return 1-Dt.easeOutBounce(1-t)},easeOutBounce:function(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},easeInOutBounce:function(t){return t<.5?.5*Dt.easeInBounce(2*t):.5*Dt.easeOutBounce(2*t-1)+.5}},Ot=new(function(){function t(){e(this,t),this.color="rgba(0,0,0,0.1)",this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.fontColor="#666",this.fontFamily="'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",this.fontSize=12,this.fontStyle="normal",this.lineHeight=1.2,this.hover={onHover:null,mode:"nearest",intersect:!0},this.maintainAspectRatio=!0,this.onClick=null,this.responsive=!0,this.showLines=!0,this.plugins=void 0,this.scale=void 0,this.legend=void 0,this.title=void 0,this.tooltips=void 0,this.doughnut=void 0}return n(t,[{key:"set",value:function(t,e){return P(this[t]||(this[t]={}),e)}}]),t}());function At(t,e){var i=(""+t).match(/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/);if(!i||"normal"===i[1])return 1.2*e;switch(t=+i[2],i[3]){case"px":return t;case"%":t/=100}return e*t}function Tt(t){var e,i,n,a;return b(t)?(e=+t.top||0,i=+t.right||0,n=+t.bottom||0,a=+t.left||0):e=i=n=a=+t||0,{top:e,right:i,bottom:n,left:a,height:e+n,width:a+i}}function Ct(t){var e=_(t.fontSize,Ot.fontSize);"string"==typeof e&&(e=parseInt(e,10));var i={family:_(t.fontFamily,Ot.fontFamily),lineHeight:At(_(t.lineHeight,Ot.lineHeight),e),size:e,style:_(t.fontStyle,Ot.fontStyle),weight:null,string:""};return i.string=function(t){return!t||y(t.size)||y(t.family)?null:(t.style?t.style+" ":"")+(t.weight?t.weight+" ":"")+t.size+"px "+t.family}(i),i}function Ft(t,e,i,n){var a,r,o,s=!0;for(a=0,r=t.length;a<r;++a)if(void 0!==(o=t[a])&&(void 0!==e&&"function"==typeof o&&(o=o(e),s=!1),void 0!==i&&m(o)&&(o=o[i],s=!1),void 0!==o))return n&&!s&&(n.cacheable=!1),o}var Et=Object.freeze({__proto__:null,toLineHeight:At,toPadding:Tt,_parseFont:Ct,resolve:Ft});function It(t,e,i){return t?function(t,e){return{x:function(i){return t+t+e-i},setWidth:function(t){e=t},textAlign:function(t){return"center"===t?t:"right"===t?"left":"right"},xPlus:function(t,e){return t-e},leftForLtr:function(t,e){return t-e}}}(e,i):{x:function(t){return t},setWidth:function(t){},textAlign:function(t){return t},xPlus:function(t,e){return t+e},leftForLtr:function(t,e){return t}}}function Lt(t,e){var i,n;"ltr"!==e&&"rtl"!==e||(n=[(i=t.canvas.style).getPropertyValue("direction"),i.getPropertyPriority("direction")],i.setProperty("direction",e,"important"),t.prevTextDirection=n)}function Rt(t,e){void 0!==e&&(delete t.prevTextDirection,t.canvas.style.setProperty("direction",e[0],e[1]))}var zt=Object.freeze({__proto__:null,getRtlAdapter:It,overrideTextDirection:Lt,restoreTextDirection:Rt}),Bt={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},Vt="0123456789ABCDEF",Wt=function(t){return Vt[15&t]},Nt=function(t){return Vt[(240&t)>>4]+Vt[15&t]},Ht=function(t){return(240&t)>>4==(15&t)};
/*!
 * @kurkle/color v0.1.6
 * https://github.com/kurkle/color#readme
 * (c) 2020 Jukka Kurkela
 * Released under the MIT License
 */function jt(t){var e=function(t){return Ht(t.r)&&Ht(t.g)&&Ht(t.b)&&Ht(t.a)}(t)?Wt:Nt;return t?"#"+e(t.r)+e(t.g)+e(t.b)+(t.a<255?e(t.a):""):t}function Yt(t){return t+.5|0}var Ut=function(t,e,i){return Math.max(Math.min(t,i),e)};function Xt(t){return Ut(Yt(2.55*t),0,255)}function qt(t){return Ut(Yt(255*t),0,255)}function $t(t){return Ut(Yt(t/2.55)/100,0,1)}function Gt(t){return Ut(Yt(100*t),0,100)}var Kt=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/;var Zt=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function Qt(t,e,i){var n=e*Math.min(i,1-i),a=function(e){var a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:(e+t/30)%12;return i-n*Math.max(Math.min(a-3,9-a,1),-1)};return[a(0),a(8),a(4)]}function Jt(t,e,i){var n=function(n){var a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:(n+t/60)%6;return i-i*e*Math.max(Math.min(a,4-a,1),0)};return[n(5),n(3),n(1)]}function te(t,e,i){var n,a=Qt(t,1,.5);for(e+i>1&&(e*=n=1/(e+i),i*=n),n=0;n<3;n++)a[n]*=1-e-i,a[n]+=e;return a}function ee(t){var e,i,n,a=t.r/255,r=t.g/255,o=t.b/255,s=Math.max(a,r,o),l=Math.min(a,r,o),u=(s+l)/2;return s!==l&&(n=s-l,i=u>.5?n/(2-s-l):n/(s+l),e=60*(e=s===a?(r-o)/n+(r<o?6:0):s===r?(o-a)/n+2:(a-r)/n+4)+.5),[0|e,i||0,u]}function ie(t,e,i,n){return(Array.isArray(e)?t(e[0],e[1],e[2]):t(e,i,n)).map(qt)}function ne(t,e,i){return ie(Qt,t,e,i)}function ae(t){return(t%360+360)%360}function re(t){var e,i=Zt.exec(t),n=255;if(i){i[5]!==e&&(n=i[6]?Xt(+i[5]):qt(+i[5]));var a=ae(+i[2]),r=+i[3]/100,o=+i[4]/100;return{r:(e="hwb"===i[1]?function(t,e,i){return ie(te,t,e,i)}(a,r,o):"hsv"===i[1]?function(t,e,i){return ie(Jt,t,e,i)}(a,r,o):ne(a,r,o))[0],g:e[1],b:e[2],a:n}}}var oe={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"};var se=function(t){var e,i,n,a,r,o={},s=Object.keys(t),l=Object.keys(oe);for(e=0;e<s.length;e++){for(a=r=s[e],i=0;i<l.length;i++)n=l[i],r=r.replace(n,oe[n]);n=parseInt(t[a],16),o[r]=[n>>16&255,n>>8&255,255&n]}return o}({OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"});function le(t,e,i){if(t){var n=ee(t);n[e]=Math.max(0,Math.min(n[e]+n[e]*i,0===e?360:1)),n=ne(n),t.r=n[0],t.g=n[1],t.b=n[2]}}function ue(t,e){return t?r(e||{},t):t}function ce(t){var e={r:0,g:0,b:0,a:255};return Array.isArray(t)?t.length>=3&&(e={r:t[0],g:t[1],b:t[2],a:255},t.length>3&&(e.a=qt(t[3]))):(e=ue(t,{r:0,g:0,b:0,a:1})).a=qt(e.a),e}function he(t){return"r"===t.charAt(0)?function(t){var e,i,n,a=Kt.exec(t),r=255;if(a){if(a[7]!==e){var o=+a[7];r=255&(a[8]?Xt(o):255*o)}return e=+a[1],i=+a[3],n=+a[5],{r:e=255&(a[2]?Xt(e):e),g:i=255&(a[4]?Xt(i):i),b:n=255&(a[6]?Xt(n):n),a:r}}}(t):re(t)}se.transparent=[0,0,0,0];var de=function(){function i(n){if(e(this,i),n instanceof i)return n;var a,r,o,s,l=t(n);"object"===l?a=ce(n):"string"===l&&(s=(r=n).length,"#"===r[0]&&(4===s||5===s?o={r:255&17*Bt[r[1]],g:255&17*Bt[r[2]],b:255&17*Bt[r[3]],a:5===s?17*Bt[r[4]]:255}:7!==s&&9!==s||(o={r:Bt[r[1]]<<4|Bt[r[2]],g:Bt[r[3]]<<4|Bt[r[4]],b:Bt[r[5]]<<4|Bt[r[6]],a:9===s?Bt[r[7]]<<4|Bt[r[8]]:255})),a=o||function(t){var e=se[t];return e&&{r:e[0],g:e[1],b:e[2],a:4===e.length?e[3]:255}}(n)||he(n)),this._rgb=a,this._valid=!!a}return n(i,[{key:"rgbString",value:function(){return(t=this._rgb)&&(t.a<255?"rgba(".concat(t.r,", ").concat(t.g,", ").concat(t.b,", ").concat($t(t.a),")"):"rgb(".concat(t.r,", ").concat(t.g,", ").concat(t.b,")"));var t}},{key:"hexString",value:function(){return jt(this._rgb)}},{key:"hslString",value:function(){return function(t){if(t){var e=ee(t),i=e[0],n=Gt(e[1]),a=Gt(e[2]);return t.a<255?"hsla(".concat(i,", ").concat(n,"%, ").concat(a,"%, ").concat($t(t.a),")"):"hsl(".concat(i,", ").concat(n,"%, ").concat(a,"%)")}}(this._rgb)}},{key:"mix",value:function(t,e){if(t){var i,n=this.rgb,a=t.rgb,r=e===i?.5:e,o=2*r-1,s=n.a-a.a,l=((o*s==-1?o:(o+s)/(1+o*s))+1)/2;i=1-l,n.r=255&l*n.r+i*a.r+.5,n.g=255&l*n.g+i*a.g+.5,n.b=255&l*n.b+i*a.b+.5,n.a=r*n.a+(1-r)*a.a,this.rgb=n}return this}},{key:"clone",value:function(){return new i(this.rgb)}},{key:"alpha",value:function(t){return this._rgb.a=qt(t),this}},{key:"clearer",value:function(t){return this._rgb.a*=1-t,this}},{key:"greyscale",value:function(){var t=this._rgb,e=Yt(.3*t.r+.59*t.g+.11*t.b);return t.r=t.g=t.b=e,this}},{key:"opaquer",value:function(t){return this._rgb.a*=1+t,this}},{key:"negate",value:function(){var t=this._rgb;return t.r=255-t.r,t.g=255-t.g,t.b=255-t.b,this}},{key:"lighten",value:function(t){return le(this._rgb,2,t),this}},{key:"darken",value:function(t){return le(this._rgb,2,-t),this}},{key:"saturate",value:function(t){return le(this._rgb,1,t),this}},{key:"desaturate",value:function(t){return le(this._rgb,1,-t),this}},{key:"rotate",value:function(t){return function(t,e){var i=ee(t);i[0]=ae(i[0]+e),i=ne(i),t.r=i[0],t.g=i[1],t.b=i[2]}(this._rgb,t),this}},{key:"valid",get:function(){return this._valid}},{key:"rgb",get:function(){var t=ue(this._rgb);return t&&(t.a=$t(t.a)),t},set:function(t){this._rgb=ce(t)}}]),i}();function fe(t){return new de(t)}var ve=function(t){return t instanceof CanvasGradient||t instanceof CanvasPattern};var pe=function(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?o(Object(i),!0).forEach((function(e){a(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):o(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}({},T,{canvas:X,curve:yt,dom:Pt,easing:{effects:Dt},options:Et,math:ht,rtl:zt,requestAnimFrame:"undefined"==typeof window?function(t){t()}:window.requestAnimationFrame,fontString:function(t,e,i){return e+" "+t+"px "+i},color:function(t){return ve(t)?t:fe(t)},getHoverColor:function(t){return ve(t)?t:fe(t).saturate(.5).darken(.1).hexString()}});var ge=new(function(){function t(){e(this,t),this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}return n(t,[{key:"_notify",value:function(t,e,i,n){var a=e.listeners[n]||[],r=e.duration;a.forEach((function(n){return n({chart:t,numSteps:r,currentStep:i-e.start})}))}},{key:"_refresh",value:function(){var t=this;t._request||(t._running=!0,t._request=pe.requestAnimFrame.call(window,(function(){t._update(),t._request=null,t._running&&t._refresh()})))}},{key:"_update",value:function(){var t=this,e=Date.now(),i=0;t._charts.forEach((function(n,a){if(n.running&&n.items.length){for(var r,o=n.items,s=o.length-1,l=!1;s>=0;--s)(r=o[s])._active?(r.tick(e),l=!0):(o[s]=o[o.length-1],o.pop());l&&a.draw(),a.options.animation.debug&&function(t,e,i,n){var a=1e3/(i-n)|0,r=t.ctx;r.save(),r.clearRect(0,0,50,24),r.fillStyle="black",r.textAlign="right",e&&(r.fillText(e,50,8),r.fillText(a+" fps",50,18)),r.restore()}(a,o.length,e,t._lastDate),t._notify(a,n,e,"progress"),o.length||(n.running=!1,t._notify(a,n,e,"complete")),i+=o.length}})),t._lastDate=e,0===i&&(t._running=!1)}},{key:"_getAnims",value:function(t){var e=this._charts,i=e.get(t);return i||(i={running:!1,items:[],listeners:{complete:[],progress:[]}},e.set(t,i)),i}},{key:"listen",value:function(t,e,i){this._getAnims(t).listeners[e].push(i)}},{key:"add",value:function(t,e){var i;e&&e.length&&(i=this._getAnims(t).items).push.apply(i,f(e))}},{key:"has",value:function(t){return this._getAnims(t).items.length>0}},{key:"start",value:function(t){var e=this._charts.get(t);e&&(e.running=!0,e.start=Date.now(),e.duration=e.items.reduce((function(t,e){return Math.max(t,e._duration)}),0),this._refresh())}},{key:"running",value:function(t){if(!this._running)return!1;var e=this._charts.get(t);return!!(e&&e.running&&e.items.length)}},{key:"stop",value:function(t){var e=this._charts.get(t);if(e&&e.items.length){for(var i=e.items,n=i.length-1;n>=0;--n)i[n].cancel();e.items=[],this._notify(t,e,Date.now(),"complete")}}},{key:"remove",value:function(t){return this._charts.delete(t)}}]),t}()),ye={boolean:function(t,e,i){return i>.5?e:t},color:function(t,e,i){var n=pe.color(t||"transparent"),a=n.valid&&pe.color(e||"transparent");return a&&a.valid?a.mix(n,i).hexString():e},number:function(t,e,i){return t+(e-t)*i}},me=function(){function i(n,a,r,o){e(this,i);var s=a[r];o=Ft([n.to,o,s,n.from]);var l=Ft([n.from,s,o]);this._active=!0,this._fn=n.fn||ye[n.type||t(l)],this._easing=Dt[n.easing||"linear"],this._start=Math.floor(Date.now()+(n.delay||0)),this._duration=Math.floor(n.duration),this._loop=!!n.loop,this._target=a,this._prop=r,this._from=l,this._to=o}return n(i,[{key:"active",value:function(){return this._active}},{key:"cancel",value:function(){this._active&&(this.tick(Date.now()),this._active=!1)}},{key:"tick",value:function(t){var e,i=this,n=t-i._start,a=i._duration,r=i._prop,o=i._from,s=i._loop,l=i._to;i._active=o!==l&&(s||n<a),i._active?n<0?i._target[r]=o:(e=n/a%2,e=s&&e>1?2-e:e,e=i._easing(Math.min(1,Math.max(0,e))),i._target[r]=i._fn(o,l,e)):i._target[r]=l}}]),i}(),be=["borderColor","backgroundColor"];Ot.set("animation",{duration:1e3,easing:"easeOutQuart",onProgress:v,onComplete:v,colors:{type:"color",properties:be},numbers:{type:"number",properties:["x","y","borderWidth","radius","tension"]},active:{duration:400},resize:{duration:0},show:{colors:{type:"color",properties:be,from:"transparent"},visible:{type:"boolean",duration:0}},hide:{colors:{type:"color",properties:be,to:"transparent"},visible:{type:"boolean",easing:"easeInExpo"}}});var xe=function(){function t(i,n){e(this,t),this._chart=i,this._properties=new Map,this.configure(n)}return n(t,[{key:"configure",value:function(t){if(b(t)){var e=this._properties,i=function(t){var e={};return Object.keys(t).forEach((function(i){var n=t[i];b(n)||(e[i]=n)})),e}(t);Object.keys(t).forEach((function(n){var a=t[n];b(a)&&(a.properties||[n]).forEach((function(t){e.has(t)?t===n&&e.set(t,r({},e.get(t),a)):e.set(t,r({},i,a))}))}))}}},{key:"_animateOptions",value:function(t,e){var i=e.options,n=[];if(!i)return n;var a=t.options;return a?(a.$shared&&(t.options=a=r({},a,{$shared:!1,$animations:{}})),n=this._createAnimations(a,i)):t.options=i,n}},{key:"_createAnimations",value:function(t,e){var i,n=this._properties,a=[],r=t.$animations||(t.$animations={}),o=Object.keys(e);for(i=o.length-1;i>=0;--i){var s=o[i];if("$"!==s.charAt(0))if("options"!==s){var l=e[s],u=r[s];u&&u.cancel();var c=n.get(s);c&&c.duration?(r[s]=u=new me(c,t,s,l),a.push(u)):t[s]=l}else a.push.apply(a,f(this._animateOptions(t,e)))}return a}},{key:"update",value:function(t,e){if(0===this._properties.size)return function(t,e){var i=t.options,n=e.options;i&&n&&!n.$shared&&(i.$shared?t.options=r({},i,n,{$shared:!1}):r(i,n),delete e.options)}(t,e),void r(t,e);var i=this._createAnimations(t,e);return i.length?(ge.add(this._chart,i),!0):void 0}}]),t}(),_e=pe.options.resolve,ke=["push","pop","shift","splice","unshift"];function Me(t,e){var i=t&&t.options||{},n=i.reverse,a=void 0===i.min?e:0,r=void 0===i.max?e:0;return{start:n?r:a,end:n?a:r}}function we(t,e){var i=t._chartjs;if(i){var n=i.listeners,a=n.indexOf(e);-1!==a&&n.splice(a,1),n.length>0||(ke.forEach((function(e){delete t[e]})),delete t._chartjs)}}function Se(t,e){var i,n,a=[],r=t._getSortedDatasetMetas(e);for(i=0,n=r.length;i<n;++i)a.push(r[i].index);return a}function Pe(t,e,i,n){var a,r,o,s,l=t.keys;for(a=0,r=l.length;a<r;++a){if((o=+l[a])===i){if(n)continue;break}s=t.values[o],isNaN(s)||0!==e&&pe.math.sign(e)!==pe.math.sign(s)||(e+=s)}return e}function De(t,e){var i=t&&t.options.stacked;return i||void 0===i&&void 0!==e.stack}function Oe(t,e,i){var n=t[e]||(t[e]={});return n[i]||(n[i]={})}function Ae(t,e){for(var i=t.chart,n=t._cachedMeta,a=i._stacks||(i._stacks={}),r=n.iScale,o=n.vScale,s=n.index,l=r.axis,u=o.axis,c=function(t,e,i){return t.id+"."+e.id+"."+i.stack+"."+i.type}(r,o,n),h=e.length,d=0;d<h;++d){var f=e[d],v=f[l],p=f[u];((f._stacks||(f._stacks={}))[u]=Oe(a,c,v))[s]=p}}function Te(t,e){var i=t.scales;return Object.keys(i).filter((function(t){return i[t].axis===e})).shift()}var Ce=function(){function t(i,n){e(this,t),this.chart=i,this._ctx=i.ctx,this.index=n,this._cachedAnimations={},this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this._config=void 0,this._parsing=!1,this._data=void 0,this._dataCopy=void 0,this._objectData=void 0,this._labels=void 0,this._scaleStacked={},this.initialize()}return n(t,[{key:"initialize",value:function(){var t=this._cachedMeta;this.configure(),this.linkScales(),t._stacked=De(t.vScale,t),this.addElements()}},{key:"updateIndex",value:function(t){this.index=t}},{key:"linkScales",value:function(){var t=this,e=t.chart,i=t._cachedMeta,n=t.getDataset(),a=i.xAxisID=n.xAxisID||Te(e,"x"),r=i.yAxisID=n.yAxisID||Te(e,"y"),o=i.rAxisID=n.rAxisID||Te(e,"r");i.xScale=t.getScaleForId(a),i.yScale=t.getScaleForId(r),i.rScale=t.getScaleForId(o),i.iScale=t._getIndexScale(),i.vScale=t._getValueScale()}},{key:"getDataset",value:function(){return this.chart.data.datasets[this.index]}},{key:"getMeta",value:function(){return this.chart.getDatasetMeta(this.index)}},{key:"getScaleForId",value:function(t){return this.chart.scales[t]}},{key:"getValueScaleId",value:function(){return this._cachedMeta.yAxisID}},{key:"getIndexScaleId",value:function(){return this._cachedMeta.xAxisID}},{key:"_getValueScale",value:function(){return this.getScaleForId(this.getValueScaleId())}},{key:"_getIndexScale",value:function(){return this.getScaleForId(this.getIndexScaleId())}},{key:"_getOtherScale",value:function(t){var e=this._cachedMeta;return t===e.iScale?e.vScale:e.iScale}},{key:"reset",value:function(){this._update("reset")}},{key:"_destroy",value:function(){this._data&&we(this._data,this)}},{key:"_dataCheck",value:function(){var t,e,i=this,n=i.getDataset(),a=n.data||(n.data=[]);if(pe.isObject(a)){if(i._objectData===a)return!1;i._data=function(t){var e,i,n,a=Object.keys(t),r=new Array(a.length);for(e=0,i=a.length;e<i;++e)n=a[e],r[e]={x:n,y:t[n]};return r}(a),i._objectData=a}else{if(i._data===a&&pe.arrayEquals(a,i._dataCopy))return!1;i._data&&we(i._data,i),i._dataCopy=a.slice(0),a&&Object.isExtensible(a)&&(e=i,(t=a)._chartjs?t._chartjs.listeners.push(e):(Object.defineProperty(t,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[e]}}),ke.forEach((function(e){var i="_onData"+e.charAt(0).toUpperCase()+e.slice(1),n=t[e];Object.defineProperty(t,e,{configurable:!0,enumerable:!1,value:function(){for(var e=arguments.length,a=new Array(e),r=0;r<e;r++)a[r]=arguments[r];var o=n.apply(this,a);return t._chartjs.listeners.forEach((function(t){"function"==typeof t[i]&&t[i].apply(t,a)})),o}})})))),i._data=a}return!0}},{key:"_labelCheck",value:function(){var t=this._cachedMeta.iScale,e=t?t.getLabels():this.chart.data.labels;return this._labels!==e&&(this._labels=e,!0)}},{key:"addElements",value:function(){var t=this,e=t._cachedMeta;t._dataCheck();for(var i=t._data,n=e.data=new Array(i.length),a=0,r=i.length;a<r;++a)n[a]=new t.dataElementType;t.datasetElementType&&(e.dataset=new t.datasetElementType)}},{key:"buildOrUpdateElements",value:function(){var t=this,e=t._dataCheck(),i=t._labelCheck(),n=t._scaleCheck(),a=t._cachedMeta,r=t.getDataset(),o=!1;a._stacked=De(a.vScale,a),a.stack!==r.stack&&(o=!0,a._parsed.forEach((function(t){delete t._stacks[a.vScale.id][a.index]})),a.stack=r.stack),t._resyncElements(e||i||n||o),o&&Ae(t,a._parsed)}},{key:"configure",value:function(){var t=this;t._config=pe.merge({},[t.chart.options[t._type].datasets,t.getDataset()],{merger:function(t,e,i){"data"!==t&&pe._merger(t,e,i)}}),t._parsing=_e([t._config.parsing,t.chart.options.parsing,!0])}},{key:"parse",value:function(t,e){var i,n,a,r,o=this,s=o._cachedMeta,l=o._data,u=s.iScale,c=s.vScale,h=s._stacked,d=u.axis,f=!0;if(t>0&&(f=s._sorted,r=s._parsed[t-1]),!1===o._parsing)s._parsed=l,s._sorted=!0;else{for(n=pe.isArray(l[t])?o.parseArrayData(s,l,t,e):pe.isObject(l[t])?o.parseObjectData(s,l,t,e):o.parsePrimitiveData(s,l,t,e),i=0;i<e;++i)s._parsed[i+t]=a=n[i],f&&(r&&a[d]<r[d]&&(f=!1),r=a);s._sorted=f}h&&Ae(o,n),u.invalidateCaches(),c.invalidateCaches()}},{key:"parsePrimitiveData",value:function(t,e,i,n){var r,o,s,l=t.iScale,u=t.vScale,c=l.axis,h=u.axis,d=l.getLabels(),f=l===u,v=new Array(n);for(r=0,o=n;r<o;++r){var p;s=r+i,v[r]=(a(p={},c,f||l.parse(d[s],s)),a(p,h,u.parse(e[s],s)),p)}return v}},{key:"parseArrayData",value:function(t,e,i,n){var a,r,o,s,l=t.xScale,u=t.yScale,c=new Array(n);for(a=0,r=n;a<r;++a)s=e[o=a+i],c[a]={x:l.parse(s[0],o),y:u.parse(s[1],o)};return c}},{key:"parseObjectData",value:function(t,e,i,n){var a,r,o,s,l=t.xScale,u=t.yScale,c=new Array(n);for(a=0,r=n;a<r;++a)s=e[o=a+i],c[a]={x:l.parseObject(s,"x",o),y:u.parseObject(s,"y",o)};return c}},{key:"getParsed",value:function(t){return this._cachedMeta._parsed[t]}},{key:"applyStack",value:function(t,e){var i=this.chart,n=this._cachedMeta,a=e[t.axis];return Pe({keys:Se(i,!0),values:e._stacks[t.axis]},a,n.index)}},{key:"getMinMax",value:function(t,e){var i,n,a,r,o=this._cachedMeta,s=o._parsed,l=o._sorted&&t===o.iScale,u=s.length,c=this._getOtherScale(t),h=e&&o._stacked&&{keys:Se(this.chart,!0),values:null},d=Number.POSITIVE_INFINITY,f=Number.NEGATIVE_INFINITY,v=function(t){var e=t.getUserBounds(),i=e.min,n=e.max,a=e.minDefined,r=e.maxDefined;return{min:a?i:Number.NEGATIVE_INFINITY,max:r?n:Number.POSITIVE_INFINITY}}(c),p=v.min,g=v.max;function y(){h&&(h.values=a._stacks[t.axis],d=Math.min(d,n),f=Math.max(f,n),n=Pe(h,n,o.index,!0)),d=Math.min(d,n),f=Math.max(f,n)}function m(){return a=s[i],n=a[t.axis],r=a[c.axis],isNaN(n)||p>r||g<r}for(i=0;i<u&&(m()||(y(),!l));++i);if(l)for(i=u-1;i>=0;--i)if(!m()){y();break}return{min:d,max:f}}},{key:"getAllParsedValues",value:function(t){var e,i,n,a=this._cachedMeta._parsed,r=[];for(e=0,i=a.length;e<i;++e)n=a[e][t.axis],isNaN(n)||r.push(n);return r}},{key:"_cacheScaleStackStatus",value:function(){var t=this._cachedMeta,e=t.iScale,i=t.vScale,n=this._scaleStacked={};e&&i&&(n[e.id]=e.options.stacked,n[i.id]=i.options.stacked)}},{key:"_scaleCheck",value:function(){var t=this._cachedMeta,e=t.iScale,i=t.vScale,n=this._scaleStacked;return!n||!e||!i||n[e.id]!==e.options.stacked||n[i.id]!==i.options.stacked}},{key:"getMaxOverflow",value:function(){return!1}},{key:"getLabelAndValue",value:function(t){var e=this._cachedMeta,i=e.iScale,n=e.vScale,a=this.getParsed(t);return{label:i?""+i.getLabelForValue(a[i.axis]):"",value:n?""+n.getLabelForValue(a[n.axis]):""}}},{key:"_update",value:function(t){var e,i,n,a,r,o=this,s=o._cachedMeta;o.configure(),o._cachedAnimations={},o._cachedDataOpts={},o.update(t),s._clip=(e=pe.valueOrDefault(o._config.clip,function(t,e,i){if(!1===i)return!1;var n=Me(t,i),a=Me(e,i);return{top:a.end,right:n.end,bottom:a.start,left:n.start}}(s.xScale,s.yScale,o.getMaxOverflow())),pe.isObject(e)?(i=e.top,n=e.right,a=e.bottom,r=e.left):i=n=a=r=e,{top:i,right:n,bottom:a,left:r}),o._cacheScaleStackStatus()}},{key:"update",value:function(t){}},{key:"draw",value:function(){var t=this._ctx,e=this._cachedMeta,i=e.data||[],n=i.length,a=0;for(e.dataset&&e.dataset.draw(t);a<n;++a)i[a].draw(t)}},{key:"_addAutomaticHoverColors",value:function(t,e){for(var i,n=pe.getHoverColor,a=this.getStyle(t),r=Object.keys(a).filter((function(t){return-1!==t.indexOf("Color")&&!(t in e)})),o=r.length-1;o>=0;o--)i=r[o],e[i]=n(a[i])}},{key:"getStyle",value:function(t,e){var i=this,n=i._cachedMeta.dataset;i._config||i.configure();var a=n&&void 0===t?i.resolveDatasetElementOptions(e):i.resolveDataElementOptions(t||0,e&&"active");return e&&i._addAutomaticHoverColors(t,a),a}},{key:"_getContext",value:function(t,e){return{chart:this.chart,dataIndex:t,dataset:this.getDataset(),datasetIndex:this.index,active:e}}},{key:"resolveDatasetElementOptions",value:function(t){var e,i,n,a,r,o=this,s=o.chart,l=o._config,u=s.options.elements[o.datasetElementType._type]||{},c=o.datasetElementOptions,h={},d=o._getContext(void 0,t);for(e=0,i=c.length;e<i;++e)n=c[e],a=t?"hover"+n.charAt(0).toUpperCase()+n.slice(1):n,void 0!==(r=_e([l[a],u[a]],d))&&(h[n]=r);return h}},{key:"resolveDataElementOptions",value:function(t,e){var i=this,n="active"===e,a=i._cachedDataOpts;if(a[e])return a[e];var r,o,s,l,u,c,h=i.chart,d=i._config,f=h.options.elements[i.dataElementType._type]||{},v=i.dataElementOptions,p={},g=i._getContext(t,n),y={cacheable:!n};if(pe.isArray(v))for(o=0,s=v.length;o<s;++o)l=v[o],c=n?"hover"+l.charAt(0).toUpperCase()+l.slice(1):l,void 0!==(u=_e([d[c],f[c]],g,t,y))&&(p[l]=u);else for(o=0,s=(r=Object.keys(v)).length;o<s;++o)l=r[o],c=n?"hover"+l.charAt(0).toUpperCase()+l.slice(1):l,void 0!==(u=_e([d[v[c]],d[c],f[c]],g,t,y))&&(p[l]=u);return y.cacheable&&(p.$shared=!0,a[e]=p),p}},{key:"_resolveAnimations",value:function(t,e,i){var n=this.chart,a=this._cachedAnimations;if(a[e=e||"default"])return a[e];var o={cacheable:!0},s=this._getContext(t,i),l=_e([this._config.animation],s,t,o),u=_e([n.options.animation],s,t,o),c=pe.mergeIf({},[l,u]);c[e]&&(c=r({},c,c[e]));var h=new xe(n,c);return o.cacheable&&(a[e]=h&&Object.freeze(h)),h}},{key:"getSharedOptions",value:function(t,e,i){if(t||(this._sharedOptions=i&&i.$shared),"reset"!==t&&i&&i.$shared&&e&&e.options&&e.options.$shared)return{target:e.options,options:i}}},{key:"includeOptions",value:function(t,e){return"hide"===t||"show"===t||"resize"!==t&&!e}},{key:"updateElement",value:function(t,e,i,n){"reset"===n||"none"===n?r(t,i):this._resolveAnimations(e,n).update(t,i)}},{key:"updateSharedOptions",value:function(t,e){t&&this._resolveAnimations(void 0,e).update(t.target,t.options)}},{key:"_setStyle",value:function(t,e,i,n){t.active=n,this._resolveAnimations(e,i,n).update(t,{options:this.getStyle(e,n)})}},{key:"removeHoverStyle",value:function(t,e,i){this._setStyle(t,i,"active",!1)}},{key:"setHoverStyle",value:function(t,e,i){this._setStyle(t,i,"active",!0)}},{key:"_removeDatasetHoverStyle",value:function(){var t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!1)}},{key:"_setDatasetHoverStyle",value:function(){var t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!0)}},{key:"_resyncElements",value:function(t){var e=this,i=e._cachedMeta,n=i.data.length,a=e._data.length;a>n?(e._insertElements(n,a-n),t&&n&&e.parse(0,n)):a<n?(i.data.splice(a,n-a),i._parsed.splice(a,n-a),e.parse(0,a)):t&&e.parse(0,a)}},{key:"_insertElements",value:function(t,e){var i,n,a=this,r=new Array(e),o=a._cachedMeta,s=o.data;for(i=0;i<e;++i)r[i]=new a.dataElementType;(s.splice.apply(s,[t,0].concat(r)),a._parsing)&&(n=o._parsed).splice.apply(n,[t,0].concat(f(new Array(e))));a.parse(t,e),a.updateElements(r,t,"reset")}},{key:"updateElements",value:function(t,e,i){}},{key:"_removeElements",value:function(t,e){this._parsing&&this._cachedMeta._parsed.splice(t,e),this._cachedMeta.data.splice(t,e)}},{key:"_onDataPush",value:function(){var t=arguments.length;this._insertElements(this.getDataset().data.length-t,t)}},{key:"_onDataPop",value:function(){this._removeElements(this._cachedMeta.data.length-1,1)}},{key:"_onDataShift",value:function(){this._removeElements(0,1)}},{key:"_onDataSplice",value:function(t,e){this._removeElements(t,e),this._insertElements(t,arguments.length-2)}},{key:"_onDataUnshift",value:function(){this._insertElements(0,arguments.length)}}]),t}();a(Ce,"extend",pe.inherits),Ce.prototype.datasetElementType=null,Ce.prototype.dataElementType=null,Ce.prototype.datasetElementOptions=["backgroundColor","borderCapStyle","borderColor","borderDash","borderDashOffset","borderJoinStyle","borderWidth"],Ce.prototype.dataElementOptions=["backgroundColor","borderColor","borderWidth","pointStyle"];var Fe=function(){function t(){e(this,t),this.x=void 0,this.y=void 0,this.active=!1,this.options=void 0,this.$animations=void 0}return n(t,[{key:"tooltipPosition",value:function(t){var e=this.getProps(["x","y"],t);return{x:e.x,y:e.y}}},{key:"hasValue",value:function(){return Q(this.x)&&Q(this.y)}},{key:"getProps",value:function(t,e){var i=this,n=this.$animations;if(!e||!n)return i;var a={};return t.forEach((function(t){a[t]=n[t]&&n[t].active?n[t]._to:i[t]})),a}}]),t}();a(Fe,"extend",A);var Ee=2*Math.PI;function Ie(t,e){var i=e.startAngle,n=e.endAngle,a=e.pixelMargin,r=e.x,o=e.y,s=a/e.outerRadius;t.beginPath(),t.arc(r,o,e.outerRadius,i-s,n+s),e.innerRadius>a?(s=a/e.innerRadius,t.arc(r,o,e.innerRadius-a,n+s,i-s,!0)):t.arc(r,o,a,n+Math.PI/2,i-Math.PI/2),t.closePath(),t.clip()}function Le(t,e,i){var n=e.options,a="inner"===n.borderAlign;a?(t.lineWidth=2*n.borderWidth,t.lineJoin="round"):(t.lineWidth=n.borderWidth,t.lineJoin="bevel"),i.fullCircles&&function(t,e,i,n){var a,r=i.endAngle;for(n&&(i.endAngle=i.startAngle+Ee,Ie(t,i),i.endAngle=r,i.endAngle===i.startAngle&&i.fullCircles&&(i.endAngle+=Ee,i.fullCircles--)),t.beginPath(),t.arc(i.x,i.y,i.innerRadius,i.startAngle+Ee,i.startAngle,!0),a=0;a<i.fullCircles;++a)t.stroke();for(t.beginPath(),t.arc(i.x,i.y,e.outerRadius,i.startAngle,i.startAngle+Ee),a=0;a<i.fullCircles;++a)t.stroke()}(t,e,i,a),a&&Ie(t,i),t.beginPath(),t.arc(i.x,i.y,e.outerRadius,i.startAngle,i.endAngle),t.arc(i.x,i.y,i.innerRadius,i.endAngle,i.startAngle,!0),t.closePath(),t.stroke()}Ot.set("elements",{arc:{backgroundColor:Ot.color,borderAlign:"center",borderColor:"#fff",borderWidth:2}});var Re=function(t){function i(t){var n;return e(this,i),(n=h(this,l(i).call(this))).options=void 0,n.circumference=void 0,n.startAngle=void 0,n.endAngle=void 0,n.innerRadius=void 0,n.outerRadius=void 0,t&&r(c(n),t),n}return s(i,t),n(i,[{key:"inRange",value:function(t,e,i){var n=ot(this.getProps(["x","y"],i),{x:t,y:e}),a=n.angle,r=n.distance,o=this.getProps(["startAngle","endAngle","innerRadius","outerRadius","circumference"],i),s=o.startAngle,l=o.endAngle,u=o.innerRadius,c=o.outerRadius;return(o.circumference>=Ee||ut(a,s,l))&&(r>=u&&r<=c)}},{key:"getCenterPoint",value:function(t){var e=this.getProps(["x","y","startAngle","endAngle","innerRadius","outerRadius"],t),i=e.x,n=e.y,a=(e.startAngle+e.endAngle)/2,r=(e.innerRadius+e.outerRadius)/2;return{x:i+Math.cos(a)*r,y:n+Math.sin(a)*r}}},{key:"tooltipPosition",value:function(t){return this.getCenterPoint(t)}},{key:"draw",value:function(t){var e,i=this,n=i.options,a="inner"===n.borderAlign?.33:0,r={x:i.x,y:i.y,innerRadius:i.innerRadius,outerRadius:Math.max(i.outerRadius-a,0),pixelMargin:a,startAngle:i.startAngle,endAngle:i.endAngle,fullCircles:Math.floor(i.circumference/Ee)};if(t.save(),t.fillStyle=n.backgroundColor,t.strokeStyle=n.borderColor,r.fullCircles){for(r.endAngle=r.startAngle+Ee,t.beginPath(),t.arc(r.x,r.y,r.outerRadius,r.startAngle,r.endAngle),t.arc(r.x,r.y,r.innerRadius,r.endAngle,r.startAngle,!0),t.closePath(),e=0;e<r.fullCircles;++e)t.fill();r.endAngle=r.startAngle+i.circumference%Ee}t.beginPath(),t.arc(r.x,r.y,r.outerRadius,r.startAngle,r.endAngle),t.arc(r.x,r.y,r.innerRadius,r.endAngle,r.startAngle,!0),t.closePath(),t.fill(),n.borderWidth&&Le(t,i,r),t.restore()}}]),i}(Fe);function ze(t,e,i,n){return{x:t.x+i*(e.x-t.x),y:t.y+i*(e.y-t.y)}}function Be(t,e,i,n){return{x:t.x+i*(e.x-t.x),y:"middle"===n?i<.5?t.y:e.y:"after"===n?i<1?t.y:e.y:i>0?e.y:t.y}}function Ve(t,e,i,n){var a={x:t.controlPointNextX,y:t.controlPointNextY},r={x:e.controlPointPreviousX,y:e.controlPointPreviousY},o=ze(t,a,i),s=ze(a,r,i),l=ze(r,e,i),u=ze(o,s,i),c=ze(s,l,i);return ze(u,c,i)}function We(t){return"angle"===t?{between:ut,compare:st,normalize:lt}:{between:function(t,e,i){return t>=e&&t<=i},compare:function(t,e){return t-e},normalize:function(t){return t}}}function Ne(t,e,i,n){return{start:t%n,end:e%n,loop:i&&(e-t+1)%n==0}}function He(t,e,i){if(!i)return[t];var n,a,r,o,s=i.property,l=i.start,u=i.end,c=e.length,h=We(s),d=h.compare,f=h.between,v=h.normalize,p=function(t,e,i){var n,a,r=i.property,o=i.start,s=i.end,l=We(r),u=l.between,c=l.normalize,h=e.length,d=t.start,f=t.end,v=t.loop;if(v){for(d+=h,f+=h,n=0,a=h;n<a&&u(c(e[d%h][r]),o,s);++n)d--,f--;d%=h,f%=h}return f<d&&(f+=h),{start:d,end:f,loop:v}}(t,e,i),g=p.start,y=p.end,m=p.loop,b=[],x=!1,_=null;for(n=g;n<=y;++n)(r=e[n%c]).skip||(x=f(a=v(r[s]),l,u),null===_&&x&&(_=n>g&&d(a,l)>0?o:n),null===_||x&&0!==d(a,u)||(b.push(Ne(_,n,m,c)),_=null),o=n);return null!==_&&b.push(Ne(_,y,m,c)),b}function je(t,e){for(var i=[],n=t.segments,a=0;a<n.length;a++){var r=He(n[a],t.points,e);r.length&&i.push.apply(i,f(r))}return i}a(Re,"_type","arc");var Ye=Ot.color;function Ue(t,e,i){t.lineTo(i.x,i.y)}function Xe(t,e,i,n){var a,r,o,s=i.start,l=i.end,u=i.loop,c=e.points,h=e.options,d=function(t){return t.stepped?Y:t.tension?U:Ue}(h),f=c.length,v=n||{},p=v.move,g=void 0===p||p,y=v.reverse,m=l<s?f+l-s:l-s;for(a=0;a<=m;++a)(r=c[(s+(y?m-a:a))%f]).skip||(g?(t.moveTo(r.x,r.y),g=!1):d(t,o,r,y,h.stepped),o=r);return u&&d(t,o,r=c[(s+(y?m:0))%f],y,h.stepped),!!u}function qe(t,e,i,n){var a,r,o,s,l,u,c=e.points,h=c.length,d=i.start,f=i.end,v=n||{},p=v.move,g=void 0===p||p,y=v.reverse,m=f<d?h+f-d:f-d,b=0,x=0;for(g&&(r=c[(d+(y?m:0))%h],t.moveTo(r.x,r.y)),a=0;a<=m;++a)if(!(r=c[(d+(y?m-a:a))%h]).skip){var _=r.x,k=r.y,M=0|_;M===o?(k<s?s=k:k>l&&(l=k),b=(x*b+_)/++x):(s!==l&&(t.lineTo(b,l),t.lineTo(b,s),t.lineTo(b,u)),t.lineTo(_,k),o=M,x=0,s=l=k),u=k}}function $e(t){var e=t.options,i=e.borderDash&&e.borderDash.length;return!(t._loop||e.tension||e.stepped||i)?qe:Xe}Ot.set("elements",{line:{backgroundColor:Ye,borderCapStyle:"butt",borderColor:Ye,borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:3,capBezierPoints:!0,fill:!0,tension:.4}});var Ge=function(t){function i(t){var n;return e(this,i),(n=h(this,l(i).call(this))).options=void 0,n._loop=void 0,n._fullLoop=void 0,n._controlPointsUpdated=void 0,n._points=void 0,n._segments=void 0,t&&r(c(n),t),n}return s(i,t),n(i,[{key:"updateControlPoints",value:function(t){var e=this;if(!e._controlPointsUpdated){var i=e.options;if(i.tension&&!i.stepped){var n=i.spanGaps?e._loop:e._fullLoop;gt(e._points,i,t,n)}}}},{key:"first",value:function(){var t=this.segments,e=this.points;return t.length&&e[t[0].start]}},{key:"last",value:function(){var t=this.segments,e=this.points,i=t.length;return i&&e[t[i-1].end]}},{key:"interpolate",value:function(t,e){var i=this.options,n=t[e],a=this.points,r=je(this,{property:e,start:n,end:n});if(r.length){var o,s,l=[],u=function(t){return t.stepped?Be:t.tension?Ve:ze}(i);for(o=0,s=r.length;o<s;++o){var c=r[o],h=c.start,d=c.end,f=a[h],v=a[d];if(f!==v){var p=u(f,v,Math.abs((n-f[e])/(v[e]-f[e])),i.stepped);p[e]=t[e],l.push(p)}else l.push(f)}return 1===l.length?l[0]:l}}},{key:"pathSegment",value:function(t,e,i){return $e(this)(t,this,e,i)}},{key:"path",value:function(t){for(var e=this.segments,i=e.length,n=$e(this),a=this._loop,r=0;r<i;++r)a&=n(t,this,e[r]);return!!a}},{key:"draw",value:function(t){this.points.length&&(t.save(),function(t,e){t.lineCap=e.borderCapStyle,t.setLineDash(e.borderDash),t.lineDashOffset=e.borderDashOffset,t.lineJoin=e.borderJoinStyle,t.lineWidth=e.borderWidth,t.strokeStyle=e.borderColor}(t,this.options),t.beginPath(),this.path(t)&&t.closePath(),t.stroke(),t.restore())}},{key:"points",set:function(t){this._points=t,delete this._segments},get:function(){return this._points}},{key:"segments",get:function(){return this._segments||(this._segments=function(t){var e=t.points,i=t.options.spanGaps,n=e.length;if(!n)return[];var a=!!t._loop,r=function(t,e,i,n){var a=0,r=e-1;if(i&&!n)for(;a<e&&!t[a].skip;)a++;for(;a<e&&t[a].skip;)a++;for(a%=e,i&&(r+=a);r>a&&t[r%e].skip;)r--;return{start:a,end:r%=e}}(e,n,a,i),o=r.start,s=r.end;return!0===i?[{start:o,end:s,loop:a}]:function(t,e,i,n){var a,r=t.length,o=[],s=e,l=t[e];for(a=e+1;a<=i;++a){var u=t[a%r];u.skip||u.stop?l.skip||(n=!1,o.push({start:e%r,end:(a-1)%r,loop:n}),e=s=u.stop?a:null):(s=a,l.skip&&(e=a)),l=u}return null!==s&&o.push({start:e%r,end:s%r,loop:n}),o}(e,o,s<o?s+n:s,!!t._fullLoop&&0===o&&s===n-1)}(this))}}]),i}(Fe);a(Ge,"_type","line");var Ke=Ot.color;Ot.set("elements",{point:{backgroundColor:Ke,borderColor:Ke,borderWidth:1,hitRadius:1,hoverBorderWidth:1,hoverRadius:4,pointStyle:"circle",radius:3}});var Ze=function(t){function i(t){var n;return e(this,i),(n=h(this,l(i).call(this))).options=void 0,n.skip=void 0,n.stop=void 0,t&&r(c(n),t),n}return s(i,t),n(i,[{key:"inRange",value:function(t,e,i){var n=this.options,a=this.getProps(["x","y"],i),r=a.x,o=a.y;return Math.pow(t-r,2)+Math.pow(e-o,2)<Math.pow(n.hitRadius+n.radius,2)}},{key:"inXRange",value:function(t,e){var i=this.options,n=this.getProps(["x"],e).x;return Math.abs(t-n)<i.radius+i.hitRadius}},{key:"inYRange",value:function(t,e){var i=this.options,n=this.getProps(["x"],e).y;return Math.abs(t-n)<i.radius+i.hitRadius}},{key:"getCenterPoint",value:function(t){var e=this.getProps(["x","y"],t);return{x:e.x,y:e.y}}},{key:"size",value:function(){var t=this.options||{},e=Math.max(t.radius,t.hoverRadius)||0;return 2*(e+(e&&t.borderWidth||0))}},{key:"draw",value:function(t,e){var i=this,n=i.options;i.skip||n.radius<=0||(void 0===e||N(i,e))&&(t.strokeStyle=n.borderColor,t.lineWidth=n.borderWidth,t.fillStyle=n.backgroundColor,W(t,n,i.x,i.y))}},{key:"getRange",value:function(){var t=this.options||{};return t.radius+t.hitRadius}}]),i}(Fe);a(Ze,"_type","point");var Qe=Ot.color;function Je(t,e){var i,n,a,r,o,s=t.getProps(["x","y","base","width","height"],e),l=s.x,u=s.y,c=s.base,h=s.width,d=s.height;return t.horizontal?(o=d/2,i=Math.min(l,c),n=Math.max(l,c),a=u-o,r=u+o):(i=l-(o=h/2),n=l+o,a=Math.min(u,c),r=Math.max(u,c)),{left:i,top:a,right:n,bottom:r}}function ti(t,e,i){return t===e?i:t===i?e:t}function ei(t,e,i,n){return t?0:Math.max(Math.min(e,n),i)}function ii(t,e,i){var n,a,r,o,s=t.options.borderWidth,l=function(t){var e=t.options.borderSkipped,i={};return e?(t.horizontal?t.base>t.x&&(e=ti(e,"left","right")):t.base<t.y&&(e=ti(e,"bottom","top")),i[e]=!0,i):i}(t);return b(s)?(n=+s.top||0,a=+s.right||0,r=+s.bottom||0,o=+s.left||0):n=a=r=o=+s||0,{t:ei(l.top,n,0,i),r:ei(l.right,a,0,e),b:ei(l.bottom,r,0,i),l:ei(l.left,o,0,e)}}function ni(t,e,i,n){var a=null===e,r=null===i,o=!(!t||a&&r)&&Je(t,n);return o&&(a||e>=o.left&&e<=o.right)&&(r||i>=o.top&&i<=o.bottom)}Ot.set("elements",{rectangle:{backgroundColor:Qe,borderColor:Qe,borderSkipped:"bottom",borderWidth:0}});var ai=function(t){function i(t){var n;return e(this,i),(n=h(this,l(i).call(this))).options=void 0,n.horizontal=void 0,n.base=void 0,n.width=void 0,n.height=void 0,t&&r(c(n),t),n}return s(i,t),n(i,[{key:"draw",value:function(t){var e,i,n,a,r,o=this.options,s=(i=Je(e=this),n=i.right-i.left,a=i.bottom-i.top,r=ii(e,n/2,a/2),{outer:{x:i.left,y:i.top,w:n,h:a},inner:{x:i.left+r.l,y:i.top+r.t,w:n-r.l-r.r,h:a-r.t-r.b}}),l=s.inner,u=s.outer;t.save(),u.w===l.w&&u.h===l.h||(t.beginPath(),t.rect(u.x,u.y,u.w,u.h),t.clip(),t.rect(l.x,l.y,l.w,l.h),t.fillStyle=o.borderColor,t.fill("evenodd")),t.fillStyle=o.backgroundColor,t.fillRect(l.x,l.y,l.w,l.h),t.restore()}},{key:"inRange",value:function(t,e,i){return ni(this,t,e,i)}},{key:"inXRange",value:function(t,e){return ni(this,t,null,e)}},{key:"inYRange",value:function(t,e){return ni(this,null,t,e)}},{key:"getCenterPoint",value:function(t){var e=this.getProps(["x","y","base","horizontal",t]),i=e.x,n=e.y,a=e.base,r=e.horizontal;return{x:r?(i+a)/2:i,y:r?n:(n+a)/2}}},{key:"getRange",value:function(t){return"x"===t?this.width/2:this.height/2}}]),i}(Fe);a(ai,"_type","rectangle");var ri=Object.freeze({__proto__:null,Arc:Re,Line:Ge,Point:Ze,Rectangle:ai});function oi(t,e,i){var n,a,r=i.barThickness,o=e.stackCount,s=e.pixels[t],l=y(r)?function(t,e){var i,n,a,r,o=t._length;for(a=1,r=e.length;a<r;++a)o=Math.min(o,Math.abs(e[a]-e[a-1]));for(a=0,r=t.ticks.length;a<r;++a)n=t.getPixelForTick(a),o=a>0?Math.min(o,Math.abs(n-i)):o,i=n;return o}(e.scale,e.pixels):-1;return y(r)?(n=l*i.categoryPercentage,a=i.barPercentage):(n=r*o,a=1),{chunk:n/o,ratio:a,start:s-n/2}}function si(t,e,i,n){var a=i.parse(t[0],n),r=i.parse(t[1],n),o=Math.min(a,r),s=Math.max(a,r),l=o,u=s;Math.abs(o)>Math.abs(s)&&(l=s,u=o),e[i.axis]=u,e._custom={barStart:l,barEnd:u,start:a,end:r,min:o,max:s}}function li(t,e,i,n){var a,r,o,s,l=t.iScale,u=t.vScale,c=l.getLabels(),h=l===u,d=[];for(a=i,r=i+n;a<r;++a)s=e[a],(o={})[l.axis]=h||l.parse(c[a],a),m(s)?si(s,o,u,a):o[u.axis]=u.parse(s,a),d.push(o);return d}function ui(t){return t&&void 0!==t.barStart&&void 0!==t.barEnd}Ot.set("bar",{hover:{mode:"index"},datasets:{categoryPercentage:.8,barPercentage:.9,animation:{numbers:{type:"number",properties:["x","y","base","width","height"]}}},scales:{x:{type:"category",offset:!0,gridLines:{offsetGridLines:!0}},y:{type:"linear",beginAtZero:!0}}});var ci=function(t){function i(){return e(this,i),h(this,l(i).apply(this,arguments))}return s(i,t),n(i,[{key:"parsePrimitiveData",value:function(t,e,i,n){return li(t,e,i,n)}},{key:"parseArrayData",value:function(t,e,i,n){return li(t,e,i,n)}},{key:"parseObjectData",value:function(t,e,i,n){var a,r,o,s,l,u=t.iScale,c=t.vScale,h=c.axis,d=[];for(a=i,r=i+n;a<r;++a)s=e[a],(o={})[u.axis]=u.parseObject(s,u.axis,a),m(l=s[h])?si(l,o,c,a):o[c.axis]=c.parseObject(s,h,a),d.push(o);return d}},{key:"getLabelAndValue",value:function(t){var e=this._cachedMeta,i=e.iScale,n=e.vScale,a=this.getParsed(t),r=a._custom,o=ui(r)?"["+r.start+", "+r.end+"]":""+n.getLabelForValue(a[n.axis]);return{label:""+i.getLabelForValue(a[i.axis]),value:o}}},{key:"initialize",value:function(){d(l(i.prototype),"initialize",this).call(this);var t=this._cachedMeta;t.stack=this.getDataset().stack,t.bar=!0}},{key:"update",value:function(t){var e=this._cachedMeta;this.updateElements(e.data,0,t)}},{key:"updateElements",value:function(t,e,i){var n,a=this,r="reset"===i,o=a._cachedMeta.vScale,s=o.getBasePixel(),l=o.isHorizontal(),u=a._getRuler(),c=a.resolveDataElementOptions(e,i),h=a.getSharedOptions(i,t[e],c),d=a.includeOptions(i,h);for(n=0;n<t.length;n++){var f=e+n,v=a.resolveDataElementOptions(f,i),p=a._calculateBarValuePixels(f,v),g=a._calculateBarIndexPixels(f,u,v),y={horizontal:l,base:r?s:p.base,x:l?r?s:p.head:g.center,y:l?g.center:r?s:p.head,height:l?g.size:void 0,width:l?void 0:g.size};d&&(y.options=v),a.updateElement(t[n],f,y,i)}a.updateSharedOptions(h,i)}},{key:"_getStacks",value:function(t){var e,i,n=this._cachedMeta.iScale,a=n.getMatchingVisibleMetas(this._type),r=n.options.stacked,o=a.length,s=[];for(e=0;e<o&&(i=a[e],(!1===r||-1===s.indexOf(i.stack)||void 0===r&&void 0===i.stack)&&s.push(i.stack),i.index!==t);++e);return s.length||s.push(void 0),s}},{key:"_getStackCount",value:function(){return this._getStacks().length}},{key:"_getStackIndex",value:function(t,e){var i=this._getStacks(t),n=void 0!==e?i.indexOf(e):-1;return-1===n?i.length-1:n}},{key:"_getRuler",value:function(){var t,e,i=this._cachedMeta,n=i.iScale,a=[];for(t=0,e=i.data.length;t<e;++t)a.push(n.getPixelForValue(this.getParsed(t)[n.axis]));return{pixels:a,start:n._startPixel,end:n._endPixel,stackCount:this._getStackCount(),scale:n}}},{key:"_calculateBarValuePixels",value:function(t,e){var i,n,a=this._cachedMeta,r=a.vScale,o=e.minBarLength,s=this.getParsed(t),l=s._custom,u=s[r.axis],c=0,h=a._stacked?this.applyStack(r,s):u;h!==u&&(c=h-u,h=u),ui(l)&&(u=l.barStart,h=l.barEnd-l.barStart,0!==u&&it(u)!==it(l.barEnd)&&(c=0),c+=u);var d=ct(r.getPixelForValue(c),r._startPixel-10,r._endPixel+10);return n=(i=r.getPixelForValue(c+h))-d,void 0!==o&&Math.abs(n)<o&&(i=d+(n=n<0?-o:o)),{size:n,base:d,head:i,center:i+n/2}}},{key:"_calculateBarIndexPixels",value:function(t,e,i){var n="flex"===i.barThickness?function(t,e,i){var n=e.pixels,a=n[t],r=t>0?n[t-1]:null,o=t<n.length-1?n[t+1]:null,s=i.categoryPercentage;null===r&&(r=a-(null===o?e.end-e.start:o-a)),null===o&&(o=a+a-r);var l=a-(a-Math.min(r,o))/2*s;return{chunk:Math.abs(o-r)/2*s/e.stackCount,ratio:i.barPercentage,start:l}}(t,e,i):oi(t,e,i),a=this._getStackIndex(this.index,this._cachedMeta.stack),r=n.start+n.chunk*a+n.chunk/2,o=Math.min(_(i.maxBarThickness,1/0),n.chunk*n.ratio);return{base:r-o/2,head:r+o/2,center:r,size:o}}},{key:"draw",value:function(){var t=this.chart,e=this._cachedMeta,i=e.vScale,n=e.data,a=n.length,r=0;for(H(t.ctx,t.chartArea);r<a;++r)isNaN(this.getParsed(r)[i.axis])||n[r].draw(this._ctx);j(t.ctx)}}]),i}(Ce);ci.prototype.dataElementType=ai,ci.prototype.dataElementOptions=["backgroundColor","borderColor","borderSkipped","borderWidth","barPercentage","barThickness","categoryPercentage","maxBarThickness","minBarLength"],Ot.set("bubble",{animation:{numbers:{properties:["x","y","borderWidth","radius"]}},scales:{x:{type:"linear"},y:{type:"linear"}},tooltips:{callbacks:{title:function(){return""}}}});var hi=function(t){function i(){return e(this,i),h(this,l(i).apply(this,arguments))}return s(i,t),n(i,[{key:"parseObjectData",value:function(t,e,i,n){var a,r,o,s=t.xScale,l=t.yScale,u=[];for(a=i,r=i+n;a<r;++a)o=e[a],u.push({x:s.parseObject(o,"x",a),y:l.parseObject(o,"y",a),_custom:o&&o.r&&+o.r});return u}},{key:"getMaxOverflow",value:function(){for(var t=(this._cachedMeta.data||[]).length-1,e=0;t>=0;--t)e=Math.max(e,this.getStyle(t,!0).radius);return e>0&&e}},{key:"getLabelAndValue",value:function(t){var e=this._cachedMeta,i=e.xScale,n=e.yScale,a=this.getParsed(t),r=i.getLabelForValue(a.x),o=n.getLabelForValue(a.y),s=a._custom;return{label:e.label,value:"("+r+", "+o+(s?", "+s:"")+")"}}},{key:"update",value:function(t){var e=this._cachedMeta.data;this.updateElements(e,0,t)}},{key:"updateElements",value:function(t,e,i){for(var n=this,a="reset"===i,r=n._cachedMeta,o=r.xScale,s=r.yScale,l=n.resolveDataElementOptions(e,i),u=n.getSharedOptions(i,t[e],l),c=n.includeOptions(i,u),h=0;h<t.length;h++){var d=t[h],f=e+h,v=!a&&n.getParsed(f),p=a?o.getPixelForDecimal(.5):o.getPixelForValue(v.x),g=a?s.getBasePixel():s.getPixelForValue(v.y),y={x:p,y:g,skip:isNaN(p)||isNaN(g)};c&&(y.options=n.resolveDataElementOptions(h,i),a&&(y.options.radius=0)),n.updateElement(d,f,y,i)}n.updateSharedOptions(u,i)}},{key:"resolveDataElementOptions",value:function(t,e){var n=this,a=n.chart,o=n.getDataset(),s=n.getParsed(t),u=d(l(i.prototype),"resolveDataElementOptions",this).call(this,t,e),c={chart:a,dataIndex:t,dataset:o,datasetIndex:n.index};return u.$shared&&(u=r({},u,{$shared:!1})),"active"!==e&&(u.radius=0),u.radius+=Ft([s&&s._custom,n._config.radius,a.options.elements.point.radius],c,t),u}}]),i}(Ce);hi.prototype.dataElementType=Ze,hi.prototype.dataElementOptions=["backgroundColor","borderColor","borderWidth","hitRadius","radius","pointStyle","rotation"];var di=Math.PI,fi=2*di,vi=di/2;Ot.set("doughnut",{animation:{numbers:{type:"number",properties:["circumference","endAngle","innerRadius","outerRadius","startAngle","x","y"]},animateRotate:!0,animateScale:!1},legend:{labels:{generateLabels:function(t){var e=t.data;return e.labels.length&&e.datasets.length?e.labels.map((function(e,i){var n=t.getDatasetMeta(0).controller.getStyle(i);return{text:e,fillStyle:n.backgroundColor,strokeStyle:n.borderColor,lineWidth:n.borderWidth,hidden:!t.getDataVisibility(i),index:i}})):[]}},onClick:function(t,e){this.chart.toggleDataVisibility(e.index),this.chart.update()}},cutoutPercentage:50,rotation:-vi,circumference:fi,tooltips:{callbacks:{title:function(){return""},label:function(t,e){var i=e.labels[t.index],n=": "+e.datasets[t.datasetIndex].data[t.index];return m(i)?(i=i.slice())[0]+=n:i+=n,i}}}});var pi=function(t){function i(t,n){var a;return e(this,i),(a=h(this,l(i).call(this,t,n))).innerRadius=void 0,a.outerRadius=void 0,a.offsetX=void 0,a.offsetY=void 0,a}return s(i,t),n(i,[{key:"linkScales",value:function(){}},{key:"parse",value:function(t,e){var i,n,a=this.getDataset().data,r=this._cachedMeta;for(i=t,n=t+e;i<n;++i)r._parsed[i]=+a[i]}},{key:"getRingIndex",value:function(t){for(var e=0,i=0;i<t;++i)this.chart.isDatasetVisible(i)&&++e;return e}},{key:"update",value:function(t){var e=this,i=e.chart,n=i.chartArea,a=i.options,r=e._cachedMeta,o=r.data,s=a.cutoutPercentage/100||0,l=e._getRingWeight(e.index),u=function(t,e,i){var n=1,a=1,r=0,o=0;if(e<fi){var s=t%fi,l=(s+=s>=di?-fi:s<-di?fi:0)+e,u=Math.cos(s),c=Math.sin(s),h=Math.cos(l),d=Math.sin(l),f=s<=0&&l>=0||l>=fi,v=s<=vi&&l>=vi||l>=fi+vi,p=s<=-vi&&l>=-vi||l>=di+vi,g=s===-di||l>=di?-1:Math.min(u,u*i,h,h*i),y=p?-1:Math.min(c,c*i,d,d*i),m=f?1:Math.max(u,u*i,h,h*i),b=v?1:Math.max(c,c*i,d,d*i);n=(m-g)/2,a=(b-y)/2,r=-(m+g)/2,o=-(b+y)/2}return{ratioX:n,ratioY:a,offsetX:r,offsetY:o}}(a.rotation,a.circumference,s),c=u.ratioX,h=u.ratioY,d=u.offsetX,f=u.offsetY,v=e.getMaxBorderWidth(),p=(n.right-n.left-v)/c,g=(n.bottom-n.top-v)/h,y=Math.max(Math.min(p,g)/2,0),m=(y-Math.max(y*s,0))/e._getVisibleDatasetWeightTotal();e.offsetX=d*y,e.offsetY=f*y,r.total=e.calculateTotal(),e.outerRadius=y-m*e._getRingWeightOffset(e.index),e.innerRadius=Math.max(e.outerRadius-m*l,0),e.updateElements(o,0,t)}},{key:"_circumference",value:function(t,e){var i=this.chart.options,n=this._cachedMeta;return e&&i.animation.animateRotate?0:this.chart.getDataVisibility(t)?this.calculateCircumference(n._parsed[t]*i.circumference/fi):0}},{key:"updateElements",value:function(t,e,i){var n,a=this,r="reset"===i,o=a.chart,s=o.chartArea,l=o.options,u=l.animation,c=(s.left+s.right)/2,h=(s.top+s.bottom)/2,d=r&&u.animateScale,f=d?0:a.innerRadius,v=d?0:a.outerRadius,p=a.resolveDataElementOptions(e,i),g=a.getSharedOptions(i,t[e],p),y=a.includeOptions(i,g),m=l.rotation;for(n=0;n<e;++n)m+=a._circumference(n,r);for(n=0;n<t.length;++n){var b=e+n,x=a._circumference(b,r),_=t[n],k={x:c+a.offsetX,y:h+a.offsetY,startAngle:m,endAngle:m+x,circumference:x,outerRadius:v,innerRadius:f};y&&(k.options=a.resolveDataElementOptions(b,i)),m+=x,a.updateElement(_,b,k,i)}a.updateSharedOptions(g,i)}},{key:"calculateTotal",value:function(){var t,e=this._cachedMeta,i=e.data,n=0;for(t=0;t<i.length;t++){var a=e._parsed[t];!isNaN(a)&&this.chart.getDataVisibility(t)&&(n+=Math.abs(a))}return n}},{key:"calculateCircumference",value:function(t){var e=this._cachedMeta.total;return e>0&&!isNaN(t)?fi*(Math.abs(t)/e):0}},{key:"getMaxBorderWidth",value:function(t){var e,i,n,a,r,o=0,s=this.chart;if(!t)for(e=0,i=s.data.datasets.length;e<i;++e)if(s.isDatasetVisible(e)){t=(n=s.getDatasetMeta(e)).data,(a=n.controller)!==this&&a.configure();break}if(!t)return 0;for(e=0,i=t.length;e<i;++e)"inner"!==(r=a.resolveDataElementOptions(e)).borderAlign&&(o=Math.max(o,r.borderWidth||0,r.hoverBorderWidth||0));return o}},{key:"_getRingWeightOffset",value:function(t){for(var e=0,i=0;i<t;++i)this.chart.isDatasetVisible(i)&&(e+=this._getRingWeight(i));return e}},{key:"_getRingWeight",value:function(t){return Math.max(_(this.chart.data.datasets[t].weight,1),0)}},{key:"_getVisibleDatasetWeightTotal",value:function(){return this._getRingWeightOffset(this.chart.data.datasets.length)||1}}]),i}(Ce);pi.prototype.dataElementType=Re,pi.prototype.dataElementOptions=["backgroundColor","borderColor","borderWidth","borderAlign","hoverBackgroundColor","hoverBorderColor","hoverBorderWidth"],Ot.set("horizontalBar",{hover:{mode:"index",axis:"y"},scales:{x:{type:"linear",beginAtZero:!0},y:{type:"category",offset:!0,gridLines:{offsetGridLines:!0}}},datasets:{categoryPercentage:.8,barPercentage:.9},elements:{rectangle:{borderSkipped:"left"}},tooltips:{mode:"index",axis:"y"}});var gi=function(t){function i(){return e(this,i),h(this,l(i).apply(this,arguments))}return s(i,t),n(i,[{key:"getValueScaleId",value:function(){return this._cachedMeta.xAxisID}},{key:"getIndexScaleId",value:function(){return this._cachedMeta.yAxisID}}]),i}(ci);Ot.set("line",{showLines:!0,spanGaps:!1,hover:{mode:"index"},scales:{x:{type:"category"},y:{type:"linear"}}});var yi=function(t){function i(t,n){var a;return e(this,i),(a=h(this,l(i).call(this,t,n)))._showLine=!1,a}return s(i,t),n(i,[{key:"update",value:function(t){var e=this,i=e._cachedMeta,n=i.dataset,a=i.data||[],r=e.chart.options,o=e._config;if((e._showLine=_(o.showLine,r.showLines))&&"resize"!==t){var s={points:a,options:e.resolveDatasetElementOptions()};e.updateElement(n,void 0,s,t)}e.updateElements(a,0,t)}},{key:"updateElements",value:function(t,e,i){for(var n,a=this,r="reset"===i,o=a._cachedMeta,s=o.xScale,l=o.yScale,u=o._stacked,c=a.resolveDataElementOptions(e,i),h=a.getSharedOptions(i,t[e],c),d=a.includeOptions(i,h),f=_(a._config.spanGaps,a.chart.options.spanGaps),v=Q(f)?f:Number.POSITIVE_INFINITY,p=0;p<t.length;++p){var g=e+p,y=t[p],m=a.getParsed(g),b=s.getPixelForValue(m.x),x=r?l.getBasePixel():l.getPixelForValue(u?a.applyStack(l,m):m.y),k={x:b,y:x,skip:isNaN(b)||isNaN(x),stop:p>0&&m.x-n.x>v};d&&(k.options=a.resolveDataElementOptions(g,i)),a.updateElement(y,g,k,i),n=m}a.updateSharedOptions(h,i)}},{key:"resolveDatasetElementOptions",value:function(t){var e=this._config,n=this.chart.options,a=n.elements.line,r=d(l(i.prototype),"resolveDatasetElementOptions",this).call(this,t);return r.spanGaps=_(e.spanGaps,n.spanGaps),r.tension=_(e.lineTension,a.tension),r.stepped=Ft([e.stepped,a.stepped]),r}},{key:"getMaxOverflow",value:function(){var t=this._cachedMeta,e=this._showLine&&t.dataset.options.borderWidth||0,i=t.data||[];if(!i.length)return e;var n=i[0].size(),a=i[i.length-1].size();return Math.max(e,n,a)/2}},{key:"draw",value:function(){var t,e,i=this._ctx,n=this.chart,a=this._cachedMeta,r=a.data||[],o=n.chartArea,s=[],l=r.length;for(this._showLine&&a.dataset.draw(i,o),t=0;t<l;++t)(e=r[t]).active?s.push(e):e.draw(i,o);for(t=0,l=s.length;t<l;++t)s[t].draw(i,o)}}]),i}(Ce);yi.prototype.datasetElementType=Ge,yi.prototype.dataElementType=Ze,yi.prototype.datasetElementOptions=["backgroundColor","borderCapStyle","borderColor","borderDash","borderDashOffset","borderJoinStyle","borderWidth","capBezierPoints","cubicInterpolationMode","fill"],yi.prototype.dataElementOptions={backgroundColor:"pointBackgroundColor",borderColor:"pointBorderColor",borderWidth:"pointBorderWidth",hitRadius:"pointHitRadius",hoverHitRadius:"pointHitRadius",hoverBackgroundColor:"pointHoverBackgroundColor",hoverBorderColor:"pointHoverBorderColor",hoverBorderWidth:"pointHoverBorderWidth",hoverRadius:"pointHoverRadius",pointStyle:"pointStyle",radius:"pointRadius",rotation:"pointRotation"},Ot.set("polarArea",{animation:{numbers:{type:"number",properties:["x","y","startAngle","endAngle","innerRadius","outerRadius"]},animateRotate:!0,animateScale:!0},scales:{r:{type:"radialLinear",angleLines:{display:!1},beginAtZero:!0,gridLines:{circular:!0},pointLabels:{display:!1}}},startAngle:0,legend:{labels:{generateLabels:function(t){var e=t.data;return e.labels.length&&e.datasets.length?e.labels.map((function(e,i){var n=t.getDatasetMeta(0).controller.getStyle(i);return{text:e,fillStyle:n.backgroundColor,strokeStyle:n.borderColor,lineWidth:n.borderWidth,hidden:!t.getDataVisibility(i),index:i}})):[]}},onClick:function(t,e){this.chart.toggleDataVisibility(e.index),this.chart.update()}},tooltips:{callbacks:{title:function(){return""},label:function(t,e){return e.labels[t.index]+": "+t.value}}}});var mi=function(t){function i(t,n){var a;return e(this,i),(a=h(this,l(i).call(this,t,n))).innerRadius=void 0,a.outerRadius=void 0,a}return s(i,t),n(i,[{key:"getIndexScaleId",value:function(){return this._cachedMeta.rAxisID}},{key:"getValueScaleId",value:function(){return this._cachedMeta.rAxisID}},{key:"update",value:function(t){var e=this._cachedMeta.data;this._updateRadius(),this.updateElements(e,0,t)}},{key:"_updateRadius",value:function(){var t=this,e=t.chart,i=e.chartArea,n=e.options,a=Math.min(i.right-i.left,i.bottom-i.top),r=Math.max(a/2,0),o=(r-Math.max(n.cutoutPercentage?r/100*n.cutoutPercentage:1,0))/e.getVisibleDatasetCount();t.outerRadius=r-o*t.index,t.innerRadius=t.outerRadius-o}},{key:"updateElements",value:function(t,e,i){var n,a=this,r="reset"===i,o=a.chart,s=a.getDataset(),l=o.options,u=l.animation,c=o.scales.r,h=c.xCenter,d=c.yCenter,f=nt(l.startAngle)-.5*Math.PI,v=f;for(a._cachedMeta.count=a.countVisibleElements(),n=0;n<e;++n)v+=a._computeAngle(n);for(n=0;n<t.length;n++){var p=t[n],g=e+n,y=v,m=v+a._computeAngle(g),b=this.chart.getDataVisibility(g)?c.getDistanceFromCenterForValue(s.data[g]):0;v=m,r&&(u.animateScale&&(b=0),u.animateRotate&&(y=f,m=f));var x={x:h,y:d,innerRadius:0,outerRadius:b,startAngle:y,endAngle:m,options:a.resolveDataElementOptions(g)};a.updateElement(p,g,x,i)}}},{key:"countVisibleElements",value:function(){var t=this,e=this.getDataset(),i=this._cachedMeta,n=0;return i.data.forEach((function(i,a){!isNaN(e.data[a])&&t.chart.getDataVisibility(a)&&n++})),n}},{key:"_computeAngle",value:function(t){var e=this,i=e._cachedMeta.count,n=e.getDataset();if(isNaN(n.data[t])||!this.chart.getDataVisibility(t))return 0;var a={chart:e.chart,dataIndex:t,dataset:n,datasetIndex:e.index};return Ft([e.chart.options.elements.arc.angle,2*Math.PI/i],a,t)}}]),i}(Ce);mi.prototype.dataElementType=Re,mi.prototype.dataElementOptions=["backgroundColor","borderColor","borderWidth","borderAlign","hoverBackgroundColor","hoverBorderColor","hoverBorderWidth"],Ot.set("pie",w(Ot.doughnut)),Ot.set("pie",{cutoutPercentage:0}),Ot.set("radar",{spanGaps:!1,scales:{r:{type:"radialLinear"}},elements:{line:{tension:0}}});var bi=function(t){function i(){return e(this,i),h(this,l(i).apply(this,arguments))}return s(i,t),n(i,[{key:"getIndexScaleId",value:function(){return this._cachedMeta.rAxisID}},{key:"getValueScaleId",value:function(){return this._cachedMeta.rAxisID}},{key:"getLabelAndValue",value:function(t){var e=this._cachedMeta.vScale,i=this.getParsed(t);return{label:e.getLabels()[t],value:""+e.getLabelForValue(i[e.axis])}}},{key:"update",value:function(t){var e=this,i=e._cachedMeta,n=i.dataset,a=i.data||[],r={points:a,_loop:!0,_fullLoop:i.iScale.getLabels().length===a.length,options:e.resolveDatasetElementOptions()};e.updateElement(n,void 0,r,t),e.updateElements(a,0,t),n.updateControlPoints(e.chart.chartArea)}},{key:"updateElements",value:function(t,e,i){var n,a=this.getDataset(),r=this.chart.scales.r,o="reset"===i;for(n=0;n<t.length;n++){var s=t[n],l=e+n,u=this.resolveDataElementOptions(l),c=r.getPointPositionForValue(l,a.data[l]),h=o?r.xCenter:c.x,d=o?r.yCenter:c.y,f={x:h,y:d,angle:c.angle,skip:isNaN(h)||isNaN(d),options:u};this.updateElement(s,l,f,i)}}},{key:"resolveDatasetElementOptions",value:function(t){var e=this._config,n=this.chart.options,a=d(l(i.prototype),"resolveDatasetElementOptions",this).call(this,t);return a.spanGaps=_(e.spanGaps,n.spanGaps),a.tension=_(e.lineTension,n.elements.line.tension),a}}]),i}(Ce);bi.prototype.datasetElementType=Ge,bi.prototype.dataElementType=Ze,bi.prototype.datasetElementOptions=["backgroundColor","borderColor","borderCapStyle","borderDash","borderDashOffset","borderJoinStyle","borderWidth","fill"],bi.prototype.dataElementOptions={backgroundColor:"pointBackgroundColor",borderColor:"pointBorderColor",borderWidth:"pointBorderWidth",hitRadius:"pointHitRadius",hoverBackgroundColor:"pointHoverBackgroundColor",hoverBorderColor:"pointHoverBorderColor",hoverBorderWidth:"pointHoverBorderWidth",hoverRadius:"pointHoverRadius",pointStyle:"pointStyle",radius:"pointRadius",rotation:"pointRotation"},Ot.set("scatter",{scales:{x:{type:"linear"},y:{type:"linear"}},datasets:{showLine:!1},tooltips:{callbacks:{title:function(){return""},label:function(t){return"("+t.label+", "+t.value+")"}}}});var xi={bar:ci,bubble:hi,doughnut:pi,horizontalBar:gi,line:yi,polarArea:mi,pie:pi,radar:bi,scatter:yi};function _i(t,e,i){for(var n,a=t.length-1,r=0;a-r>1;)t[n=r+a>>1][e]<i?r=n:a=n;return{lo:r,hi:a}}function ki(t,e,i){for(var n,a=t.length-1,r=0;a-r>1;)t[n=r+a>>1][e]<i?a=n:r=n;return{lo:r,hi:a}}function Mi(t,e){return"native"in t?{x:t.x,y:t.y}:pe.dom.getRelativePosition(t,e)}function wi(t,e){for(var i,n,a,r=t.getSortedVisibleDatasetMetas(),o=0,s=r.length;o<s;++o){var l=r[o];i=l.index;for(var u=0,c=(n=l.data).length;u<c;++u)(a=n[u]).skip||e(a,i,u)}}function Si(t,e,i,n){var a=t.controller,r=t.data,o=t._sorted,s=a._cachedMeta.iScale;if(s&&e===s.axis&&o&&r.length){var l=s._reversePixels?ki:_i;if(!n)return l(r,e,i);if(a._sharedOptions){var u=r[0],c="function"==typeof u.getRange&&u.getRange(e);if(c){var h=l(r,e,i-c),d=l(r,e,i+c);return{lo:h.lo,hi:d.hi}}}}return{lo:0,hi:r.length-1}}function Pi(t,e,i,n,a){for(var r=t.getSortedVisibleDatasetMetas(),o=i[e],s=0,l=r.length;s<l;++s)for(var u=r[s],c=u.index,h=u.data,d=Si(r[s],e,o,a),f=d.lo,v=d.hi,p=f;p<=v;++p){var g=h[p];g.skip||n(g,c,p)}}function Di(t,e,i,n){var a=[];if(!N(e,t.chartArea))return a;return Pi(t,i,e,(function(t,i,r){t.inRange(e.x,e.y,n)&&a.push({element:t,datasetIndex:i,index:r})}),!0),a}function Oi(t,e,i,n,a){var r=function(t){var e=-1!==t.indexOf("x"),i=-1!==t.indexOf("y");return function(t,n){var a=e?Math.abs(t.x-n.x):0,r=i?Math.abs(t.y-n.y):0;return Math.sqrt(Math.pow(a,2)+Math.pow(r,2))}}(i),o=Number.POSITIVE_INFINITY,s=[];if(!N(e,t.chartArea))return s;return Pi(t,i,e,(function(t,i,l){if(!n||t.inRange(e.x,e.y,a)){var u=t.getCenterPoint(a),c=r(e,u);c<o?(s=[{element:t,datasetIndex:i,index:l}],o=c):c===o&&s.push({element:t,datasetIndex:i,index:l})}})),s}var Ai={modes:{index:function(t,e,i,n){var a=Mi(e,t),r=i.axis||"x",o=i.intersect?Di(t,a,r,n):Oi(t,a,r,!1,n),s=[];return o.length?(t.getSortedVisibleDatasetMetas().forEach((function(t){var e=o[0].index,i=t.data[e];i&&!i.skip&&s.push({element:i,datasetIndex:t.index,index:e})})),s):[]},dataset:function(t,e,i,n){var a=Mi(e,t),r=i.axis||"xy",o=i.intersect?Di(t,a,r,n):Oi(t,a,r,!1,n);if(o.length>0){var s=o[0].datasetIndex,l=t.getDatasetMeta(s).data;o=[];for(var u=0;u<l.length;++u)o.push({element:l[u],datasetIndex:s,index:u})}return o},point:function(t,e,i,n){return Di(t,Mi(e,t),i.axis||"xy",n)},nearest:function(t,e,i,n){return Oi(t,Mi(e,t),i.axis||"xy",i.intersect,n)},x:function(t,e,i,n){var a=Mi(e,t),r=[],o=!1;return wi(t,(function(t,e,i){t.inXRange(a.x,n)&&r.push({element:t,datasetIndex:e,index:i}),t.inRange(a.x,a.y,n)&&(o=!0)})),i.intersect&&!o?[]:r},y:function(t,e,i,n){var a=Mi(e,t),r=[],o=!1;return wi(t,(function(t,e,i){t.inYRange(a.y,n)&&r.push({element:t,datasetIndex:e,index:i}),t.inRange(a.x,a.y,n)&&(o=!0)})),i.intersect&&!o?[]:r}}},Ti=["left","top","right","bottom"];function Ci(t,e){return t.filter((function(t){return t.pos===e}))}function Fi(t,e){return t.filter((function(t){return-1===Ti.indexOf(t.pos)&&t.box.axis===e}))}function Ei(t,e){return t.sort((function(t,i){var n=e?i:t,a=e?t:i;return n.weight===a.weight?n.index-a.index:n.weight-a.weight}))}function Ii(t,e,i,n){return Math.max(t[i],e[i])+Math.max(t[n],e[n])}function Li(t,e,i){var n=i.box,a=t.maxPadding;if(i.size&&(t[i.pos]-=i.size),i.size=i.horizontal?n.height:n.width,t[i.pos]+=i.size,n.getPadding){var r=n.getPadding();a.top=Math.max(a.top,r.top),a.left=Math.max(a.left,r.left),a.bottom=Math.max(a.bottom,r.bottom),a.right=Math.max(a.right,r.right)}var o=e.outerWidth-Ii(a,t,"left","right"),s=e.outerHeight-Ii(a,t,"top","bottom");if(o!==t.w||s!==t.h)return t.w=o,t.h=s,i.horizontal?o!==t.w:s!==t.h}function Ri(t,e){var i=e.maxPadding;function n(t){var n={left:0,top:0,right:0,bottom:0};return t.forEach((function(t){n[t]=Math.max(e[t],i[t])})),n}return n(t?["left","right"]:["top","bottom"])}function zi(t,e,i){var n,a,r,o,s,l,u=[];for(n=0,a=t.length;n<a;++n)(o=(r=t[n]).box).update(r.width||e.w,r.height||e.h,Ri(r.horizontal,e)),Li(e,i,r)&&(l=!0,u.length&&(s=!0)),o.fullWidth||u.push(r);return s&&zi(u,e,i)||l}function Bi(t,e,i){var n,a,r,o,s=i.padding,l=e.x,u=e.y;for(n=0,a=t.length;n<a;++n)o=(r=t[n]).box,r.horizontal?(o.left=o.fullWidth?s.left:e.left,o.right=o.fullWidth?i.outerWidth-s.right:e.left+e.w,o.top=u,o.bottom=u+o.height,o.width=o.right-o.left,u=o.bottom):(o.left=l,o.right=l+o.width,o.top=e.top,o.bottom=e.top+e.h,o.height=o.bottom-o.top,l=o.right);e.x=l,e.y=u}Ot.set("layout",{padding:{top:0,right:0,bottom:0,left:0}});var Vi={defaults:{},addBox:function(t,e){t.boxes||(t.boxes=[]),e.fullWidth=e.fullWidth||!1,e.position=e.position||"top",e.weight=e.weight||0,e._layers=e._layers||function(){return[{z:0,draw:function(t){e.draw(t)}}]},t.boxes.push(e)},removeBox:function(t,e){var i=t.boxes?t.boxes.indexOf(e):-1;-1!==i&&t.boxes.splice(i,1)},configure:function(t,e,i){for(var n,a=["fullWidth","position","weight"],r=a.length,o=0;o<r;++o)n=a[o],Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n])},update:function(t,e,i){if(t){var n=Tt((t.options.layout||{}).padding),a=e-n.width,o=i-n.height,s=function(t){var e=function(t){var e,i,n,a=[];for(e=0,i=(t||[]).length;e<i;++e)n=t[e],a.push({index:e,box:n,pos:n.position,horizontal:n.isHorizontal(),weight:n.weight});return a}(t),i=Ei(Ci(e,"left"),!0),n=Ei(Ci(e,"right")),a=Ei(Ci(e,"top"),!0),r=Ei(Ci(e,"bottom")),o=Fi(e,"x"),s=Fi(e,"y");return{leftAndTop:i.concat(a),rightAndBottom:n.concat(s).concat(r).concat(o),chartArea:Ci(e,"chartArea"),vertical:i.concat(n).concat(s),horizontal:a.concat(r).concat(o)}}(t.boxes),l=s.vertical,u=s.horizontal,c=Object.freeze({outerWidth:e,outerHeight:i,padding:n,availableWidth:a,vBoxMaxWidth:a/2/l.length,hBoxMaxHeight:o/2}),h=r({maxPadding:r({},n),w:a,h:o,x:n.left,y:n.top},n);!function(t,e){var i,n,a;for(i=0,n=t.length;i<n;++i)(a=t[i]).width=a.horizontal?a.box.fullWidth&&e.availableWidth:e.vBoxMaxWidth,a.height=a.horizontal&&e.hBoxMaxHeight}(l.concat(u),c),zi(l,h,c),zi(u,h,c)&&zi(l,h,c),function(t){var e=t.maxPadding;function i(i){var n=Math.max(e[i]-t[i],0);return t[i]+=n,n}t.y+=i("top"),t.x+=i("left"),i("right"),i("bottom")}(h),Bi(s.leftAndTop,h,c),h.x+=h.w,h.y+=h.h,Bi(s.rightAndBottom,h,c),t.chartArea={left:h.left,top:h.top,right:h.left+h.w,bottom:h.top+h.h,height:h.h,width:h.w},M(s.chartArea,(function(e){var i=e.box;r(i,t.chartArea),i.update(h.w,h.h)}))}}},Wi=function(){function t(){e(this,t)}return n(t,[{key:"acquireContext",value:function(t,e){}},{key:"releaseContext",value:function(t){return!1}},{key:"addEventListener",value:function(t,e,i){}},{key:"removeEventListener",value:function(t,e,i){}},{key:"getDevicePixelRatio",value:function(){return 1}}]),t}(),Ni=function(t){function i(){return e(this,i),h(this,l(i).apply(this,arguments))}return s(i,t),n(i,[{key:"acquireContext",value:function(t){return t&&t.getContext&&t.getContext("2d")||null}}]),i}(Wi),Hi=function(){if("undefined"!=typeof Map)return Map;function t(t,e){var i=-1;return t.some((function(t,n){return t[0]===e&&(i=n,!0)})),i}return(function(){function e(){this.__entries__=[]}return Object.defineProperty(e.prototype,"size",{get:function(){return this.__entries__.length},enumerable:!0,configurable:!0}),e.prototype.get=function(e){var i=t(this.__entries__,e),n=this.__entries__[i];return n&&n[1]},e.prototype.set=function(e,i){var n=t(this.__entries__,e);~n?this.__entries__[n][1]=i:this.__entries__.push([e,i])},e.prototype.delete=function(e){var i=this.__entries__,n=t(i,e);~n&&i.splice(n,1)},e.prototype.has=function(e){return!!~t(this.__entries__,e)},e.prototype.clear=function(){this.__entries__.splice(0)},e.prototype.forEach=function(t,e){void 0===e&&(e=null);for(var i=0,n=this.__entries__;i<n.length;i++){var a=n[i];t.call(e,a[1],a[0])}},e}())}(),ji="undefined"!=typeof window&&"undefined"!=typeof document&&window.document===document,Yi="undefined"!=typeof global&&global.Math===Math?global:"undefined"!=typeof self&&self.Math===Math?self:"undefined"!=typeof window&&window.Math===Math?window:Function("return this")(),Ui="function"==typeof requestAnimationFrame?requestAnimationFrame.bind(Yi):function(t){return setTimeout((function(){return t(Date.now())}),1e3/60)};var Xi=["top","right","bottom","left","width","height","size","weight"],qi="undefined"!=typeof MutationObserver,$i=function(){function t(){this.connected_=!1,this.mutationEventsAdded_=!1,this.mutationsObserver_=null,this.observers_=[],this.onTransitionEnd_=this.onTransitionEnd_.bind(this),this.refresh=function(t,e){var i=!1,n=!1,a=0;function r(){i&&(i=!1,t()),n&&s()}function o(){Ui(r)}function s(){var t=Date.now();if(i){if(t-a<2)return;n=!0}else i=!0,n=!1,setTimeout(o,e);a=t}return s}(this.refresh.bind(this),20)}return t.prototype.addObserver=function(t){~this.observers_.indexOf(t)||this.observers_.push(t),this.connected_||this.connect_()},t.prototype.removeObserver=function(t){var e=this.observers_,i=e.indexOf(t);~i&&e.splice(i,1),!e.length&&this.connected_&&this.disconnect_()},t.prototype.refresh=function(){this.updateObservers_()&&this.refresh()},t.prototype.updateObservers_=function(){var t=this.observers_.filter((function(t){return t.gatherActive(),t.hasActive()}));return t.forEach((function(t){return t.broadcastActive()})),t.length>0},t.prototype.connect_=function(){ji&&!this.connected_&&(document.addEventListener("transitionend",this.onTransitionEnd_),window.addEventListener("resize",this.refresh),qi?(this.mutationsObserver_=new MutationObserver(this.refresh),this.mutationsObserver_.observe(document,{attributes:!0,childList:!0,characterData:!0,subtree:!0})):(document.addEventListener("DOMSubtreeModified",this.refresh),this.mutationEventsAdded_=!0),this.connected_=!0)},t.prototype.disconnect_=function(){ji&&this.connected_&&(document.removeEventListener("transitionend",this.onTransitionEnd_),window.removeEventListener("resize",this.refresh),this.mutationsObserver_&&this.mutationsObserver_.disconnect(),this.mutationEventsAdded_&&document.removeEventListener("DOMSubtreeModified",this.refresh),this.mutationsObserver_=null,this.mutationEventsAdded_=!1,this.connected_=!1)},t.prototype.onTransitionEnd_=function(t){var e=t.propertyName,i=void 0===e?"":e;Xi.some((function(t){return!!~i.indexOf(t)}))&&this.refresh()},t.getInstance=function(){return this.instance_||(this.instance_=new t),this.instance_},t.instance_=null,t}(),Gi=function(t,e){for(var i=0,n=Object.keys(e);i<n.length;i++){var a=n[i];Object.defineProperty(t,a,{value:e[a],enumerable:!1,writable:!1,configurable:!0})}return t},Ki=function(t){return t&&t.ownerDocument&&t.ownerDocument.defaultView||Yi},Zi=an(0,0,0,0);function Qi(t){return parseFloat(t)||0}function Ji(t){for(var e=[],i=1;i<arguments.length;i++)e[i-1]=arguments[i];return e.reduce((function(e,i){return e+Qi(t["border-"+i+"-width"])}),0)}function tn(t){var e=t.clientWidth,i=t.clientHeight;if(!e&&!i)return Zi;var n=Ki(t).getComputedStyle(t),a=function(t){for(var e={},i=0,n=["top","right","bottom","left"];i<n.length;i++){var a=n[i],r=t["padding-"+a];e[a]=Qi(r)}return e}(n),r=a.left+a.right,o=a.top+a.bottom,s=Qi(n.width),l=Qi(n.height);if("border-box"===n.boxSizing&&(Math.round(s+r)!==e&&(s-=Ji(n,"left","right")+r),Math.round(l+o)!==i&&(l-=Ji(n,"top","bottom")+o)),!function(t){return t===Ki(t).document.documentElement}(t)){var u=Math.round(s+r)-e,c=Math.round(l+o)-i;1!==Math.abs(u)&&(s-=u),1!==Math.abs(c)&&(l-=c)}return an(a.left,a.top,s,l)}var en="undefined"!=typeof SVGGraphicsElement?function(t){return t instanceof Ki(t).SVGGraphicsElement}:function(t){return t instanceof Ki(t).SVGElement&&"function"==typeof t.getBBox};function nn(t){return ji?en(t)?function(t){var e=t.getBBox();return an(0,0,e.width,e.height)}(t):tn(t):Zi}function an(t,e,i,n){return{x:t,y:e,width:i,height:n}}var rn=function(){function t(t){this.broadcastWidth=0,this.broadcastHeight=0,this.contentRect_=an(0,0,0,0),this.target=t}return t.prototype.isActive=function(){var t=nn(this.target);return this.contentRect_=t,t.width!==this.broadcastWidth||t.height!==this.broadcastHeight},t.prototype.broadcastRect=function(){var t=this.contentRect_;return this.broadcastWidth=t.width,this.broadcastHeight=t.height,t},t}(),on=function(t,e){var i,n,a,r,o,s,l,u=(n=(i=e).x,a=i.y,r=i.width,o=i.height,s="undefined"!=typeof DOMRectReadOnly?DOMRectReadOnly:Object,l=Object.create(s.prototype),Gi(l,{x:n,y:a,width:r,height:o,top:a,right:n+r,bottom:o+a,left:n}),l);Gi(this,{target:t,contentRect:u})},sn=function(){function t(t,e,i){if(this.activeObservations_=[],this.observations_=new Hi,"function"!=typeof t)throw new TypeError("The callback provided as parameter 1 is not a function.");this.callback_=t,this.controller_=e,this.callbackCtx_=i}return t.prototype.observe=function(t){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if("undefined"!=typeof Element&&Element instanceof Object){if(!(t instanceof Ki(t).Element))throw new TypeError('parameter 1 is not of type "Element".');var e=this.observations_;e.has(t)||(e.set(t,new rn(t)),this.controller_.addObserver(this),this.controller_.refresh())}},t.prototype.unobserve=function(t){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if("undefined"!=typeof Element&&Element instanceof Object){if(!(t instanceof Ki(t).Element))throw new TypeError('parameter 1 is not of type "Element".');var e=this.observations_;e.has(t)&&(e.delete(t),e.size||this.controller_.removeObserver(this))}},t.prototype.disconnect=function(){this.clearActive(),this.observations_.clear(),this.controller_.removeObserver(this)},t.prototype.gatherActive=function(){var t=this;this.clearActive(),this.observations_.forEach((function(e){e.isActive()&&t.activeObservations_.push(e)}))},t.prototype.broadcastActive=function(){if(this.hasActive()){var t=this.callbackCtx_,e=this.activeObservations_.map((function(t){return new on(t.target,t.broadcastRect())}));this.callback_.call(t,e,t),this.clearActive()}},t.prototype.clearActive=function(){this.activeObservations_.splice(0)},t.prototype.hasActive=function(){return this.activeObservations_.length>0},t}(),ln="undefined"!=typeof WeakMap?new WeakMap:new Hi,un=function t(e){if(!(this instanceof t))throw new TypeError("Cannot call a class as a function.");if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");var i=$i.getInstance(),n=new sn(e,i,this);ln.set(this,n)};["observe","unobserve","disconnect"].forEach((function(t){un.prototype[t]=function(){var e;return(e=ln.get(this))[t].apply(e,arguments)}}));var cn=void 0!==Yi.ResizeObserver?Yi.ResizeObserver:un,hn={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"};function dn(t,e){var i=pe.dom.getStyle(t,e),n=i&&i.match(/^(\d+)(\.\d+)?px$/);return n?+n[1]:void 0}var fn=!!function(){var t=!1;try{var e={get passive(){return t=!0,!1}};window.addEventListener("test",null,e),window.removeEventListener("test",null,e)}catch(t){}return t}()&&{passive:!0};function vn(t,e){var i=!1,n=[];return function(){for(var a=arguments.length,r=new Array(a),o=0;o<a;o++)r[o]=arguments[o];n=Array.prototype.slice.call(r),i||(i=!0,pe.requestAnimFrame.call(window,(function(){i=!1,t.apply(e,n)})))}}function pn(t,e){var i=vn((function(i,n){var a=t.clientWidth;e(i,n),a<t.clientWidth&&e()}),window),n=new cn((function(t){var e=t[0];i(e.contentRect.width,e.contentRect.height)}));return n.observe(t),n}function gn(t,e){var i=bt(t);if(i){var n=new MutationObserver((function(i){i.forEach((function(i){for(var n=0;n<i.removedNodes.length;n++)if(i.removedNodes[n]===t){e();break}}))}));return n.observe(i,{childList:!0}),n}}function yn(t,e){var i=t[e];i&&(i.disconnect(),t[e]=void 0)}function mn(t){yn(t,"attach"),yn(t,"detach"),yn(t,"resize")}function bn(t,e,i){var n=function(){return bn(t,e,i)};mn(e);var a,r,o,s=bt(t);s?(e.resize=pn(s,i),e.detach=gn(t,n)):e.attach=(a=t,r=function(){yn(e,"attach");var a=bt(t);e.resize=pn(a,i),e.detach=gn(t,n)},(o=new MutationObserver((function(t){var e=bt(a);t.forEach((function(t){for(var i=0;i<t.addedNodes.length;i++){var n=t.addedNodes[i];n!==a&&n!==e||r(t.target)}}))}))).observe(document,{childList:!0,subtree:!0}),o)}var xn=function(t){function i(){return e(this,i),h(this,l(i).apply(this,arguments))}return s(i,t),n(i,[{key:"acquireContext",value:function(t,e){var i=t&&t.getContext&&t.getContext("2d");return i&&i.canvas===t?(function(t,e){var i=t.style,n=t.getAttribute("height"),a=t.getAttribute("width");if(t.$chartjs={initial:{height:n,width:a,style:{display:i.display,height:i.height,width:i.width}}},i.display=i.display||"block",i.boxSizing=i.boxSizing||"border-box",null===a||""===a){var r=dn(t,"width");void 0!==r&&(t.width=r)}if(null===n||""===n)if(""===t.style.height)t.height=t.width/(e.options.aspectRatio||2);else{var o=dn(t,"height");void 0!==o&&(t.height=o)}}(t,e),i):null}},{key:"releaseContext",value:function(t){var e=t.canvas;if(!e.$chartjs)return!1;var i=e.$chartjs.initial;["height","width"].forEach((function(t){var n=i[t];pe.isNullOrUndef(n)?e.removeAttribute(t):e.setAttribute(t,n)}));var n=i.style||{};return Object.keys(n).forEach((function(t){e.style[t]=n[t]})),e.width=e.width,delete e.$chartjs,!0}},{key:"addEventListener",value:function(t,e,i){this.removeEventListener(t,e);var n=t.canvas,a=t.$proxies||(t.$proxies={});if("resize"===e)return bn(n,a,i);!function(t,e,i){t.addEventListener(e,i,fn)}(n,e,a[e]=vn((function(e){i(function(t,e){var i=hn[t.type]||t.type,n=pe.dom.getRelativePosition(t,e);return function(t,e,i,n,a){return{type:t,chart:e,native:a||null,x:void 0!==i?i:null,y:void 0!==n?n:null}}(i,e,n.x,n.y,t)}(e,t))}),t))}},{key:"removeEventListener",value:function(t,e){var i=t.canvas,n=t.$proxies||(t.$proxies={});if("resize"===e)return mn(n);var a=n[e];a&&(!function(t,e,i){t.removeEventListener(e,i,fn)}(i,e,a),n[e]=void 0)}},{key:"getDevicePixelRatio",value:function(){return window.devicePixelRatio}}]),i}(Wi),_n={BasicPlatform:Ni,DomPlatform:xn,BasePlatform:Wi};Ot.set("plugins",{});var kn=new(function(){function t(){e(this,t),this._plugins=[],this._cacheId=0}return n(t,[{key:"register",value:function(t){var e=this._plugins;[].concat(t).forEach((function(t){-1===e.indexOf(t)&&e.push(t)})),this._cacheId++}},{key:"unregister",value:function(t){var e=this._plugins;[].concat(t).forEach((function(t){var i=e.indexOf(t);-1!==i&&e.splice(i,1)})),this._cacheId++}},{key:"clear",value:function(){this._plugins=[],this._cacheId++}},{key:"count",value:function(){return this._plugins.length}},{key:"getAll",value:function(){return this._plugins}},{key:"notify",value:function(t,e,i){var n,a,r,o,s,l=this._descriptors(t),u=l.length;for(n=0;n<u;++n)if("function"==typeof(s=(r=(a=l[n]).plugin)[e])&&((o=[t].concat(i||[])).push(a.options),!1===s.apply(r,o)))return!1;return!0}},{key:"_descriptors",value:function(t){var e=t.$plugins||(t.$plugins={});if(e.id===this._cacheId)return e.descriptors;var i=[],n=[],a=t&&t.config||{},r=a.options&&a.options.plugins||{};return this._plugins.concat(a.plugins||[]).forEach((function(t){if(-1===i.indexOf(t)){var e=t.id,a=r[e];!1!==a&&(!0===a&&(a=w(Ot.plugins[e])),i.push(t),n.push({plugin:t,options:a||{}}))}})),e.descriptors=n,e.id=this._cacheId,n}},{key:"invalidate",value:function(t){delete t.$plugins}}]),t}()),Mn={constructors:{},defaults:{},registerScale:function(t){var e=t.id;this.constructors[e]=t,this.defaults[e]=w(t.defaults)},getScaleConstructor:function(t){return Object.prototype.hasOwnProperty.call(this.constructors,t)?this.constructors[t]:void 0},getScaleDefaults:function(t){return Object.prototype.hasOwnProperty.call(this.defaults,t)?P({},[Ot.scale,this.defaults[t]]):{}},updateScaleDefaults:function(t,e){Object.prototype.hasOwnProperty.call(this.defaults,t)&&(this.defaults[t]=r(this.defaults[t],e))},addScalesToLayout:function(t){M(t.scales,(function(e){e.fullWidth=e.options.fullWidth,e.position=e.options.position,e.weight=e.options.weight,Vi.addBox(t,e)}))}},wn=pe.valueOrDefault;function Sn(t,e){e=e||{};var i=Ot[t.type]||{scales:{}},n=e.scales||{},a={},r={};return Object.keys(n).forEach((function(t){var e=t[0];a[e]=a[e]||t,r[t]=pe.mergeIf({},[n[t],i.scales[e]])})),e.scale&&(r[e.scale.id||"r"]=pe.mergeIf({},[e.scale,i.scales.r]),a.r=a.r||e.scale.id||"r"),t.data.datasets.forEach((function(e){var i=(Ot[e.type||t.type]||{scales:{}}).scales||{};Object.keys(i).forEach((function(t){var o=e[t+"AxisID"]||a[t]||t;r[o]=r[o]||{},pe.mergeIf(r[o],[n[o],i[t]])}))})),Object.keys(r).forEach((function(t){var e=r[t];pe.mergeIf(e,Mn.getScaleDefaults(e.type))})),r}function Pn(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];return pe.merge({},e,{merger:function(t,e,i,n){"scales"!==t&&"scale"!==t&&pe._merger(t,e,i,n)}})}var Dn=new Set(["top","bottom","left","right","chartArea"]);function On(t,e){return"top"===t||"bottom"===t||!Dn.has(t)&&"x"===e}function An(t,e){return function(i,n){return i[t]===n[t]?i[e]-n[e]:i[t]-n[t]}}function Tn(t){var e=t.chart,i=e.options.animation;kn.notify(e,"afterRender"),pe.callback(i&&i.onComplete,[t],e)}function Cn(t){var e=t.chart,i=e.options.animation;pe.callback(i&&i.onProgress,[t],e)}function Fn(){return"undefined"!=typeof window&&"undefined"!=typeof document}var En=function(){function t(i,n){e(this,t);var a=this;n=function(t){var e=(t=t||{}).data=t.data||{datasets:[],labels:[]};e.datasets=e.datasets||[],e.labels=e.labels||[];var i=Sn(t,t.options);return t.options=Pn(Ot,Ot[t.type],t.options||{}),t.options.scales=i,t}(n);var r=function(t){return Fn()&&"string"==typeof t?t=document.getElementById(t):t.length&&(t=t[0]),t&&t.canvas&&(t=t.canvas),t}(i);this.platform=a._initializePlatform(r,n);var o=a.platform.acquireContext(r,n),s=o&&o.canvas,l=s&&s.height,u=s&&s.width;this.id=pe.uid(),this.ctx=o,this.canvas=s,this.config=n,this.width=u,this.height=l,this.aspectRatio=l?u/l:null,this.options=n.options,this._bufferedRender=!1,this._layers=[],this._metasets=[],this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this.data=void 0,this.active=void 0,this.lastActive=[],this._lastEvent=void 0,this._listeners={},this._sortedMetasets=[],this._updating=!1,this.scales={},this.scale=void 0,this.$plugins=void 0,this.$proxies={},this._hiddenIndices={},t.instances[a.id]=a,Object.defineProperty(a,"data",{get:function(){return a.config.data},set:function(t){a.config.data=t}}),o&&s?(ge.listen(a,"complete",Tn),ge.listen(a,"progress",Cn),a._initialize(),a.update()):console.error("Failed to create chart: can't acquire context from the given item")}return n(t,[{key:"_initialize",value:function(){var t=this;return kn.notify(t,"beforeInit"),t.options.responsive?t.resize(!0):pe.dom.retinaScale(t,t.options.devicePixelRatio),t.bindEvents(),kn.notify(t,"afterInit"),t}},{key:"_initializePlatform",value:function(t,e){return e.platform?new e.platform:!Fn()||"undefined"!=typeof OffscreenCanvas&&t instanceof OffscreenCanvas?new Ni:new xn}},{key:"clear",value:function(){return pe.canvas.clear(this),this}},{key:"stop",value:function(){return ge.stop(this),this}},{key:"resize",value:function(t,e,i){var n=this,a=n.options,r=n.canvas,o=a.maintainAspectRatio&&n.aspectRatio;void 0!==e&&void 0!==i||(e=wt(r),i=St(r));var s=Math.max(0,Math.floor(e)),l=Math.max(0,Math.floor(o?s/o:i)),u=n.currentDevicePixelRatio,c=a.devicePixelRatio||n.platform.getDevicePixelRatio();if((n.width!==s||n.height!==l||u!==c)&&(r.width=n.width=s,r.height=n.height=l,r.style&&(r.style.width=s+"px",r.style.height=l+"px"),pe.dom.retinaScale(n,c),!t)){var h={width:s,height:l};kn.notify(n,"resize",[h]),a.onResize&&a.onResize(n,h),n.stop(),n.update("resize")}}},{key:"ensureScalesHaveIDs",value:function(){var t=this.options,e=t.scales||{},i=t.scale;pe.each(e,(function(t,e){t.id=e})),i&&(i.id=i.id||"scale")}},{key:"buildOrUpdateScales",value:function(){var t=this,e=t.options.scales,i=t.scales||{},n=Object.keys(i).reduce((function(t,e){return t[e]=!1,t}),{}),a=[];e&&(a=a.concat(Object.keys(e).map((function(t){var i=e[t],n="r"===t.charAt(0).toLowerCase(),a="x"===t.charAt(0).toLowerCase();return{options:i,dposition:n?"chartArea":a?"bottom":"left",dtype:n?"radialLinear":a?"category":"linear"}})))),pe.each(a,(function(e){var a=e.options,r=a.id,o=wn(a.type,e.dtype);void 0!==a.position&&On(a.position,a.axis||r[0])===On(e.dposition)||(a.position=e.dposition),n[r]=!0;var s=null;if(r in i&&i[r].type===o)(s=i[r]).options=a,s.ctx=t.ctx,s.chart=t;else{var l=Mn.getScaleConstructor(o);if(!l)return;s=new l({id:r,type:o,options:a,ctx:t.ctx,chart:t}),i[s.id]=s}s.axis="chartArea"===s.options.position?"r":s.isHorizontal()?"x":"y",s._userMin=s.parse(s.options.min),s._userMax=s.parse(s.options.max),e.isDefault&&(t.scale=s)})),pe.each(n,(function(t,e){t||delete i[e]})),t.scales=i,Mn.addScalesToLayout(this)}},{key:"_updateMetasetIndex",value:function(t,e){var i=this._metasets,n=t.index;n!==e&&(i[n]=i[e],i[e]=t,t.index=e)}},{key:"_updateMetasets",value:function(){var t=this._metasets,e=this.data.datasets.length,i=t.length;if(i>e){for(var n=e;n<i;++n)this._destroyDatasetMeta(n);t.splice(e,i-e)}this._sortedMetasets=t.slice(0).sort(An("order","index"))}},{key:"buildOrUpdateControllers",value:function(){var t,e,i=this,n=[],a=i.data.datasets;for(t=0,e=a.length;t<e;t++){var r=a[t],o=i.getDatasetMeta(t),s=r.type||i.config.type;if(o.type&&o.type!==s&&(i._destroyDatasetMeta(t),o=i.getDatasetMeta(t)),o.type=s,o.order=r.order||0,i._updateMetasetIndex(o,t),o.label=""+r.label,o.visible=i.isDatasetVisible(t),o.controller)o.controller.updateIndex(t),o.controller.linkScales();else{var l=xi[o.type];if(void 0===l)throw new Error('"'+o.type+'" is not a chart type.');o.controller=new l(i,t),n.push(o.controller)}}return i._updateMetasets(),n}},{key:"_resetElements",value:function(){var t=this;pe.each(t.data.datasets,(function(e,i){t.getDatasetMeta(i).controller.reset()}),t)}},{key:"reset",value:function(){this._resetElements(),kn.notify(this,"reset")}},{key:"update",value:function(t){var e,i,n=this;if(n._updating=!0,function(t){var e=t.options;pe.each(t.scales,(function(e){Vi.removeBox(t,e)}));var i=Sn(t.config,e);e=Pn(Ot,Ot[t.config.type],e),t.options=t.config.options=e,t.options.scales=i,t._animationsDisabled=!e.animation,t.ensureScalesHaveIDs(),t.buildOrUpdateScales()}(n),kn.invalidate(n),!1!==kn.notify(n,"beforeUpdate")){var a=n.buildOrUpdateControllers();for(e=0,i=n.data.datasets.length;e<i;e++)n.getDatasetMeta(e).controller.buildOrUpdateElements();n._updateLayout(),n.options.animation&&pe.each(a,(function(t){t.reset()})),n._updateDatasets(t),kn.notify(n,"afterUpdate"),n._layers.sort(An("z","_idx")),n._lastEvent&&n._eventHandler(n._lastEvent,!0),n.render(),n._updating=!1}}},{key:"_updateLayout",value:function(){var t=this;!1!==kn.notify(t,"beforeLayout")&&(Vi.update(t,t.width,t.height),t._layers=[],pe.each(t.boxes,(function(e){var i;e.configure&&e.configure(),(i=t._layers).push.apply(i,f(e._layers()))}),t),t._layers.forEach((function(t,e){t._idx=e})),kn.notify(t,"afterLayout"))}},{key:"_updateDatasets",value:function(t){var e="function"==typeof t;if(!1!==kn.notify(this,"beforeDatasetsUpdate")){for(var i=0,n=this.data.datasets.length;i<n;++i)this._updateDataset(i,e?t({datasetIndex:i}):t);kn.notify(this,"afterDatasetsUpdate")}}},{key:"_updateDataset",value:function(t,e){var i=this.getDatasetMeta(t),n={meta:i,index:t,mode:e};!1!==kn.notify(this,"beforeDatasetUpdate",[n])&&(i.controller._update(e),kn.notify(this,"afterDatasetUpdate",[n]))}},{key:"render",value:function(){var t=this,e=t.options.animation;if(!1!==kn.notify(t,"beforeRender")){ge.has(t)?ge.running(t)||ge.start(t):(t.draw(),kn.notify(t,"afterRender"),pe.callback(e&&e.onComplete,[],t))}}},{key:"draw",value:function(){var t,e=this;if(e.clear(),!(e.width<=0||e.height<=0)&&!1!==kn.notify(e,"beforeDraw")){var i=e._layers;for(t=0;t<i.length&&i[t].z<=0;++t)i[t].draw(e.chartArea);for(e._drawDatasets();t<i.length;++t)i[t].draw(e.chartArea);kn.notify(e,"afterDraw")}}},{key:"_getSortedDatasetMetas",value:function(t){var e,i,n=this._sortedMetasets,a=[];for(e=0,i=n.length;e<i;++e){var r=n[e];t&&!r.visible||a.push(r)}return a}},{key:"getSortedVisibleDatasetMetas",value:function(){return this._getSortedDatasetMetas(!0)}},{key:"_drawDatasets",value:function(){if(!1!==kn.notify(this,"beforeDatasetsDraw")){for(var t=this.getSortedVisibleDatasetMetas(),e=t.length-1;e>=0;--e)this._drawDataset(t[e]);kn.notify(this,"afterDatasetsDraw")}}},{key:"_drawDataset",value:function(t){var e=this,i=e.ctx,n=t._clip,a=e.chartArea,r={meta:t,index:t.index};!1!==kn.notify(e,"beforeDatasetDraw",[r])&&(pe.canvas.clipArea(i,{left:!1===n.left?0:a.left-n.left,right:!1===n.right?e.width:a.right+n.right,top:!1===n.top?0:a.top-n.top,bottom:!1===n.bottom?e.height:a.bottom+n.bottom}),t.controller.draw(),pe.canvas.unclipArea(i),kn.notify(e,"afterDatasetDraw",[r]))}},{key:"getElementAtEvent",value:function(t){return Ai.modes.nearest(this,t,{intersect:!0})}},{key:"getElementsAtEvent",value:function(t){return Ai.modes.index(this,t,{intersect:!0})}},{key:"getElementsAtXAxis",value:function(t){return Ai.modes.index(this,t,{intersect:!1})}},{key:"getElementsAtEventForMode",value:function(t,e,i,n){var a=Ai.modes[e];return"function"==typeof a?a(this,t,i,n):[]}},{key:"getDatasetAtEvent",value:function(t){return Ai.modes.dataset(this,t,{intersect:!0})}},{key:"getDatasetMeta",value:function(t){var e=this.data.datasets[t],i=this._metasets,n=i.filter((function(t){return t._dataset===e})).pop();return n||(n=i[t]={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:e.order||0,index:t,_dataset:e,_parsed:[],_sorted:!1}),n}},{key:"getVisibleDatasetCount",value:function(){return this.getSortedVisibleDatasetMetas().length}},{key:"isDatasetVisible",value:function(t){var e=this.getDatasetMeta(t);return"boolean"==typeof e.hidden?!e.hidden:!this.data.datasets[t].hidden}},{key:"setDatasetVisibility",value:function(t,e){this.getDatasetMeta(t).hidden=!e}},{key:"toggleDataVisibility",value:function(t){this._hiddenIndices[t]=!this._hiddenIndices[t]}},{key:"getDataVisibility",value:function(t){return!this._hiddenIndices[t]}},{key:"_updateDatasetVisibility",value:function(t,e){var i=e?"show":"hide",n=this.getDatasetMeta(t),a=n.controller._resolveAnimations(void 0,i);this.setDatasetVisibility(t,e),a.update(n,{visible:e}),this.update((function(e){return e.datasetIndex===t?i:void 0}))}},{key:"hide",value:function(t){this._updateDatasetVisibility(t,!1)}},{key:"show",value:function(t){this._updateDatasetVisibility(t,!0)}},{key:"_destroyDatasetMeta",value:function(t){var e=this._metasets&&this._metasets[t];e&&(e.controller._destroy(),delete this._metasets[t])}},{key:"destroy",value:function(){var e,i,n=this,a=n.canvas;for(n.stop(),ge.remove(n),e=0,i=n.data.datasets.length;e<i;++e)n._destroyDatasetMeta(e);a&&(n.unbindEvents(),pe.canvas.clear(n),n.platform.releaseContext(n.ctx),n.canvas=null,n.ctx=null),kn.notify(n,"destroy"),delete t.instances[n.id]}},{key:"toBase64Image",value:function(){var t;return(t=this.canvas).toDataURL.apply(t,arguments)}},{key:"bindEvents",value:function(){var t=this,e=t._listeners,i=function(e){t._eventHandler(e)};pe.each(t.options.events,(function(n){t.platform.addEventListener(t,n,i),e[n]=i})),t.options.responsive&&(i=function(e,i){t.canvas&&t.resize(!1,e,i)},t.platform.addEventListener(t,"resize",i),e.resize=i)}},{key:"unbindEvents",value:function(){var t=this,e=t._listeners;e&&(delete t._listeners,pe.each(e,(function(e,i){t.platform.removeEventListener(t,i,e)})))}},{key:"updateHoverStyle",value:function(t,e,i){var n,a,r,o=i?"set":"remove";for("dataset"===e&&this.getDatasetMeta(t[0].datasetIndex).controller["_"+o+"DatasetHoverStyle"](),a=0,r=t.length;a<r;++a)(n=t[a])&&this.getDatasetMeta(n.datasetIndex).controller[o+"HoverStyle"](n.element,n.datasetIndex,n.index)}},{key:"_updateHoverStyles",value:function(){var t=this,e=(t.options||{}).hover;t.lastActive.length&&t.updateHoverStyle(t.lastActive,e.mode,!1),t.active.length&&e.mode&&t.updateHoverStyle(t.active,e.mode,!0)}},{key:"_eventHandler",value:function(t,e){var i=this;if(!1!==kn.notify(i,"beforeEvent",[t,e]))return i._handleEvent(t,e),kn.notify(i,"afterEvent",[t,e]),i.render(),i}},{key:"_handleEvent",value:function(t,e){var i,n=this,a=n.options,r=a.hover,o=e;return"mouseout"===t.type?(n.active=[],n._lastEvent=null):(n.active=n.getElementsAtEventForMode(t,r.mode,r,o),n._lastEvent="click"===t.type?n._lastEvent:t),pe.callback(a.onHover||a.hover.onHover,[t.native,n.active],n),"mouseup"!==t.type&&"click"!==t.type||a.onClick&&pe.canvas._isPointInArea(t,n.chartArea)&&a.onClick.call(n,t.native,n.active),((i=!pe._elementsEqual(n.active,n.lastActive))||e)&&n._updateHoverStyles(),n.lastActive=n.active,i}}]),t}();function In(){throw new Error("This method is not implemented: either no adapter can be found or an incomplete integration was provided.")}a(En,"version","3.0.0-alpha"),a(En,"instances",{});var Ln=function(){function t(i){e(this,t),this.options=i||{}}return n(t,[{key:"formats",value:function(){return In()}},{key:"parse",value:function(t,e){return In()}},{key:"format",value:function(t,e){return In()}},{key:"add",value:function(t,e,i){return In()}},{key:"diff",value:function(t,e,i){return In()}},{key:"startOf",value:function(t,e,i){return In()}},{key:"endOf",value:function(t,e){return In()}}]),t}();Ln.override=function(t){r(Ln.prototype,t)};var Rn={_date:Ln},zn={formatters:{values:function(t){return m(t)?t:""+t},numeric:function(t,e,i){if(0===t)return"0";var n=i.length>3?i[2].value-i[1].value:i[1].value-i[0].value;Math.abs(n)>1&&t!==Math.floor(t)&&(n=t-Math.floor(t));var a=Z(Math.abs(n)),r=Math.max(Math.abs(i[0].value),Math.abs(i[i.length-1].value)),o=Math.min(Math.abs(i[0].value),Math.abs(i[i.length-1].value)),s=this.chart.options.locale;if(r<1e-4||o>1e7){var l=Z(Math.abs(t)),u=Math.floor(l)-Math.floor(a);return u=Math.max(Math.min(u,20),0),t.toExponential(u)}var c=-1*Math.floor(a);return c=Math.max(Math.min(c,20),0),new Intl.NumberFormat(s,{minimumFractionDigits:c,maximumFractionDigits:c}).format(t)}}};function Bn(t,e){for(var i=[],n=t.length/e,a=t.length,r=0;r<a;r+=n)i.push(t[Math.floor(r)]);return i}function Vn(t,e,i){var n,a=t.ticks.length,r=Math.min(e,a-1),o=t._startPixel,s=t._endPixel,l=t.getPixelForTick(r);if(!(i&&(n=1===a?Math.max(l-o,s-l):0===e?(t.getPixelForTick(1)-l)/2:(l-t.getPixelForTick(r-1))/2,(l+=r<e?n:-n)<o-1e-6||l>s+1e-6)))return l}function Wn(t){return t.drawTicks?t.tickMarkLength:0}function Nn(t){if(!t.display)return 0;var e=Ct(t),i=Tt(t.padding);return e.lineHeight+i.height}function Hn(t,e,i,n,a){var r,o,s,l=_(n,0),u=Math.min(_(a,t.length),t.length),c=0;for(i=Math.ceil(i),a&&(i=(r=a-n)/Math.floor(r/i)),s=l;s<0;)c++,s=Math.round(l+c*i);for(o=Math.max(l,0);o<u;o++)o===s&&(e.push(t[o]),c++,s=Math.round(l+c*i))}Ot.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,gridLines:{display:!0,color:"rgba(0,0,0,0.1)",lineWidth:1,drawBorder:!0,drawOnChartArea:!0,drawTicks:!0,tickMarkLength:10,offsetGridLines:!1,borderDash:[],borderDashOffset:0},scaleLabel:{display:!1,labelString:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,lineWidth:0,strokeStyle:"",padding:0,display:!0,autoSkip:!0,autoSkipPadding:0,labelOffset:0,callback:zn.formatters.values,minor:{},major:{}}});var jn=function(t){function i(t){var n;return e(this,i),(n=h(this,l(i).call(this))).id=t.id,n.type=t.type,n.options=t.options,n.ctx=t.ctx,n.chart=t.chart,n.top=void 0,n.bottom=void 0,n.left=void 0,n.right=void 0,n.width=void 0,n.height=void 0,n._margins={left:0,right:0,top:0,bottom:0},n.maxWidth=void 0,n.maxHeight=void 0,n.paddingTop=void 0,n.paddingBottom=void 0,n.paddingLeft=void 0,n.paddingRight=void 0,n.axis=void 0,n.labelRotation=void 0,n.min=void 0,n.max=void 0,n.ticks=[],n._gridLineItems=null,n._labelItems=null,n._labelSizes=null,n._length=0,n._longestTextCache={},n._startPixel=void 0,n._endPixel=void 0,n._reversePixels=!1,n._userMax=void 0,n._userMin=void 0,n._ticksLength=0,n._borderValue=0,n}return s(i,t),n(i,[{key:"parse",value:function(t,e){return t}},{key:"parseObject",value:function(t,e,i){return void 0!==t[e]?this.parse(t[e],i):null}},{key:"getUserBounds",value:function(){var t=this._userMin,e=this._userMax;return(y(t)||isNaN(t))&&(t=Number.POSITIVE_INFINITY),(y(e)||isNaN(e))&&(e=Number.NEGATIVE_INFINITY),{min:t,max:e,minDefined:x(t),maxDefined:x(e)}}},{key:"getMinMax",value:function(t){var e,i=this.getUserBounds(),n=i.min,a=i.max,r=i.minDefined,o=i.maxDefined;if(r&&o)return{min:n,max:a};for(var s=this.getMatchingVisibleMetas(),l=0,u=s.length;l<u;++l)e=s[l].controller.getMinMax(this,t),r||(n=Math.min(n,e.min)),o||(a=Math.max(a,e.max));return{min:n,max:a}}},{key:"invalidateCaches",value:function(){}},{key:"getPadding",value:function(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}},{key:"getTicks",value:function(){return this.ticks}},{key:"getLabels",value:function(){var t=this.chart.data;return this.options.labels||(this.isHorizontal()?t.xLabels:t.yLabels)||t.labels||[]}},{key:"beforeUpdate",value:function(){k(this.options.beforeUpdate,[this])}},{key:"update",value:function(t,e,i){var n=this,a=n.options.ticks,o=a.sampleSize;n.beforeUpdate(),n.maxWidth=t,n.maxHeight=e,n._margins=r({left:0,right:0,top:0,bottom:0},i),n.ticks=null,n._labelSizes=null,n._gridLineItems=null,n._labelItems=null,n.beforeSetDimensions(),n.setDimensions(),n.afterSetDimensions(),n.beforeDataLimits(),n.determineDataLimits(),n.afterDataLimits(),n.beforeBuildTicks(),n.ticks=n.buildTicks()||[],n.afterBuildTicks();var s=o<n.ticks.length;n._convertTicksToLabels(s?Bn(n.ticks,o):n.ticks),n.configure(),n.beforeCalculateLabelRotation(),n.calculateLabelRotation(),n.afterCalculateLabelRotation(),n.beforeFit(),n.fit(),n.afterFit(),n.ticks=a.display&&(a.autoSkip||"auto"===a.source)?n._autoSkip(n.ticks):n.ticks,s&&n._convertTicksToLabels(n.ticks),n.afterUpdate()}},{key:"configure",value:function(){var t,e,i=this,n=i.options.reverse;i.isHorizontal()?(t=i.left,e=i.right):(t=i.top,e=i.bottom,n=!n),i._startPixel=t,i._endPixel=e,i._reversePixels=n,i._length=e-t}},{key:"afterUpdate",value:function(){k(this.options.afterUpdate,[this])}},{key:"beforeSetDimensions",value:function(){k(this.options.beforeSetDimensions,[this])}},{key:"setDimensions",value:function(){var t=this;t.isHorizontal()?(t.width=t.maxWidth,t.left=0,t.right=t.width):(t.height=t.maxHeight,t.top=0,t.bottom=t.height),t.paddingLeft=0,t.paddingTop=0,t.paddingRight=0,t.paddingBottom=0}},{key:"afterSetDimensions",value:function(){k(this.options.afterSetDimensions,[this])}},{key:"beforeDataLimits",value:function(){k(this.options.beforeDataLimits,[this])}},{key:"determineDataLimits",value:function(){}},{key:"afterDataLimits",value:function(){k(this.options.afterDataLimits,[this])}},{key:"beforeBuildTicks",value:function(){k(this.options.beforeBuildTicks,[this])}},{key:"buildTicks",value:function(){return[]}},{key:"afterBuildTicks",value:function(){k(this.options.afterBuildTicks,[this])}},{key:"beforeTickToLabelConversion",value:function(){k(this.options.beforeTickToLabelConversion,[this])}},{key:"generateTickLabels",value:function(t){var e,i,n,a=this.options.ticks;for(e=0,i=t.length;e<i;e++)(n=t[e]).label=k(a.callback,[n.value,e,t],this)}},{key:"afterTickToLabelConversion",value:function(){k(this.options.afterTickToLabelConversion,[this])}},{key:"beforeCalculateLabelRotation",value:function(){k(this.options.beforeCalculateLabelRotation,[this])}},{key:"calculateLabelRotation",value:function(){var t,e,i,n=this,a=n.options,r=a.ticks,o=n.ticks.length,s=r.minRotation||0,l=r.maxRotation,u=s;if(!n._isVisible()||!r.display||s>=l||o<=1||!n.isHorizontal())n.labelRotation=s;else{var c=n._getLabelSizes(),h=c.widest.width,d=c.highest.height-c.highest.offset,f=Math.min(n.maxWidth,n.chart.width-h);h+6>(t=a.offset?n.maxWidth/o:f/(o-1))&&(t=f/(o-(a.offset?.5:1)),e=n.maxHeight-Wn(a.gridLines)-r.padding-Nn(a.scaleLabel),i=Math.sqrt(h*h+d*d),u=at(Math.min(Math.asin(Math.min((c.highest.height+6)/t,1)),Math.asin(Math.min(e/i,1))-Math.asin(d/i))),u=Math.max(s,Math.min(l,u))),n.labelRotation=u}}},{key:"afterCalculateLabelRotation",value:function(){k(this.options.afterCalculateLabelRotation,[this])}},{key:"beforeFit",value:function(){k(this.options.beforeFit,[this])}},{key:"fit",value:function(){var t=this,e={width:0,height:0},i=t.chart,n=t.options,a=n.ticks,r=n.scaleLabel,o=n.gridLines,s=t._isVisible(),l="top"!==n.position&&"x"===t.axis,u=t.isHorizontal();if(u?e.width=t.maxWidth:s&&(e.width=Wn(o)+Nn(r)),u?s&&(e.height=Wn(o)+Nn(r)):e.height=t.maxHeight,a.display&&s){var c=t._getLabelSizes(),h=c.first,d=c.last,f=c.widest,v=c.highest,p=.8*v.offset,g=a.padding;if(u){var y=0!==t.labelRotation,m=nt(t.labelRotation),b=Math.cos(m),x=Math.sin(m),_=x*f.width+b*(v.height-(y?v.offset:0))+(y?0:p);e.height=Math.min(t.maxHeight,e.height+_+g);var k,M,w=t.getPixelForTick(0)-t.left,S=t.right-t.getPixelForTick(t.ticks.length-1);y?(k=l?b*h.width+x*h.offset:x*(h.height-h.offset),M=l?x*(d.height-d.offset):b*d.width+x*d.offset):(k=h.width/2,M=d.width/2),t.paddingLeft=Math.max((k-w)*t.width/(t.width-w),0)+3,t.paddingRight=Math.max((M-S)*t.width/(t.width-S),0)+3}else{var P=a.mirror?0:f.width+g+p;e.width=Math.min(t.maxWidth,e.width+P),t.paddingTop=h.height/2,t.paddingBottom=d.height/2}}t._handleMargins(),u?(t.width=t._length=i.width-t._margins.left-t._margins.right,t.height=e.height):(t.width=e.width,t.height=t._length=i.height-t._margins.top-t._margins.bottom)}},{key:"_handleMargins",value:function(){var t=this;t._margins&&(t._margins.left=Math.max(t.paddingLeft,t._margins.left),t._margins.top=Math.max(t.paddingTop,t._margins.top),t._margins.right=Math.max(t.paddingRight,t._margins.right),t._margins.bottom=Math.max(t.paddingBottom,t._margins.bottom))}},{key:"afterFit",value:function(){k(this.options.afterFit,[this])}},{key:"isHorizontal",value:function(){var t=this.options,e=t.axis,i=t.position;return"top"===i||"bottom"===i||"x"===e}},{key:"isFullWidth",value:function(){return this.options.fullWidth}},{key:"_convertTicksToLabels",value:function(t){this.beforeTickToLabelConversion(),this.generateTickLabels(t),this.afterTickToLabelConversion()}},{key:"_getLabelSizes",value:function(){var t=this._labelSizes;return t||(this._labelSizes=t=this._computeLabelSizes()),t}},{key:"_computeLabelSizes",value:function(){var t=this,e=t.ctx,i=t._longestTextCache,n=t.options.ticks.sampleSize,a=[],r=[],o=[],s=t.ticks;n<s.length&&(s=Bn(s,n));var l,u,c,h,d,f,v,p,g,b,x,_=s.length;for(l=0;l<_;++l){if(h=s[l].label,d=t._resolveTickFontOptions(l),e.font=f=d.string,v=i[f]=i[f]||{data:{},gc:[]},p=d.lineHeight,g=b=0,y(h)||m(h)){if(m(h))for(u=0,c=h.length;u<c;++u)y(x=h[u])||m(x)||(g=z(e,v.data,v.gc,g,x),b+=p)}else g=z(e,v.data,v.gc,g,h),b=p;a.push(g),r.push(b),o.push(p/2)}!function(t,e){M(t,(function(t){var i,n=t.gc,a=n.length/2;if(a>e){for(i=0;i<a;++i)delete t.data[n[i]];n.splice(0,a)}}))}(i,_);var k=a.indexOf(Math.max.apply(null,a)),w=r.indexOf(Math.max.apply(null,r));function S(t){return{width:a[t]||0,height:r[t]||0,offset:o[t]||0}}return{first:S(0),last:S(_-1),widest:S(k),highest:S(w)}}},{key:"getLabelForValue",value:function(t){return t}},{key:"getPixelForValue",value:function(t){return NaN}},{key:"getValueForPixel",value:function(t){}},{key:"getPixelForTick",value:function(t){var e=this.options.offset,i=this.ticks.length,n=1/Math.max(i-(e?0:1),1);return t<0||t>i-1?null:this.getPixelForDecimal(t*n+(e?n/2:0))}},{key:"getPixelForDecimal",value:function(t){return this._reversePixels&&(t=1-t),this._startPixel+t*this._length}},{key:"getDecimalForPixel",value:function(t){var e=(t-this._startPixel)/this._length;return this._reversePixels?1-e:e}},{key:"getBasePixel",value:function(){return this.getPixelForValue(this.getBaseValue())}},{key:"getBaseValue",value:function(){var t=this.min,e=this.max;return t<0&&e<0?e:t>0&&e>0?t:0}},{key:"_autoSkip",value:function(t){var e=this.options.ticks,i=this._length,n=e.maxTicksLimit||i/this._tickSize(),a=e.major.enabled?function(t){var e,i,n=[];for(e=0,i=t.length;e<i;e++)t[e].major&&n.push(e);return n}(t):[],r=a.length,o=a[0],s=a[r-1],l=[];if(r>n)return function(t,e,i,n){var a,r=0,o=i[0];for(n=Math.ceil(n),a=0;a<t.length;a++)a===o&&(e.push(t[a]),o=i[++r*n])}(t,l,a,r/n),l;var u=function(t,e,i,n){var a=function(t){var e,i,n=t.length;if(n<2)return!1;for(i=t[0],e=1;e<n;++e)if(t[e]-t[e-1]!==i)return!1;return i}(t),r=e.length/n;if(!a)return Math.max(r,1);for(var o=K(a),s=0,l=o.length-1;s<l;s++){var u=o[s];if(u>r)return u}return Math.max(r,1)}(a,t,0,n);if(r>0){var c,h,d=r>1?Math.round((s-o)/(r-1)):null;for(Hn(t,l,u,y(d)?0:o-d,o),c=0,h=r-1;c<h;c++)Hn(t,l,u,a[c],a[c+1]);return Hn(t,l,u,s,y(d)?t.length:s+d),l}return Hn(t,l,u),l}},{key:"_tickSize",value:function(){var t=this.options.ticks,e=nt(this.labelRotation),i=Math.abs(Math.cos(e)),n=Math.abs(Math.sin(e)),a=this._getLabelSizes(),r=t.autoSkipPadding||0,o=a?a.widest.width+r:0,s=a?a.highest.height+r:0;return this.isHorizontal()?s*i>o*n?o/i:s/n:s*n<o*i?s/i:o/n}},{key:"_isVisible",value:function(){var t=this.options.display;return"auto"!==t?!!t:this.getMatchingVisibleMetas().length>0}},{key:"_computeGridLineItems",value:function(t){var e,i,n,a,r,o,s,l,u,c,h,d,f=this,v=f.axis,p=f.chart,g=f.options,y=g.gridLines,m=g.position,x=y.offsetGridLines,_=f.isHorizontal(),k=f.ticks,M=k.length+(x?1:0),w=Wn(y),S=[],P={scale:f,tick:k[0]},D=y.drawBorder?Ft([y.borderWidth,y.lineWidth,0],P,0):0,O=D/2,A=function(t){return V(p,t,D)};if("top"===m)e=A(f.bottom),o=f.bottom-w,l=e-O,c=A(t.top)+O,d=t.bottom;else if("bottom"===m)e=A(f.top),c=t.top,d=A(t.bottom)-O,o=e+O,l=f.top+w;else if("left"===m)e=A(f.right),r=f.right-w,s=e-O,u=A(t.left)+O,h=t.right;else if("right"===m)e=A(f.left),u=t.left,h=A(t.right)-O,r=e+O,s=f.left+w;else if("x"===v){if("center"===m)e=A((t.top+t.bottom)/2);else if(b(m)){var T=Object.keys(m)[0],C=m[T];e=A(f.chart.scales[T].getPixelForValue(C))}c=t.top,d=t.bottom,l=(o=e+O)+w}else if("y"===v){if("center"===m)e=A((t.left+t.right)/2);else if(b(m)){var F=Object.keys(m)[0],E=m[F];e=A(f.chart.scales[F].getPixelForValue(E))}s=(r=e-O)-w,u=t.left,h=t.right}for(i=0;i<M;++i){P={scale:f,tick:k[i]||{}};var I=Ft([y.lineWidth],P,i),L=Ft([y.color],P,i),R=y.borderDash||[],z=Ft([y.borderDashOffset],P,i);void 0!==(n=Vn(f,i,x))&&(a=V(p,n,I),_?r=s=u=h=a:o=l=c=d=a,S.push({tx1:r,ty1:o,tx2:s,ty2:l,x1:u,y1:c,x2:h,y2:d,width:I,color:L,borderDash:R,borderDashOffset:z}))}return f._ticksLength=M,f._borderValue=e,S}},{key:"_computeLabelItems",value:function(t){var e,i,n,a,r,o,s,l,u,c,h,d=this,f=d.axis,v=d.options,p=v.position,g=v.ticks,y=g.mirror,x=d.isHorizontal(),_=d.ticks,k=g.padding,M=Wn(v.gridLines),w=-nt(d.labelRotation),S=[];if("top"===p)r=d.bottom-M-k,o=w?"left":"center";else if("bottom"===p)r=d.top+M+k,o=w?"right":"center";else if("left"===p)a=d.right-(y?0:M)-k,o=y?"left":"right";else if("right"===p)a=d.left+(y?0:M)+k,o=y?"right":"left";else if("x"===f){if("center"===p)r=(t.top+t.bottom)/2+M+k;else if(b(p)){var P=Object.keys(p)[0],D=p[P];r=d.chart.scales[P].getPixelForValue(D)+M+k}o=w?"right":"center"}else if("y"===f){if("center"===p)a=(t.left+t.right)/2-M-k;else if(b(p)){var O=Object.keys(p)[0],A=p[O];a=d.chart.scales[O].getPixelForValue(A)}o="right"}for(e=0,i=_.length;e<i;++e)n=_[e].label,s=d.getPixelForTick(e)+g.labelOffset,u=(l=d._resolveTickFontOptions(e)).lineHeight,c=m(n)?n.length:1,x?(a=s,"top"===p?(h=(Math.sin(w)*(c/2)+.5)*u,h-=(0===w?c-.5:Math.cos(w)*(c/2))*u):(h=Math.sin(w)*(c/2)*u,h+=(0===w?.5:Math.cos(w)*(c/2))*u)):(r=s,h=(1-c)*u/2),S.push({x:a,y:r,rotation:w,label:n,font:l,textOffset:h,textAlign:o});return S}},{key:"drawGrid",value:function(t){var e,i,n=this,a=n.options.gridLines,r=n.ctx,o=n.chart,s={scale:n,tick:n.ticks[0]},l=a.drawBorder?Ft([a.borderWidth,a.lineWidth,0],s,0):0,u=n._gridLineItems||(n._gridLineItems=n._computeGridLineItems(t));if(a.display)for(e=0,i=u.length;e<i;++e){var c=u[e],h=c.width,d=c.color;h&&d&&(r.save(),r.lineWidth=h,r.strokeStyle=d,r.setLineDash&&(r.setLineDash(c.borderDash),r.lineDashOffset=c.borderDashOffset),r.beginPath(),a.drawTicks&&(r.moveTo(c.tx1,c.ty1),r.lineTo(c.tx2,c.ty2)),a.drawOnChartArea&&(r.moveTo(c.x1,c.y1),r.lineTo(c.x2,c.y2)),r.stroke(),r.restore())}if(l){var f=l;s={scale:n,tick:n.ticks[n._ticksLength-1]};var v,p,g,y,m=Ft([a.lineWidth,1],s,n._ticksLength-1),b=n._borderValue;n.isHorizontal()?(v=V(o,n.left,f)-f/2,p=V(o,n.right,m)+m/2,g=y=b):(g=V(o,n.top,f)-f/2,y=V(o,n.bottom,m)+m/2,v=p=b),r.lineWidth=l,r.strokeStyle=Ft([a.borderColor,a.color],s,0),r.beginPath(),r.moveTo(v,g),r.lineTo(p,y),r.stroke()}}},{key:"drawLabels",value:function(t){var e=this;if(e.options.ticks.display){var i,n,a,r,o=e.ctx,s=e._labelItems||(e._labelItems=e._computeLabelItems(t));for(i=0,a=s.length;i<a;++i){var l=s[i],u=l.font,c=u.lineWidth>0&&""!==u.strokeStyle;o.save(),o.translate(l.x,l.y),o.rotate(l.rotation),o.font=u.string,o.fillStyle=u.color,o.textBaseline="middle",o.textAlign=l.textAlign,c&&(o.strokeStyle=u.strokeStyle,o.lineWidth=u.lineWidth);var h=l.label,d=l.textOffset;if(m(h))for(n=0,r=h.length;n<r;++n)c&&o.strokeText(""+h[n],0,d),o.fillText(""+h[n],0,d),d+=u.lineHeight;else c&&o.strokeText(h,0,d),o.fillText(h,0,d);o.restore()}}}},{key:"drawTitle",value:function(t){var e=this,i=e.ctx,n=e.options,a=n.scaleLabel;if(a.display){var r,o,s,l=_(a.fontColor,Ot.fontColor),u=Ct(a),c=Tt(a.padding),h=u.lineHeight/2,d=a.align,f=n.position,v=e.options.reverse,p=0;if(e.isHorizontal()){switch(d){case"start":o=e.left+(v?e.width:0),r=v?"right":"left";break;case"end":o=e.left+(v?0:e.width),r=v?"left":"right";break;default:o=e.left+e.width/2,r="center"}s="top"===f?e.top+h+c.top:e.bottom-h-c.bottom}else{var g="left"===f;switch(o=g?e.left+h+c.top:e.right-h-c.top,d){case"start":s=e.top+(v?0:e.height),r=v===g?"right":"left";break;case"end":s=e.top+(v?e.height:0),r=v===g?"left":"right";break;default:s=e.top+e.height/2,r="center"}p=g?-.5*Math.PI:.5*Math.PI}i.save(),i.translate(o,s),i.rotate(p),i.textAlign=r,i.textBaseline="middle",i.fillStyle=l,i.font=u.string,i.fillText(a.labelString,0,0),i.restore()}}},{key:"draw",value:function(t){this._isVisible()&&(this.drawGrid(t),this.drawTitle(),this.drawLabels(t))}},{key:"_layers",value:function(){var t=this,e=t.options,i=e.ticks&&e.ticks.z||0,n=e.gridLines&&e.gridLines.z||0;return t._isVisible()&&i!==n&&t.draw===t._draw?[{z:n,draw:function(e){t.drawGrid(e),t.drawTitle()}},{z:i,draw:function(e){t.drawLabels(e)}}]:[{z:i,draw:function(e){t.draw(e)}}]}},{key:"getMatchingVisibleMetas",value:function(t){var e,i,n=this.chart.getSortedVisibleDatasetMetas(),a=this.axis+"AxisID",r=[];for(e=0,i=n.length;e<i;++e){var o=n[e];o[a]!==this.id||t&&o.type!==t||r.push(o)}return r}},{key:"_resolveTickFontOptions",value:function(t){var e=this.options.ticks,i={chart:this.chart,scale:this,tick:this.ticks[t],index:t};return r(Ct({fontFamily:Ft([e.fontFamily],i),fontSize:Ft([e.fontSize],i),fontStyle:Ft([e.fontStyle],i),lineHeight:Ft([e.lineHeight],i)}),{color:Ft([e.fontColor,Ot.fontColor],i),lineWidth:Ft([e.lineWidth],i),strokeStyle:Ft([e.strokeStyle],i)})}}]),i}(Fe);jn.prototype._draw=jn.prototype.draw;var Yn=function(t){function i(t){var n;return e(this,i),(n=h(this,l(i).call(this,t)))._numLabels=0,n._startValue=void 0,n._valueRange=0,n}return s(i,t),n(i,[{key:"parse",value:function(t,e){var i=this.getLabels();if(i[e]===t)return e;var n=i.indexOf(t),a=i.lastIndexOf(t);return-1===n||n!==a?e:n}},{key:"determineDataLimits",value:function(){var t=this,e=t.getLabels().length-1;t.min=Math.max(t._userMin||0,0),t.max=Math.min(t._userMax||e,e)}},{key:"buildTicks",value:function(){var t=this,e=t.min,i=t.max,n=t.options.offset,a=t.getLabels();return a=0===e&&i===a.length-1?a:a.slice(e,i+1),t._numLabels=a.length,t._valueRange=Math.max(a.length-(n?0:1),1),t._startValue=t.min-(n?.5:0),a.map((function(t){return{value:t}}))}},{key:"getLabelForValue",value:function(t){var e=this.getLabels();return t>=0&&t<e.length?e[t]:t}},{key:"configure",value:function(){d(l(i.prototype),"configure",this).call(this),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}},{key:"getPixelForValue",value:function(t){return"number"!=typeof t&&(t=this.parse(t)),this.getPixelForDecimal((t-this._startValue)/this._valueRange)}},{key:"getPixelForTick",value:function(t){var e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(t*this._numLabels/e.length+this.min)}},{key:"getValueForPixel",value:function(t){var e=Math.round(this._startValue+this.getDecimalForPixel(t)*this._valueRange);return Math.min(Math.max(e,0),this.ticks.length-1)}},{key:"getBasePixel",value:function(){return this.bottom}}]),i}(jn);function Un(t,e){var i=Math.floor(Z(t)),n=t/Math.pow(10,i);return(e?n<1.5?1:n<3?2:n<7?5:10:n<=1?1:n<=2?2:n<=5?5:10)*Math.pow(10,i)}a(Yn,"id","category"),a(Yn,"defaults",{});var Xn=function(t){function i(t){var n;return e(this,i),(n=h(this,l(i).call(this,t))).start=void 0,n.end=void 0,n._startValue=void 0,n._endValue=void 0,n._valueRange=0,n}return s(i,t),n(i,[{key:"parse",value:function(t,e){return y(t)?NaN:("number"==typeof t||t instanceof Number)&&!isFinite(+t)?NaN:+t}},{key:"handleTickRangeOptions",value:function(){var t=this,e=t.options;if(e.beginAtZero){var i=it(t.min),n=it(t.max);i<0&&n<0?t.max=0:i>0&&n>0&&(t.min=0)}var a=void 0!==e.min||void 0!==e.suggestedMin,r=void 0!==e.max||void 0!==e.suggestedMax;void 0!==e.min?t.min=e.min:void 0!==e.suggestedMin&&(null===t.min?t.min=e.suggestedMin:t.min=Math.min(t.min,e.suggestedMin)),void 0!==e.max?t.max=e.max:void 0!==e.suggestedMax&&(null===t.max?t.max=e.suggestedMax:t.max=Math.max(t.max,e.suggestedMax)),a!==r&&t.min>=t.max&&(a?t.max=t.min+1:t.min=t.max-1),t.min===t.max&&(t.max++,e.beginAtZero||t.min--)}},{key:"getTickLimit",value:function(){var t,e=this.options.ticks,i=e.maxTicksLimit,n=e.stepSize;return n?t=Math.ceil(this.max/n)-Math.floor(this.min/n)+1:(t=this.computeTickLimit(),i=i||11),i&&(t=Math.min(i,t)),t}},{key:"computeTickLimit",value:function(){return Number.POSITIVE_INFINITY}},{key:"handleDirectionalChanges",value:function(t){return t}},{key:"buildTicks",value:function(){var t=this,e=t.options,i=e.ticks,n=t.getTickLimit(),a=function(t,e){var i,n,a,r,o=[],s=t.stepSize,l=t.min,u=t.max,c=t.precision,h=s||1,d=t.maxTicks-1,f=e.min,v=e.max,p=Un((v-f)/d/h)*h;if(p<1e-14&&y(l)&&y(u))return[{value:f},{value:v}];(r=Math.ceil(v/p)-Math.floor(f/p))>d&&(p=Un(r*p/d/h)*h),s||y(c)?i=Math.pow(10,rt(p)):(i=Math.pow(10,c),p=Math.ceil(p*i)/i),n=Math.floor(f/p)*p,a=Math.ceil(v/p)*p,!s||y(l)||y(u)||tt((u-l)/s,p/1e3)&&(n=l,a=u),r=J(r=(a-n)/p,Math.round(r),p/1e3)?Math.round(r):Math.ceil(r),n=Math.round(n*i)/i,a=Math.round(a*i)/i,o.push({value:y(l)?n:l});for(var g=1;g<r;++g)o.push({value:Math.round((n+g*p)*i)/i});return o.push({value:y(u)?a:u}),o}({maxTicks:n=Math.max(2,n),min:e.min,max:e.max,precision:i.precision,stepSize:_(i.fixedStepSize,i.stepSize)},t);return et(a=t.handleDirectionalChanges(a),t,"value"),e.reverse?(a.reverse(),t.start=t.max,t.end=t.min):(t.start=t.min,t.end=t.max),a}},{key:"configure",value:function(){var t=this,e=t.ticks,n=t.min,a=t.max;if(d(l(i.prototype),"configure",this).call(this),t.options.offset&&e.length){var r=(a-n)/Math.max(e.length-1,1)/2;n-=r,a+=r}t._startValue=n,t._endValue=a,t._valueRange=a-n}},{key:"getLabelForValue",value:function(t){return new Intl.NumberFormat(this.options.locale).format(t)}}]),i}(jn),qn={ticks:{callback:zn.formatters.numeric}},$n=function(t){function i(){return e(this,i),h(this,l(i).apply(this,arguments))}return s(i,t),n(i,[{key:"determineDataLimits",value:function(){var t=this,e=t.options,i=t.getMinMax(!0),n=i.min,a=i.max;t.min=x(n)?n:_(e.suggestedMin,0),t.max=x(a)?a:_(e.suggestedMax,1),e.stacked&&n>0&&(t.min=0),t.handleTickRangeOptions()}},{key:"computeTickLimit",value:function(){if(this.isHorizontal())return Math.ceil(this.width/40);var t=Ct(this.options.ticks);return Math.ceil(this.height/t.lineHeight)}},{key:"handleDirectionalChanges",value:function(t){return this.isHorizontal()?t:t.reverse()}},{key:"getPixelForValue",value:function(t){return this.getPixelForDecimal((t-this._startValue)/this._valueRange)}},{key:"getValueForPixel",value:function(t){return this._startValue+this.getDecimalForPixel(t)*this._valueRange}},{key:"getPixelForTick",value:function(t){var e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}}]),i}(Xn);function Gn(t){return 1===t/Math.pow(10,Math.floor(Z(t)))}function Kn(t,e){return x(t)?t:e}a($n,"id","linear"),a($n,"defaults",qn);var Zn={ticks:{callback:zn.formatters.numeric,major:{enabled:!0}}},Qn=function(t){function i(t){var n;return e(this,i),(n=h(this,l(i).call(this,t))).start=void 0,n.end=void 0,n._startValue=void 0,n._valueRange=0,n}return s(i,t),n(i,[{key:"parse",value:function(t,e){var i=Xn.prototype.parse.apply(this,[t,e]);if(0!==i)return x(i)&&i>0?i:NaN}},{key:"determineDataLimits",value:function(){var t=this.getMinMax(!0),e=t.min,i=t.max;this.min=x(e)?Math.max(0,e):null,this.max=x(i)?Math.max(0,i):null,this.handleTickRangeOptions()}},{key:"handleTickRangeOptions",value:function(){var t=this.min,e=this.max;t===e&&(t<=0?(t=1,e=10):(t=Math.pow(10,Math.floor(Z(t))-1),e=Math.pow(10,Math.floor(Z(e))+1))),t<=0&&(t=Math.pow(10,Math.floor(Z(e))-1)),e<=0&&(e=Math.pow(10,Math.floor(Z(t))+1)),this.min=t,this.max=e}},{key:"buildTicks",value:function(){var t=this,e=t.options,i=function(t,e){var i=Math.floor(Z(e.max)),n=Math.ceil(e.max/Math.pow(10,i)),a=[],r=Kn(t.min,Math.pow(10,Math.floor(Z(e.min)))),o=Math.floor(Z(r)),s=Math.floor(r/Math.pow(10,o)),l=o<0?Math.pow(10,Math.abs(o)):1;do{a.push({value:r,major:Gn(r)}),10===++s&&(s=1,l=++o>=0?1:l),r=Math.round(s*Math.pow(10,o)*l)/l}while(o<i||o===i&&s<n);var u=Kn(t.max,r);return a.push({value:u,major:Gn(r)}),a}({min:t._userMin,max:t._userMax},t),n=!t.isHorizontal();return et(i,t,"value"),e.reverse?(n=!n,t.start=t.max,t.end=t.min):(t.start=t.min,t.end=t.max),n&&i.reverse(),i}},{key:"getLabelForValue",value:function(t){return void 0===t?"0":new Intl.NumberFormat(this.options.locale).format(t)}},{key:"getPixelForTick",value:function(t){var e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}},{key:"configure",value:function(){var t=this.min;d(l(i.prototype),"configure",this).call(this),this._startValue=Z(t),this._valueRange=Z(this.max)-Z(t)}},{key:"getPixelForValue",value:function(t){var e=this;return void 0!==t&&0!==t||(t=e.min),e.getPixelForDecimal(t===e.min?0:(Z(t)-e._startValue)/e._valueRange)}},{key:"getValueForPixel",value:function(t){var e=this.getDecimalForPixel(t);return Math.pow(10,this._startValue+e*this._valueRange)}}]),i}(jn);a(Qn,"id","logarithmic"),a(Qn,"defaults",Zn);var Jn=pe.valueOrDefault,ta=pe.valueAtIndexOrDefault,ea=pe.options.resolve,ia={display:!0,animate:!0,position:"chartArea",angleLines:{display:!0,color:"rgba(0,0,0,0.1)",lineWidth:1,borderDash:[],borderDashOffset:0},gridLines:{circular:!1},ticks:{showLabelBackdrop:!0,backdropColor:"rgba(255,255,255,0.75)",backdropPaddingY:2,backdropPaddingX:2,callback:zn.formatters.numeric},pointLabels:{display:!0,fontSize:10,callback:function(t){return t}}};function na(t){var e=t.ticks;return e.display&&t.display?Jn(e.fontSize,Ot.fontSize)+2*e.backdropPaddingY:0}function aa(t,e,i,n,a){return t===n||t===a?{start:e-i/2,end:e+i/2}:t<n||t>a?{start:e-i,end:e}:{start:e,end:e+i}}function ra(t){return 0===t||180===t?"center":t<180?"left":"right"}function oa(t,e,i,n){var a,r,o=i.y+n/2;if(pe.isArray(e))for(a=0,r=e.length;a<r;++a)t.fillText(e[a],i.x,o),o+=n;else t.fillText(e,i.x,o)}function sa(t,e,i){90===t||270===t?i.y-=e.h/2:(t>270||t<90)&&(i.y-=e.h)}function la(t){return Q(t)?t:0}var ua=function(t){function i(t){var n;return e(this,i),(n=h(this,l(i).call(this,t))).xCenter=void 0,n.yCenter=void 0,n.drawingArea=void 0,n.pointLabels=[],n}return s(i,t),n(i,[{key:"setDimensions",value:function(){var t=this;t.width=t.maxWidth,t.height=t.maxHeight,t.paddingTop=na(t.options)/2,t.xCenter=Math.floor(t.width/2),t.yCenter=Math.floor((t.height-t.paddingTop)/2),t.drawingArea=Math.min(t.height-t.paddingTop,t.width)/2}},{key:"determineDataLimits",value:function(){var t=this.getMinMax(!1),e=t.min,i=t.max;this.min=pe.isFinite(e)&&!isNaN(e)?e:0,this.max=pe.isFinite(i)&&!isNaN(i)?i:0,this.handleTickRangeOptions()}},{key:"computeTickLimit",value:function(){return Math.ceil(this.drawingArea/na(this.options))}},{key:"generateTickLabels",value:function(t){var e=this;Xn.prototype.generateTickLabels.call(e,t),e.pointLabels=e.chart.data.labels.map((function(t,i){var n=pe.callback(e.options.pointLabels.callback,[t,i],e);return n||0===n?n:""}))}},{key:"fit",value:function(){var t=this.options;t.display&&t.pointLabels.display?function(t){var e,i,n,a=pe.options._parseFont(t.options.pointLabels),r={l:0,r:t.width,t:0,b:t.height-t.paddingTop},o={};t.ctx.font=a.string,t._pointLabelSizes=[];var s,l,u,c=t.chart.data.labels.length;for(e=0;e<c;e++){n=t.getPointPosition(e,t.drawingArea+5),s=t.ctx,l=a.lineHeight,u=t.pointLabels[e],i=pe.isArray(u)?{w:B(s,s.font,u),h:u.length*l}:{w:s.measureText(u).width,h:l},t._pointLabelSizes[e]=i;var h=t.getIndexAngle(e),d=at(h),f=aa(d,n.x,i.w,0,180),v=aa(d,n.y,i.h,90,270);f.start<r.l&&(r.l=f.start,o.l=h),f.end>r.r&&(r.r=f.end,o.r=h),v.start<r.t&&(r.t=v.start,o.t=h),v.end>r.b&&(r.b=v.end,o.b=h)}t._setReductions(t.drawingArea,r,o)}(this):this.setCenterPoint(0,0,0,0)}},{key:"_setReductions",value:function(t,e,i){var n=this,a=e.l/Math.sin(i.l),r=Math.max(e.r-n.width,0)/Math.sin(i.r),o=-e.t/Math.cos(i.t),s=-Math.max(e.b-(n.height-n.paddingTop),0)/Math.cos(i.b);a=la(a),r=la(r),o=la(o),s=la(s),n.drawingArea=Math.min(Math.floor(t-(a+r)/2),Math.floor(t-(o+s)/2)),n.setCenterPoint(a,r,o,s)}},{key:"setCenterPoint",value:function(t,e,i,n){var a=this,r=a.width-e-a.drawingArea,o=t+a.drawingArea,s=i+a.drawingArea,l=a.height-a.paddingTop-n-a.drawingArea;a.xCenter=Math.floor((o+r)/2+a.left),a.yCenter=Math.floor((s+l)/2+a.top+a.paddingTop)}},{key:"getIndexAngle",value:function(t){var e=this.chart;return lt(t*(2*Math.PI/e.data.labels.length)+nt((e.options||{}).startAngle||0))}},{key:"getDistanceFromCenterForValue",value:function(t){var e=this;if(pe.isNullOrUndef(t))return NaN;var i=e.drawingArea/(e.max-e.min);return e.options.reverse?(e.max-t)*i:(t-e.min)*i}},{key:"getPointPosition",value:function(t,e){var i=this.getIndexAngle(t)-Math.PI/2;return{x:Math.cos(i)*e+this.xCenter,y:Math.sin(i)*e+this.yCenter,angle:i}}},{key:"getPointPositionForValue",value:function(t,e){return this.getPointPosition(t,this.getDistanceFromCenterForValue(e))}},{key:"getBasePosition",value:function(t){return this.getPointPositionForValue(t||0,this.getBaseValue())}},{key:"drawGrid",value:function(){var t,e,i,n=this,a=n.ctx,r=n.options,o=r.gridLines,s=r.angleLines,l=Jn(s.lineWidth,o.lineWidth),u=Jn(s.color,o.color);if(r.pointLabels.display&&function(t){var e=t.ctx,i=t.options,n=i.pointLabels,a=na(i),r=t.getDistanceFromCenterForValue(i.ticks.reverse?t.min:t.max),o=pe.options._parseFont(n);e.save(),e.font=o.string,e.textBaseline="middle";for(var s=t.chart.data.labels.length-1;s>=0;s--){var l=0===s?a/2:0,u=t.getPointPosition(s,r+l+5),c=ta(n.fontColor,s,Ot.fontColor);e.fillStyle=c;var h=at(t.getIndexAngle(s));e.textAlign=ra(h),sa(h,t._pointLabelSizes[s],u),oa(e,t.pointLabels[s],u,o.lineHeight)}e.restore()}(n),o.display&&n.ticks.forEach((function(t,i){0!==i&&(e=n.getDistanceFromCenterForValue(n.ticks[i].value),function(t,e,i,n){var a,r=t.ctx,o=e.circular,s=t.chart.data.labels.length,l=ta(e.color,n-1,void 0),u=ta(e.lineWidth,n-1,void 0);if((o||s)&&l&&u){if(r.save(),r.strokeStyle=l,r.lineWidth=u,r.setLineDash&&(r.setLineDash(e.borderDash||[]),r.lineDashOffset=e.borderDashOffset||0),r.beginPath(),o)r.arc(t.xCenter,t.yCenter,i,0,2*Math.PI);else{a=t.getPointPosition(0,i),r.moveTo(a.x,a.y);for(var c=1;c<s;c++)a=t.getPointPosition(c,i),r.lineTo(a.x,a.y)}r.closePath(),r.stroke(),r.restore()}}(n,o,e,i))})),s.display&&l&&u){for(a.save(),a.lineWidth=l,a.strokeStyle=u,a.setLineDash&&(a.setLineDash(ea([s.borderDash,o.borderDash,[]])),a.lineDashOffset=ea([s.borderDashOffset,o.borderDashOffset,0])),t=n.chart.data.labels.length-1;t>=0;t--)e=n.getDistanceFromCenterForValue(r.ticks.reverse?n.min:n.max),i=n.getPointPosition(t,e),a.beginPath(),a.moveTo(n.xCenter,n.yCenter),a.lineTo(i.x,i.y),a.stroke();a.restore()}}},{key:"drawLabels",value:function(){var t=this,e=t.ctx,i=t.options,n=i.ticks;if(n.display){var a,r,o=t.getIndexAngle(0),s=pe.options._parseFont(n),l=Jn(n.fontColor,Ot.fontColor);e.save(),e.font=s.string,e.translate(t.xCenter,t.yCenter),e.rotate(o),e.textAlign="center",e.textBaseline="middle",t.ticks.forEach((function(o,u){(0!==u||i.reverse)&&(a=t.getDistanceFromCenterForValue(t.ticks[u].value),n.showLabelBackdrop&&(r=e.measureText(o.label).width,e.fillStyle=n.backdropColor,e.fillRect(-r/2-n.backdropPaddingX,-a-s.size/2-n.backdropPaddingY,r+2*n.backdropPaddingX,s.size+2*n.backdropPaddingY)),e.fillStyle=l,e.fillText(o.label,0,-a))})),e.restore()}}},{key:"drawTitle",value:function(){}}]),i}(Xn);a(ua,"id","radialLinear"),a(ua,"defaults",ia);var ca=Number.MAX_SAFE_INTEGER||9007199254740991,ha={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},da=Object.keys(ha);function fa(t,e){return t-e}function va(t){var e,i,n=new Set;for(e=0,i=t.length;e<i;++e)n.add(t[e]);return n.size===i?t:f(n)}function pa(t,e){if(y(e))return null;var i=t._adapter,n=t.options.time,a=n.parser,r=e;return"function"==typeof a&&(r=a(r)),x(r)||(r="string"==typeof a?i.parse(r,a):i.parse(r)),null===r?r:(n.round&&(r=t._adapter.startOf(r,n.round)),+r)}function ga(t){var e,i,n="series"===t.options.distribution,a=t._cache.data||[];if(a.length)return a;var r=t.getMatchingVisibleMetas();if(n&&r.length)return r[0].controller.getAllParsedValues(t);for(e=0,i=r.length;e<i;++e)a=a.concat(r[e].controller.getAllParsedValues(t));return t._cache.data=va(a.sort(fa))}function ya(t){var e,i,n="series"===t.options.distribution,a=t._cache.labels||[];if(a.length)return a;var r=t.getLabels();for(e=0,i=r.length;e<i;++e)a.push(pa(t,r[e]));return t._cache.labels=n?a:va(a.sort(fa))}function ma(t,e,i,n){var a=_i(t,e,i),r=a.lo,o=a.hi,s=t[r],l=t[o],u=l[e]-s[e],c=u?(i-s[e])/u:0,h=(l[n]-s[n])*c;return s[n]+h}function ba(t,e,i,n){for(var a=da.length,r=da.indexOf(t);r<a-1;++r){var o=ha[da[r]],s=o.steps?o.steps:ca;if(o.common&&Math.ceil((i-e)/(s*o.size))<=n)return da[r]}return da[a-1]}function xa(t,e,i){if(t.length){var n=function(t,e){for(var i,n=t.length-1,a=0;n-a>1;)t[i=a+n>>1]<e?a=i:n=i;return{lo:a,hi:n}}(t,i),a=n.lo,r=n.hi,o=t[a]>=i?t[a]:t[r];e.add(o)}}function _a(t,e,i){var n,a,r=[],o={},s=e.length;for(n=0;n<s;++n)o[a=e[n]]=n,r.push({value:a,major:!1});return 0!==s&&i?function(t,e,i,n){var a,r,o=t._adapter,s=+o.startOf(e[0].value,n),l=e[e.length-1].value;for(a=s;a<=l;a=+o.add(a,1,n))(r=i[a])>=0&&(e[r].major=!0);return e}(t,r,o,i):r}function ka(t){return"labels"===t.options.ticks.source?ya(t):function(t){var e,i=t._adapter,n=t.min,a=t.max,r=t.options,o=r.time,s=o.unit||ba(o.minUnit,n,a,t._getLabelCapacity(n)),l=_(o.stepSize,1),u="week"===s&&o.isoWeekday,c=new Set,h=n;if(u&&(h=+i.startOf(h,"isoWeek",u)),h=+i.startOf(h,u?"day":s),i.diff(a,n,s)>1e5*l)throw new Error(n+" and "+a+" are too far apart with stepSize of "+l+" "+s);if("data"===t.options.ticks.source){var d=ga(t);for(e=h;e<a;e=+i.add(e,l,s))xa(d,c,e);e!==a&&"ticks"!==r.bounds||xa(d,c,e)}else{for(e=h;e<a;e=+i.add(e,l,s))c.add(e);e!==a&&"ticks"!==r.bounds||c.add(e)}return f(c)}(t)}function Ma(t){return"series"===t.options.distribution?function(t){var e=t._cache.all||[];if(e.length)return e;var i=ga(t),n=ya(t);return e=i.length&&n.length?va(i.concat(n).sort(fa)):i.length?i:n,e=t._cache.all=e}(t):[t.min,t.max]}var wa=function(t){function i(t){var n;e(this,i);var a=(n=h(this,l(i).call(this,t))).options,r=a.time||(a.time={}),o=n._adapter=new Rn._date(a.adapters.date);return n._cache={data:[],labels:[],all:[]},n._unit="day",n._majorUnit=void 0,n._offsets={},n._table=[],D(r.displayFormats,o.formats()),n}return s(i,t),n(i,[{key:"parse",value:function(t,e){return void 0===t?NaN:pa(this,t)}},{key:"parseObject",value:function(t,e,i){return t&&t.t?this.parse(t.t,i):void 0!==t[e]?this.parse(t[e],i):null}},{key:"invalidateCaches",value:function(){this._cache={data:[],labels:[],all:[]}}},{key:"determineDataLimits",value:function(){var t=this,e=t.options,i=t._adapter,n=e.time.unit||"day",a=t.getUserBounds(),r=a.min,o=a.max,s=a.minDefined,l=a.maxDefined;function u(t){s||isNaN(t.min)||(r=Math.min(r,t.min)),l||isNaN(t.max)||(o=Math.max(o,t.max))}s&&l||(u(function(t){var e=ya(t),i=Number.POSITIVE_INFINITY,n=Number.NEGATIVE_INFINITY;return e.length&&(i=e[0],n=e[e.length-1]),{min:i,max:n}}(t)),"ticks"===e.bounds&&"labels"===e.ticks.source||u(t.getMinMax(!1))),r=x(r)&&!isNaN(r)?r:+i.startOf(Date.now(),n),o=x(o)&&!isNaN(o)?o:+i.endOf(Date.now(),n)+1,t.min=Math.min(r,o),t.max=Math.max(r+1,o)}},{key:"buildTicks",value:function(){var t=this,e=t.options,i=e.time,n=e.ticks,a=e.distribution,r=ka(t);"ticks"===e.bounds&&r.length&&(t.min=t._userMin||r[0],t.max=t._userMax||r[r.length-1]);var o=t.min,s=t.max,l=function(t,e,i){for(var n=0,a=t.length-1;n<a&&t[n]<e;)n++;for(;a>n&&t[a]>i;)a--;return a++,n>0||a<t.length?t.slice(n,a):t}(r,o,s);return t._unit=i.unit||(n.autoSkip?ba(i.minUnit,t.min,t.max,t._getLabelCapacity(o)):function(t,e,i,n,a){for(var r=da.length-1;r>=da.indexOf(i);r--){var o=da[r];if(ha[o].common&&t._adapter.diff(a,n,o)>=e-1)return o}return da[i?da.indexOf(i):0]}(t,l.length,i.minUnit,t.min,t.max)),t._majorUnit=n.major.enabled&&"year"!==t._unit?function(t){for(var e=da.indexOf(t)+1,i=da.length;e<i;++e)if(ha[da[e]].common)return da[e]}(t._unit):void 0,t._table=function(t,e,i,n){if("linear"===n||!t.length)return[{time:e,pos:0},{time:i,pos:1}];var a,r,o,s,l,u=[],c=[e];for(a=0,r=t.length;a<r;++a)(s=t[a])>e&&s<i&&c.push(s);for(c.push(i),a=0,r=c.length;a<r;++a)l=c[a+1],o=c[a-1],s=c[a],void 0!==o&&void 0!==l&&Math.round((l+o)/2)===s||u.push({time:s,pos:a/(r-1)});return u}(Ma(t),o,s,a),t._offsets=function(t,e,i,n,a){var r,o,s=0,l=0;return a.offset&&e.length&&(r=ma(t,"time",e[0],"pos"),s=1===e.length?1-r:(ma(t,"time",e[1],"pos")-r)/2,o=ma(t,"time",e[e.length-1],"pos"),l=1===e.length?o:(o-ma(t,"time",e[e.length-2],"pos"))/2),{start:s,end:l,factor:1/(s+1+l)}}(t._table,ga(t),0,0,e),e.reverse&&l.reverse(),_a(t,l,t._majorUnit)}},{key:"getLabelForValue",value:function(t){var e=this._adapter,i=this.options.time;return i.tooltipFormat?e.format(t,i.tooltipFormat):e.format(t,i.displayFormats.datetime)}},{key:"_tickFormatFunction",value:function(t,e,i,n){var a=this.options,r=a.time.displayFormats,o=this._unit,s=this._majorUnit,l=o&&r[o],u=s&&r[s],c=i[e],h=s&&u&&c&&c.major,d=this._adapter.format(t,n||(h?u:l)),f=a.ticks.callback;return f?f(d,e,i):d}},{key:"generateTickLabels",value:function(t){var e,i,n;for(e=0,i=t.length;e<i;++e)(n=t[e]).label=this._tickFormatFunction(n.value,e,t)}},{key:"getPixelForValue",value:function(t){var e=this._offsets,i=ma(this._table,"time",t,"pos");return this.getPixelForDecimal((e.start+i)*e.factor)}},{key:"getPixelForTick",value:function(t){var e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}},{key:"getValueForPixel",value:function(t){var e=this._offsets,i=this.getDecimalForPixel(t)/e.factor-e.end;return ma(this._table,"pos",i,"time")}},{key:"_getLabelSize",value:function(t){var e=this.options.ticks,i=this.ctx.measureText(t).width,n=nt(this.isHorizontal()?e.maxRotation:e.minRotation),a=Math.cos(n),r=Math.sin(n),o=_(e.fontSize,Ot.fontSize);return{w:i*a+o*r,h:i*r+o*a}}},{key:"_getLabelCapacity",value:function(t){var e=this,i=e.options.time,n=i.displayFormats,a=n[i.unit]||n.millisecond,r=e._tickFormatFunction(t,0,_a(e,[t],e._majorUnit),a),o=e._getLabelSize(r),s=Math.floor(e.isHorizontal()?e.width/o.w:e.height/o.h)-1;return s>0?s:1}}]),i}(jn);a(wa,"id","time"),a(wa,"defaults",{distribution:"linear",bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{autoSkip:!1,source:"auto",major:{enabled:!1}}});var Sa=Object.freeze({__proto__:null,CategoryScale:Yn,LinearScale:$n,LogarithmicScale:Qn,RadialLinearScale:ua,TimeScale:wa});function Pa(t,e,i){var n=function(t){var e=t.options,i=e.fill,n=_(i&&i.target,i);return void 0===n&&(n=!!e.backgroundColor),!1!==n&&null!==n&&(!0===n?"origin":n)}(t),a=parseFloat(n);return x(a)&&Math.floor(a)===a?("-"!==n[0]&&"+"!==n[0]||(a=e+a),!(a===e||a<0||a>=i)&&a):["origin","start","end"].indexOf(n)>=0&&n}Ot.set("plugins",{filler:{propagate:!0}});var Da=function(){function t(i){e(this,t),this.x=i.x,this.y=i.y,this.radius=i.radius}return n(t,[{key:"pathSegment",value:function(t,e,i){var n=this.x,a=this.y,r=this.radius;return e=e||{start:0,end:2*Math.PI},i.reverse?t.arc(n,a,r,e.end,e.start,!0):t.arc(n,a,r,e.start,e.end),!i.bounds}},{key:"interpolate",value:function(t,e){var i=this.x,n=this.y,a=this.radius,r=t.angle;if("angle"===e)return{x:i+Math.cos(r)*a,y:n+Math.sin(r)*a,angle:r}}}]),t}();function Oa(t){return(t.scale||{}).getPointPositionForValue?function(t){var e,i,n=t.scale,a=t.fill,r=n.options,o=n.getLabels().length,s=[],l=r.reverse?n.max:n.min,u=r.reverse?n.min:n.max,c="start"===a?l:"end"===a?u:n.getBaseValue();if(r.gridLines.circular)return i=n.getPointPositionForValue(0,l),new Da({x:i.x,y:i.y,radius:n.getDistanceFromCenterForValue(c)});for(e=0;e<o;++e)s.push(n.getPointPositionForValue(e,c));return s}(t):function(t){var e,i=t.scale,n=void 0===i?{}:i,a=t.fill,r=null;return"start"===a?r=n.bottom:"end"===a?r=n.top:n.getBasePixel&&(r=n.getBasePixel()),x(r)?{x:(e=n.isHorizontal())?r:null,y:e?null:r}:null}(t)}function Aa(t){var e=t.chart,i=t.fill,n=t.line;if(x(i))return function(t,e){var i=t.getDatasetMeta(e);return i&&t.isDatasetVisible(e)?i.dataset:null}(e,i);var a=Oa(t),r=[],o=!1,s=!1;return a instanceof Da?a:(m(a)?(o=!0,r=a):(r=function(t,e){var i=t||{},n=i.x,a=void 0===n?null:n,r=i.y,o=void 0===r?null:r,s=e.points,l=[];return e.segments.forEach((function(t){var e=s[t.start],i=s[t.end];null!==o?(l.push({x:e.x,y:o,_prop:"x",_ref:e}),l.push({x:i.x,y:o,_prop:"x",_ref:i})):null!==a&&(l.push({x:a,y:e.y,_prop:"y",_ref:e}),l.push({x:a,y:i.y,_prop:"y",_ref:i}))})),l}(a,n),s=!0),r.length?new Ge({points:r,options:{tension:0},_loop:o,_fullLoop:o,_refPoints:s}):null)}function Ta(t,e,i){var n,a=t[e].fill,r=[e];if(!i)return a;for(;!1!==a&&-1===r.indexOf(a);){if(!x(a))return a;if(!(n=t[a]))return!1;if(n.visible)return a;r.push(a),a=n.fill}return!1}function Ca(t,e,i){t.beginPath(),e.path(t),t.lineTo(e.last().x,i),t.lineTo(e.first().x,i),t.closePath(),t.clip()}function Fa(t,e,i,n){if(!n){var a=e[t],r=i[t];return"angle"===t&&(a=lt(a),r=lt(r)),{property:t,start:a,end:r}}}function Ea(t,e,i,n){return t&&e?n(t[i],e[i]):t?t[i]:e?e[i]:0}function Ia(t,e,i){var n=e.chart.chartArea,a=n.top,r=n.bottom,o=i||{},s=o.property,l=o.start,u=o.end;"x"===s&&(t.beginPath(),t.rect(l,a,u-l,r-a),t.clip())}function La(t,e,i,n){var a=e.interpolate(i,n);a&&t.lineTo(a.x,a.y)}function Ra(t,e){var i=e.line,n=e.target,r=e.property,o=e.color,s=e.scale,l=function(t,e,i){var n=t.segments,r=t.points,o=e.points,s=[];if(e._refPoints)for(var l=0,u=o.length;l<u;++l){var c=o[l],h=c._prop;h&&(c[h]=c._ref[h])}for(var d=0;d<n.length;d++){var f=n[d],v=Fa(i,r[f.start],r[f.end],f.loop);if(e.segments)for(var p=je(e,v),g=0;g<p.length;++g)for(var y=p[g],m=Fa(i,o[y.start],o[y.end],y.loop),b=He(f,r,m),x=0;x<b.length;x++)s.push({source:b[x],target:y,start:a({},i,Ea(v,m,"start",Math.max)),end:a({},i,Ea(v,m,"end",Math.min))});else s.push({source:f,target:v,start:r[f.start],end:r[f.end]})}return s}(e.line,e.target,r);t.fillStyle=o;for(var u=0,c=l.length;u<c;++u){var h=l[u],d=h.source,f=h.target,v=h.start,p=h.end;t.save(),Ia(t,s,Fa(r,v,p)),t.beginPath();var g=!!i.pathSegment(t,d);g?t.closePath():La(t,n,p,r);var y=!!n.pathSegment(t,f,{move:g,reverse:!0}),m=g&&y;m||La(t,n,v,r),t.closePath(),t.fill(m?"evenodd":"nonzero"),t.restore()}}var za={id:"filler",afterDatasetsUpdate:function(t,e){var i,n,a,r,o=(t.data.datasets||[]).length,s=e.propagate,l=[];for(n=0;n<o;++n)r=null,(a=(i=t.getDatasetMeta(n)).dataset)&&a.options&&a instanceof Ge&&(r={visible:t.isDatasetVisible(n),fill:Pa(a,n,o),chart:t,scale:i.vScale,line:a,target:void 0}),i.$filler=r,l.push(r);for(n=0;n<o;++n)(r=l[n])&&!1!==r.fill&&(r.fill=Ta(l,n,s),r.target=!1!==r.fill&&Aa(r))},beforeDatasetsDraw:function(t){var e,i,n=t.getSortedVisibleDatasetMetas(),a=t.chartArea;for(e=n.length-1;e>=0;--e)(i=n[e].$filler)&&i.line.updateControlPoints(a)},beforeDatasetDraw:function(t,e){var i=t.chartArea,n=t.ctx,a=e.meta.$filler;if(a&&!1!==a.fill){var r=a.line,o=a.target,s=a.scale,l=r.options,u=l.fill,c=l.backgroundColor||Ot.color,h=u||{},d=h.above,f=void 0===d?c:d,v=h.below,p=void 0===v?c:v;o&&r.points.length&&(H(n,i),function(t,e){var i=e.line,n=e.target,a=e.above,r=e.below,o=e.area,s=e.scale,l=i._loop?"angle":"x";t.save(),"x"===l&&r!==a&&(Ca(t,n,o.top),Ra(t,{line:i,target:n,color:a,scale:s,property:l}),t.restore(),t.save(),Ca(t,n,o.bottom)),Ra(t,{line:i,target:n,color:r,scale:s,property:l}),t.restore()}(n,{line:r,target:o,above:f,below:p,area:i,scale:s}),j(n))}}};function Ba(t,e){return t.usePointStyle&&t.boxWidth>e?e:t.boxWidth}Ot.set("legend",{display:!0,position:"top",align:"center",fullWidth:!0,reverse:!1,weight:1e3,onClick:function(t,e){var i=e.datasetIndex,n=this.chart;n.isDatasetVisible(i)?(n.hide(i),e.hidden=!0):(n.show(i),e.hidden=!1)},onHover:null,onLeave:null,labels:{boxWidth:40,padding:10,generateLabels:function(t){var e=t.data.datasets,i=t.options.legend||{},n=i.labels&&i.labels.usePointStyle;return t._getSortedDatasetMetas().map((function(t){var i=t.controller.getStyle(n?0:void 0);return{text:e[t.index].label,fillStyle:i.backgroundColor,hidden:!t.visible,lineCap:i.borderCapStyle,lineDash:i.borderDash,lineDashOffset:i.borderDashOffset,lineJoin:i.borderJoinStyle,lineWidth:i.borderWidth,strokeStyle:i.borderColor,pointStyle:i.pointStyle,rotation:i.rotation,datasetIndex:t.index}}),this)}},title:{display:!1,position:"center",text:""}});var Va=function(t){function i(t){var n;return e(this,i),r(c(n=h(this,l(i).call(this))),t),n.legendHitBoxes=[],n._hoveredItem=null,n.doughnutMode=!1,n.chart=t.chart,n.options=t.options,n.ctx=t.ctx,n.legendItems=void 0,n.columnWidths=void 0,n.columnHeights=void 0,n.lineWidths=void 0,n._minSize=void 0,n.maxHeight=void 0,n.maxWidth=void 0,n.top=void 0,n.bottom=void 0,n.left=void 0,n.right=void 0,n.height=void 0,n.width=void 0,n._margins=void 0,n.paddingTop=void 0,n.paddingBottom=void 0,n.paddingLeft=void 0,n.paddingRight=void 0,n.position=void 0,n.weight=void 0,n.fullWidth=void 0,n}return s(i,t),n(i,[{key:"beforeUpdate",value:function(){}},{key:"update",value:function(t,e,i){var n=this;n.beforeUpdate(),n.maxWidth=t,n.maxHeight=e,n._margins=i,n.beforeSetDimensions(),n.setDimensions(),n.afterSetDimensions(),n.beforeBuildLabels(),n.buildLabels(),n.afterBuildLabels(),n.beforeFit(),n.fit(),n.afterFit(),n.afterUpdate()}},{key:"afterUpdate",value:function(){}},{key:"beforeSetDimensions",value:function(){}},{key:"setDimensions",value:function(){var t=this;t.isHorizontal()?(t.width=t.maxWidth,t.left=0,t.right=t.width):(t.height=t.maxHeight,t.top=0,t.bottom=t.height),t.paddingLeft=0,t.paddingTop=0,t.paddingRight=0,t.paddingBottom=0,t._minSize={width:0,height:0}}},{key:"afterSetDimensions",value:function(){}},{key:"beforeBuildLabels",value:function(){}},{key:"buildLabels",value:function(){var t=this,e=t.options.labels||{},i=k(e.generateLabels,[t.chart],t)||[];e.filter&&(i=i.filter((function(i){return e.filter(i,t.chart.data)}))),t.options.reverse&&i.reverse(),t.legendItems=i}},{key:"afterBuildLabels",value:function(){}},{key:"beforeFit",value:function(){}},{key:"fit",value:function(){var t=this,e=t.options,i=e.labels,n=e.display,a=t.ctx,r=Ct(i),o=r.size,s=t.legendHitBoxes=[],l=t._minSize,u=t.isHorizontal(),c=t._computeTitleHeight();if(u?(l.width=t.maxWidth,l.height=n?10:0):(l.width=n?10:0,l.height=t.maxHeight),n){if(a.font=r.string,u){var h=t.lineWidths=[0],d=c;a.textAlign="left",a.textBaseline="middle",t.legendItems.forEach((function(t,e){var n=Ba(i,o)+o/2+a.measureText(t.text).width;(0===e||h[h.length-1]+n+2*i.padding>l.width)&&(d+=o+i.padding,h[h.length-(e>0?0:1)]=0),s[e]={left:0,top:0,width:n,height:o},h[h.length-1]+=n+i.padding})),l.height+=d}else{var f=i.padding,v=t.columnWidths=[],p=t.columnHeights=[],g=i.padding,y=0,m=0,b=l.height-c;t.legendItems.forEach((function(t,e){var n=Ba(i,o)+o/2+a.measureText(t.text).width;e>0&&m+o+2*f>b&&(g+=y+i.padding,v.push(y),p.push(m),y=0,m=0),y=Math.max(y,n),m+=o+f,s[e]={left:0,top:0,width:n,height:o}})),g+=y,v.push(y),p.push(m),l.width+=g}t.width=l.width,t.height=l.height}else t.width=l.width=t.height=l.height=0}},{key:"afterFit",value:function(){}},{key:"isHorizontal",value:function(){return"top"===this.options.position||"bottom"===this.options.position}},{key:"draw",value:function(){var t=this,e=t.options,i=e.labels,n=Ot.color,a=Ot.elements.line,r=t.height,o=t.columnHeights,s=t.width,l=t.lineWidths;if(e.display){t.drawTitle();var u,c=It(e.rtl,t.left,t._minSize.width),h=t.ctx,d=_(i.fontColor,Ot.fontColor),f=Ct(i),v=f.size;h.textAlign=c.textAlign("left"),h.textBaseline="middle",h.lineWidth=.5,h.strokeStyle=d,h.fillStyle=d,h.font=f.string;var p=Ba(i,v),g=t.legendHitBoxes,y=function(t,n){switch(e.align){case"start":return i.padding;case"end":return t-n;default:return(t-n+i.padding)/2}},m=t.isHorizontal(),b=this._computeTitleHeight();u=m?{x:t.left+y(s,l[0]),y:t.top+i.padding+b,line:0}:{x:t.left+i.padding,y:t.top+y(r,o[0])+b,line:0},Lt(t.ctx,e.textDirection);var x=v+i.padding;t.legendItems.forEach((function(e,d){var f=h.measureText(e.text).width,b=p+v/2+f,k=u.x,M=u.y;c.setWidth(t._minSize.width),m?d>0&&k+b+i.padding>t.left+t._minSize.width&&(M=u.y+=x,u.line++,k=u.x=t.left+y(s,l[u.line])):d>0&&M+x>t.top+t._minSize.height&&(k=u.x=k+t.columnWidths[u.line]+i.padding,u.line++,M=u.y=t.top+y(r,o[u.line]));var w=c.x(k);!function(t,e,r){if(!(isNaN(p)||p<=0)){h.save();var o=_(r.lineWidth,a.borderWidth);if(h.fillStyle=_(r.fillStyle,n),h.lineCap=_(r.lineCap,a.borderCapStyle),h.lineDashOffset=_(r.lineDashOffset,a.borderDashOffset),h.lineJoin=_(r.lineJoin,a.borderJoinStyle),h.lineWidth=o,h.strokeStyle=_(r.strokeStyle,n),h.setLineDash&&h.setLineDash(_(r.lineDash,a.borderDash)),i&&i.usePointStyle){var s={radius:p*Math.SQRT2/2,pointStyle:r.pointStyle,rotation:r.rotation,borderWidth:o},l=c.xPlus(t,p/2);W(h,s,l,e+v/2)}else h.fillRect(c.leftForLtr(t,p),e,p,v),0!==o&&h.strokeRect(c.leftForLtr(t,p),e,p,v);h.restore()}}(w,M,e),g[d].left=c.leftForLtr(w,g[d].width),g[d].top=M,function(t,e,i,n){var a=v/2,r=c.xPlus(t,p+a),o=e+a;h.fillText(i.text,r,o),i.hidden&&(h.beginPath(),h.lineWidth=2,h.moveTo(r,o),h.lineTo(c.xPlus(r,n),o),h.stroke())}(w,M,e,f),m?u.x+=b+i.padding:u.y+=x})),Rt(t.ctx,e.textDirection)}}},{key:"drawTitle",value:function(){var t=this,e=t.options,i=e.title,n=Ct(i),a=Tt(i.padding);if(i.display){var r,o,s=It(e.rtl,t.left,t._minSize.width),l=t.ctx,u=_(i.fontColor,Ot.fontColor),c=i.position,h=n.size/2,d=t.top+a.top+h,v=t.left,p=t.width;if(this.isHorizontal())switch(p=Math.max.apply(Math,f(t.lineWidths)),e.align){case"start":break;case"end":v=t.right-p;break;default:v=(t.left+t.right)/2-p/2}else{var g=Math.max.apply(Math,f(t.columnHeights));switch(e.align){case"start":break;case"end":d+=t.height-g;break;default:d+=(t.height-g)/2}}switch(c){case"start":r=v,o="left";break;case"end":r=v+p,o="right";break;default:r=v+p/2,o="center"}l.textAlign=s.textAlign(o),l.textBaseline="middle",l.strokeStyle=u,l.fillStyle=u,l.font=n.string,l.fillText(i.text,r,d)}}},{key:"_computeTitleHeight",value:function(){var t=this.options.title,e=Ct(t),i=Tt(t.padding);return t.display?e.lineHeight+i.height:0}},{key:"_getLegendItemAt",value:function(t,e){var i,n,a,r=this;if(t>=r.left&&t<=r.right&&e>=r.top&&e<=r.bottom)for(a=r.legendHitBoxes,i=0;i<a.length;++i)if(t>=(n=a[i]).left&&t<=n.left+n.width&&e>=n.top&&e<=n.top+n.height)return r.legendItems[i];return null}},{key:"handleEvent",value:function(t){var e=this,i=e.options,n="mouseup"===t.type?"click":t.type;if("mousemove"===n){if(!i.onHover&&!i.onLeave)return}else{if("click"!==n)return;if(!i.onClick)return}var a=e._getLegendItemAt(t.x,t.y);"click"===n?a&&i.onClick&&i.onClick.call(e,t.native,a):(i.onLeave&&a!==e._hoveredItem&&(e._hoveredItem&&i.onLeave.call(e,t.native,e._hoveredItem),e._hoveredItem=a),i.onHover&&a&&i.onHover.call(e,t.native,a))}}]),i}(Fe);function Wa(t,e){var i=new Va({ctx:t.ctx,options:e,chart:t});Vi.configure(t,i,e),Vi.addBox(t,i),t.legend=i}var Na={id:"legend",_element:Va,beforeInit:function(t){var e=t.options.legend;e&&Wa(t,e)},afterUpdate:function(t){var e=t.options.legend,i=t.legend;e?(D(e,Ot.legend),i?(Vi.configure(t,i,e),i.options=e,i.buildLabels()):Wa(t,e)):i&&(Vi.removeBox(t,i),delete t.legend)},afterEvent:function(t,e){var i=t.legend;i&&i.handleEvent(e)}};Ot.set("title",{align:"center",display:!1,fontStyle:"bold",fullWidth:!0,padding:10,position:"top",text:"",weight:2e3});var Ha=function(t){function i(t){var n;return e(this,i),r(c(n=h(this,l(i).call(this))),t),n.chart=t.chart,n.options=t.options,n.ctx=t.ctx,n._margins=void 0,n._padding=void 0,n.legendHitBoxes=[],n.top=void 0,n.bottom=void 0,n.left=void 0,n.right=void 0,n.width=void 0,n.height=void 0,n.maxWidth=void 0,n.maxHeight=void 0,n.position=void 0,n.weight=void 0,n.fullWidth=void 0,n}return s(i,t),n(i,[{key:"beforeUpdate",value:function(){}},{key:"update",value:function(t,e,i){var n=this;n.beforeUpdate(),n.maxWidth=t,n.maxHeight=e,n._margins=i,n.beforeSetDimensions(),n.setDimensions(),n.afterSetDimensions(),n.beforeBuildLabels(),n.buildLabels(),n.afterBuildLabels(),n.beforeFit(),n.fit(),n.afterFit(),n.afterUpdate()}},{key:"afterUpdate",value:function(){}},{key:"beforeSetDimensions",value:function(){}},{key:"setDimensions",value:function(){var t=this;t.isHorizontal()?(t.width=t.maxWidth,t.left=0,t.right=t.width):(t.height=t.maxHeight,t.top=0,t.bottom=t.height)}},{key:"afterSetDimensions",value:function(){}},{key:"beforeBuildLabels",value:function(){}},{key:"buildLabels",value:function(){}},{key:"afterBuildLabels",value:function(){}},{key:"beforeFit",value:function(){}},{key:"fit",value:function(){var t=this,e=t.options,i={},n=t.isHorizontal();if(e.display){var a=pe.isArray(e.text)?e.text.length:1;t._padding=pe.options.toPadding(e.padding);var r=a*pe.options._parseFont(e).lineHeight+t._padding.height;t.width=i.width=n?t.maxWidth:r,t.height=i.height=n?r:t.maxHeight}else t.width=i.width=t.height=i.height=0}},{key:"afterFit",value:function(){}},{key:"isHorizontal",value:function(){var t=this.options.position;return"top"===t||"bottom"===t}},{key:"draw",value:function(){var t=this,e=t.ctx,i=t.options;if(i.display){var n,a,r,o,s=pe.options._parseFont(i),l=s.lineHeight,u=l/2+t._padding.top,c=0,h=t.top,d=t.left,f=t.bottom,v=t.right;if(t.isHorizontal()){switch(i.align){case"start":a=d,o="left";break;case"end":a=v,o="right";break;default:a=d+(v-d)/2,o="center"}r=h+u,n=v-d}else{switch(a="left"===i.position?d+u:v-u,i.align){case"start":r="left"===i.position?f:h,o="left";break;case"end":r="left"===i.position?h:f,o="right";break;default:r=h+(f-h)/2,o="center"}n=f-h,c=Math.PI*("left"===i.position?-.5:.5)}e.save(),e.fillStyle=pe.valueOrDefault(i.fontColor,Ot.fontColor),e.font=s.string,e.translate(a,r),e.rotate(c),e.textAlign=o,e.textBaseline="middle";var p=i.text;if(pe.isArray(p))for(var g=0,y=0;y<p.length;++y)e.fillText(p[y],0,g,n),g+=l;else e.fillText(p,0,0,n);e.restore()}}}]),i}(Fe);function ja(t,e){var i=new Ha({ctx:t.ctx,options:e,chart:t});Vi.configure(t,i,e),Vi.addBox(t,i),t.titleBlock=i}var Ya={id:"title",_element:Ha,beforeInit:function(t){var e=t.options.title;e&&ja(t,e)},beforeUpdate:function(t){var e=t.options.title,i=t.titleBlock;e?(pe.mergeIf(e,Ot.title),i?(Vi.configure(t,i,e),i.options=e):ja(t,e)):i&&(Vi.removeBox(t,i),delete t.titleBlock)}},Ua=pe.valueOrDefault,Xa=pe.rtl.getRtlAdapter;Ot.set("tooltips",{enabled:!0,custom:null,mode:"nearest",position:"average",intersect:!0,backgroundColor:"rgba(0,0,0,0.8)",titleFontStyle:"bold",titleSpacing:2,titleMarginBottom:6,titleFontColor:"#fff",titleAlign:"left",bodySpacing:2,bodyFontColor:"#fff",bodyAlign:"left",footerFontStyle:"bold",footerSpacing:2,footerMarginTop:6,footerFontColor:"#fff",footerAlign:"left",yPadding:6,xPadding:6,caretPadding:2,caretSize:5,cornerRadius:6,multiKeyBackground:"#fff",displayColors:!0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart",numbers:{type:"number",properties:["x","y","width","height"]},opacity:{easing:"linear",duration:200}},callbacks:{beforeTitle:pe.noop,title:function(t,e){var i="",n=e.labels,a=n?n.length:0;if(t.length>0){var r=t[0];r.label?i=r.label:a>0&&r.index<a&&(i=n[r.index])}return i},afterTitle:pe.noop,beforeBody:pe.noop,beforeLabel:pe.noop,label:function(t,e){var i=e.datasets[t.datasetIndex].label||"";i&&(i+=": ");var n=t.value;return pe.isNullOrUndef(n)||(i+=n),i},labelColor:function(t,e){var i=e.getDatasetMeta(t.datasetIndex).controller.getStyle(t.index);return{borderColor:i.borderColor,backgroundColor:i.backgroundColor}},labelTextColor:function(){return this.options.bodyFontColor},afterLabel:pe.noop,afterBody:pe.noop,beforeFooter:pe.noop,footer:pe.noop,afterFooter:pe.noop}});var qa={average:function(t){if(!t.length)return!1;var e,i,n=0,a=0,r=0;for(e=0,i=t.length;e<i;++e){var o=t[e].element;if(o&&o.hasValue()){var s=o.tooltipPosition();n+=s.x,a+=s.y,++r}}return{x:n/r,y:a/r}},nearest:function(t,e){var i,n,a,r=e.x,o=e.y,s=Number.POSITIVE_INFINITY;for(i=0,n=t.length;i<n;++i){var l=t[i].element;if(l&&l.hasValue()){var u=l.getCenterPoint(),c=pe.math.distanceBetweenPoints(e,u);c<s&&(s=c,a=l)}}if(a){var h=a.tooltipPosition();r=h.x,o=h.y}return{x:r,y:o}}};function $a(t,e){return e&&(pe.isArray(e)?Array.prototype.push.apply(t,e):t.push(e)),t}function Ga(t){return("string"==typeof t||t instanceof String)&&t.indexOf("\n")>-1?t.split("\n"):t}function Ka(t,e){var i=e.datasetIndex,n=e.index,a=t.getDatasetMeta(i).controller.getLabelAndValue(n);return{label:a.label,value:a.value,index:n,datasetIndex:i}}function Za(t){var e=t._chart.ctx,i=t.body,n=t.footer,a=t.options,r=t.title,o=a.bodyFontSize,s=a.footerFontSize,l=a.titleFontSize,u=a.boxWidth,c=a.boxHeight,h=r.length,d=n.length,f=i.length,v=2*a.yPadding,p=0,g=i.reduce((function(t,e){return t+e.before.length+e.lines.length+e.after.length}),0);(g+=t.beforeBody.length+t.afterBody.length,h&&(v+=h*l+(h-1)*a.titleSpacing+a.titleMarginBottom),g)&&(v+=f*(a.displayColors?Math.max(c,o):o)+(g-f)*o+(g-1)*a.bodySpacing);d&&(v+=a.footerMarginTop+d*s+(d-1)*a.footerSpacing);var y=0,m=function(t){p=Math.max(p,e.measureText(t).width+y)};return e.save(),e.font=pe.fontString(l,a.titleFontStyle,a.titleFontFamily),pe.each(t.title,m),e.font=pe.fontString(o,a.bodyFontStyle,a.bodyFontFamily),pe.each(t.beforeBody.concat(t.afterBody),m),y=a.displayColors?u+2:0,pe.each(i,(function(t){pe.each(t.before,m),pe.each(t.lines,m),pe.each(t.after,m)})),y=0,e.font=pe.fontString(s,a.footerFontStyle,a.footerFontFamily),pe.each(t.footer,m),e.restore(),{width:p+=2*a.xPadding,height:v}}function Qa(t,e,i){var n,a,r=i.x,o=i.y,s=i.width,l=i.height,u=t.chartArea,c="center",h="center";o<l?h="top":o>t.height-l&&(h="bottom");var d=(u.left+u.right)/2,f=(u.top+u.bottom)/2;"center"===h?(n=function(t){return t<=d},a=function(t){return t>d}):(n=function(t){return t<=s/2},a=function(e){return e>=t.width-s/2});var v=function(t){return t<=f?"top":"bottom"};return n(r)?(c="left",r+s+e.caretSize+e.caretPadding>t.width&&(c="center",h=v(o))):a(r)&&(c="right",function(t){return t-s-e.caretSize-e.caretPadding<0}(r)&&(c="center",h=v(o))),{xAlign:e.xAlign?e.xAlign:c,yAlign:e.yAlign?e.yAlign:h}}function Ja(t,e,i,n){var a=t.caretSize,r=t.caretPadding,o=t.cornerRadius,s=i.xAlign,l=i.yAlign,u=a+r,c=o+r,h=function(t,e,i){var n=t.x,a=t.width;return"right"===e?n-=a:"center"===e&&((n-=a/2)+a>i&&(n=i-a),n<0&&(n=0)),n}(e,s,n.width);return"center"===l?"left"===s?h+=u:"right"===s&&(h-=u):"left"===s?h-=c:"right"===s&&(h+=c),{x:h,y:function(t,e,i){var n=t.y,a=t.height;return"top"===e?n+=i:n-="bottom"===e?a+i:a/2,n}(e,l,u)}}function tr(t,e){var i=t.options;return"center"===e?t.x+t.width/2:"right"===e?t.x+t.width-i.xPadding:t.x+i.xPadding}function er(t){return $a([],Ga(t))}var ir=function(t){function i(t){var n;return e(this,i),(n=h(this,l(i).call(this))).opacity=0,n._active=[],n._chart=t._chart,n._eventPosition=void 0,n._size=void 0,n._cachedAnimations=void 0,n.$animations=void 0,n.options=void 0,n.dataPoints=void 0,n.title=void 0,n.beforeBody=void 0,n.body=void 0,n.afterBody=void 0,n.footer=void 0,n.xAlign=void 0,n.yAlign=void 0,n.x=void 0,n.y=void 0,n.height=void 0,n.width=void 0,n.caretX=void 0,n.caretY=void 0,n.labelColors=void 0,n.labelTextColors=void 0,n.initialize(),n}return s(i,t),n(i,[{key:"initialize",value:function(){this.options=function(t){return(t=r({},Ot.tooltips,t)).bodyFontFamily=Ua(t.bodyFontFamily,Ot.fontFamily),t.bodyFontStyle=Ua(t.bodyFontStyle,Ot.fontStyle),t.bodyFontSize=Ua(t.bodyFontSize,Ot.fontSize),t.boxHeight=Ua(t.boxHeight,t.bodyFontSize),t.boxWidth=Ua(t.boxWidth,t.bodyFontSize),t.titleFontFamily=Ua(t.titleFontFamily,Ot.fontFamily),t.titleFontStyle=Ua(t.titleFontStyle,Ot.fontStyle),t.titleFontSize=Ua(t.titleFontSize,Ot.fontSize),t.footerFontFamily=Ua(t.footerFontFamily,Ot.fontFamily),t.footerFontStyle=Ua(t.footerFontStyle,Ot.fontStyle),t.footerFontSize=Ua(t.footerFontSize,Ot.fontSize),t}(this._chart.options.tooltips)}},{key:"_resolveAnimations",value:function(){var t=this,e=t._cachedAnimations;if(e)return e;var i=t._chart.options.animation&&t.options.animation,n=new xe(t._chart,i);return t._cachedAnimations=Object.freeze(n),n}},{key:"getTitle",value:function(t,e){var i=this.options.callbacks,n=i.beforeTitle.apply(this,[t,e]),a=i.title.apply(this,[t,e]),r=i.afterTitle.apply(this,[t,e]),o=[];return o=$a(o,Ga(n)),o=$a(o,Ga(a)),o=$a(o,Ga(r))}},{key:"getBeforeBody",value:function(t,e){return er(this.options.callbacks.beforeBody.apply(this,[t,e]))}},{key:"getBody",value:function(t,e){var i=this,n=i.options.callbacks,a=[];return pe.each(t,(function(t){var r={before:[],lines:[],after:[]};$a(r.before,Ga(n.beforeLabel.call(i,t,e))),$a(r.lines,n.label.call(i,t,e)),$a(r.after,Ga(n.afterLabel.call(i,t,e))),a.push(r)})),a}},{key:"getAfterBody",value:function(t,e){return er(this.options.callbacks.afterBody.apply(this,[t,e]))}},{key:"getFooter",value:function(t,e){var i=this.options.callbacks,n=i.beforeFooter.apply(this,[t,e]),a=i.footer.apply(this,[t,e]),r=i.afterFooter.apply(this,[t,e]),o=[];return o=$a(o,Ga(n)),o=$a(o,Ga(a)),o=$a(o,Ga(r))}},{key:"_createItems",value:function(){var t,e,i=this,n=i._active,a=i.options,r=i._chart.data,o=[],s=[],l=[];for(t=0,e=n.length;t<e;++t)l.push(Ka(i._chart,n[t]));return a.filter&&(l=l.filter((function(t){return a.filter(t,r)}))),a.itemSort&&(l=l.sort((function(t,e){return a.itemSort(t,e,r)}))),pe.each(l,(function(t){o.push(a.callbacks.labelColor.call(i,t,i._chart)),s.push(a.callbacks.labelTextColor.call(i,t,i._chart))})),i.labelColors=o,i.labelTextColors=s,i.dataPoints=l,l}},{key:"update",value:function(t){var e,i=this,n=i.options,a=i._active;if(a.length){var o=i._chart.data,s=qa[n.position].call(i,a,i._eventPosition),l=i._createItems();i.title=i.getTitle(l,o),i.beforeBody=i.getBeforeBody(l,o),i.body=i.getBody(l,o),i.afterBody=i.getAfterBody(l,o),i.footer=i.getFooter(l,o);var u=i._size=Za(i),c=r({},s,u),h=Qa(i._chart,n,c),d=Ja(n,c,h,i._chart);i.xAlign=h.xAlign,i.yAlign=h.yAlign,e={opacity:1,x:d.x,y:d.y,width:u.width,height:u.height,caretX:s.x,caretY:s.y}}else 0!==i.opacity&&(e={opacity:0});e&&i._resolveAnimations().update(i,e),t&&n.custom&&n.custom.call(i)}},{key:"drawCaret",value:function(t,e,i){var n=this.getCaretPosition(t,i);e.lineTo(n.x1,n.y1),e.lineTo(n.x2,n.y2),e.lineTo(n.x3,n.y3)}},{key:"getCaretPosition",value:function(t,e){var i,n,a,r,o,s,l=this.xAlign,u=this.yAlign,c=this.options,h=c.cornerRadius,d=c.caretSize,f=t.x,v=t.y,p=e.width,g=e.height;return"center"===u?(n="left"===l?(i=f)-d:(i=f+p)+d,a=i,r=(o=v+g/2)+d,s=o-d):(i=(n="left"===l?f+h+d:"right"===l?f+p-h-d:this.caretX)-d,a=n+d,o="top"===u?(r=v)-d:(r=v+g)+d,s=r),{x1:i,x2:n,x3:a,y1:r,y2:o,y3:s}}},{key:"drawTitle",value:function(t,e){var i,n,a,r=this,o=r.options,s=r.title,l=s.length;if(l){var u=Xa(o.rtl,r.x,r.width);for(t.x=tr(r,o.titleAlign),e.textAlign=u.textAlign(o.titleAlign),e.textBaseline="middle",i=o.titleFontSize,n=o.titleSpacing,e.fillStyle=o.titleFontColor,e.font=pe.fontString(i,o.titleFontStyle,o.titleFontFamily),a=0;a<l;++a)e.fillText(s[a],u.x(t.x),t.y+i/2),t.y+=i+n,a+1===l&&(t.y+=o.titleMarginBottom-n)}}},{key:"_drawColorBox",value:function(t,e,i,n){var a=this.options,r=this.labelColors[i],o=a.boxHeight,s=a.boxWidth,l=a.bodyFontSize,u=tr(this,"left"),c=n.x(u),h=o<l?(l-o)/2:0,d=e.y+h;t.fillStyle=a.multiKeyBackground,t.fillRect(n.leftForLtr(c,s),d,s,o),t.lineWidth=1,t.strokeStyle=r.borderColor,t.strokeRect(n.leftForLtr(c,s),d,s,o),t.fillStyle=r.backgroundColor,t.fillRect(n.leftForLtr(n.xPlus(c,1),s-2),d+1,s-2,o-2),t.fillStyle=this.labelTextColors[i]}},{key:"drawBody",value:function(t,e){var i,n,a,r,o,s,l,u=this,c=u.body,h=u.options,d=h.bodyFontSize,f=h.bodySpacing,v=h.bodyAlign,p=h.displayColors,g=h.boxHeight,y=h.boxWidth,m=d,b=0,x=Xa(h.rtl,u.x,u.width),_=function(i){e.fillText(i,x.x(t.x+b),t.y+m/2),t.y+=m+f},k=x.textAlign(v);for(e.textAlign=v,e.textBaseline="middle",e.font=pe.fontString(d,h.bodyFontStyle,h.bodyFontFamily),t.x=tr(u,k),e.fillStyle=h.bodyFontColor,pe.each(u.beforeBody,_),b=p&&"right"!==k?"center"===v?y/2+1:y+2:0,r=0,s=c.length;r<s;++r){for(i=c[r],n=u.labelTextColors[r],e.fillStyle=n,pe.each(i.before,_),a=i.lines,p&&a.length&&(u._drawColorBox(e,t,r,x),m=Math.max(d,g)),o=0,l=a.length;o<l;++o)_(a[o]),m=d;pe.each(i.after,_)}b=0,m=d,pe.each(u.afterBody,_),t.y-=f}},{key:"drawFooter",value:function(t,e){var i,n,a=this,r=a.options,o=a.footer,s=o.length;if(s){var l=Xa(r.rtl,a.x,a.width);for(t.x=tr(a,r.footerAlign),t.y+=r.footerMarginTop,e.textAlign=l.textAlign(r.footerAlign),e.textBaseline="middle",i=r.footerFontSize,e.fillStyle=r.footerFontColor,e.font=pe.fontString(i,r.footerFontStyle,r.footerFontFamily),n=0;n<s;++n)e.fillText(o[n],l.x(t.x),t.y+i/2),t.y+=i+r.footerSpacing}}},{key:"drawBackground",value:function(t,e,i){var n=this.xAlign,a=this.yAlign,r=this.options,o=t.x,s=t.y,l=i.width,u=i.height,c=r.cornerRadius;e.fillStyle=r.backgroundColor,e.strokeStyle=r.borderColor,e.lineWidth=r.borderWidth,e.beginPath(),e.moveTo(o+c,s),"top"===a&&this.drawCaret(t,e,i),e.lineTo(o+l-c,s),e.quadraticCurveTo(o+l,s,o+l,s+c),"center"===a&&"right"===n&&this.drawCaret(t,e,i),e.lineTo(o+l,s+u-c),e.quadraticCurveTo(o+l,s+u,o+l-c,s+u),"bottom"===a&&this.drawCaret(t,e,i),e.lineTo(o+c,s+u),e.quadraticCurveTo(o,s+u,o,s+u-c),"center"===a&&"left"===n&&this.drawCaret(t,e,i),e.lineTo(o,s+c),e.quadraticCurveTo(o,s,o+c,s),e.closePath(),e.fill(),r.borderWidth>0&&e.stroke()}},{key:"_updateAnimationTarget",value:function(){var t=this,e=t._chart,i=t.options,n=t.$animations,a=n&&n.x,o=n&&n.y;if(a||o){var s=qa[i.position].call(t,t._active,t._eventPosition);if(!s)return;var l=t._size=Za(t),u=r({},s,t._size),c=Qa(e,i,u),h=Ja(i,u,c,e);a._to===h.x&&o._to===h.y||(t.xAlign=c.xAlign,t.yAlign=c.yAlign,t.width=l.width,t.height=l.height,t.caretX=s.x,t.caretY=s.y,t._resolveAnimations().update(t,h))}}},{key:"draw",value:function(t){var e=this,i=e.options,n=e.opacity;if(n){e._updateAnimationTarget();var a={width:e.width,height:e.height},r={x:e.x,y:e.y};n=Math.abs(n)<.001?0:n;var o=e.title.length||e.beforeBody.length||e.body.length||e.afterBody.length||e.footer.length;i.enabled&&o&&(t.save(),t.globalAlpha=n,e.drawBackground(r,t,a),pe.rtl.overrideTextDirection(t,i.textDirection),r.y+=i.yPadding,e.drawTitle(r,t),e.drawBody(r,t),e.drawFooter(r,t),pe.rtl.restoreTextDirection(t,i.textDirection),t.restore())}}},{key:"handleEvent",value:function(t,e){var i,n=this,a=n.options,r=n._active||[],o=[];return"mouseout"!==t.type&&(o=n._chart.getElementsAtEventForMode(t,a.mode,a,e),a.reverse&&o.reverse()),(i=e||!pe._elementsEqual(o,r))&&(n._active=o,(a.enabled||a.custom)&&(n._eventPosition={x:t.x,y:t.y},n.update(!0))),i}}]),i}(Fe);ir.positioners=qa;var nr={filler:za,legend:Na,title:Ya,tooltip:{id:"tooltip",_element:ir,positioners:qa,afterInit:function(t){t.options.tooltips&&(t.tooltip=new ir({_chart:t}))},beforeUpdate:function(t){t.tooltip&&t.tooltip.initialize()},reset:function(t){t.tooltip&&t.tooltip.initialize()},afterDraw:function(t){var e=t.tooltip,i={tooltip:e};!1!==kn.notify(t,"beforeTooltipDraw",[i])&&(e.draw(t.ctx),kn.notify(t,"afterTooltipDraw",[i]))},afterEvent:function(t,e,i){if(t.tooltip){var n=i;t.tooltip.handleEvent(e,n)}}}};for(var ar in En.helpers=pe,En._adapters=Rn,En.Animation=me,En.Animator=ge,En.animationService=xe,En.controllers=xi,En.DatasetController=Ce,En.defaults=Ot,En.Element=Fe,En.elements=ri,En.Interaction=Ai,En.layouts=Vi,En.platforms=_n,En.plugins=kn,En.Scale=jn,En.scaleService=Mn,En.Ticks=zn,Object.keys(Sa).forEach((function(t){return En.scaleService.registerScale(Sa[t])})),nr)Object.prototype.hasOwnProperty.call(nr,ar)&&En.plugins.register(nr[ar]);return"undefined"!=typeof window&&(window.Chart=En),En}));