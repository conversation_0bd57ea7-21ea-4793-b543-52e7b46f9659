<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>0717谦友群分流</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        .container {
            /*width: 100%;*/
            max-width: 600px;
            margin: 0 auto;
            text-align: center;
            background-color: white;
            padding: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        .title {
            font-size: 24px;
            margin-bottom: 20px;
        }
        .image-container {
            position: relative;
        }
        .image-container img {
            width: 100%;
            height: auto;
            border-radius: 8px;
            transition: opacity 0.5s ease-in-out; /* 添加过渡效果 */
        }
        .button {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background-color: rgba(0, 0, 0, 0.5);
            color: white;
            border: none;
            font-size: 24px;
            height: 30px;
            width: 30px;
            line-height: 30px;
            cursor: pointer;
            border-radius: 50%;
        }
        .prev {
            left: 10px;
        }
        .next {
            right: 10px;
        }
        .navigation {
            margin-top: 10px;
        }
        .nav-button {
            display: inline-block;
            width: 10px;
            height: 10px;
            margin: 0 5px;
            background-color: #ddd;
            border-radius: 50%;
            cursor: pointer;
        }
        .active {
            background-color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">0717谦友群分流</h1>
        <div class="image-container">
            <img id="image" src="upload/qun/001.png" alt="Image 1">
            <button class="button prev" onclick="changeImage(-1)">&#10094;</button>
            <button class="button next" onclick="changeImage(1)">&#10095;</button>
        </div>
        <div class="navigation">
            <span class="nav-button" onclick="goToImage(0)"></span>
            <!--<span class="nav-button" onclick="goToImage(1)"></span>-->
            <!--<span class="nav-button" onclick="goToImage(2)"></span>-->
            <!--<span class="nav-button" onclick="goToImage(3)"></span>-->
            <!--<span class="nav-button" onclick="goToImage(4)"></span>-->
            <!--<span class="nav-button" onclick="goToImage(5)"></span>-->
            <!--<span class="nav-button" onclick="goToImage(6)"></span>-->
        </div>
        <div style="text-align: left;font-size: 14px;background: #eee;padding: 20px;margin: 10px 0;"> 
        <p>1.由于微信群限制200人之后必须邀请才可以进入，才邀请了几百号谦友就导致微信被封了7天！</p>
        <p>2.目前实行分流大家每个人加一个群就行了，请把多余位置让给其他谦友，后续我会想办法集合到总群，尽量做到每一个群都500人，给大家尽可能的都聚到一起！</p>
        <p>3.但是也希望大家能够自觉遵守群规则，不要广告和私加好友，群内人多会出现骗子和一些心术不正的人，谨防被骗，谨防被骗，谨防被骗</p>
        
        </div>
    </div>

    <script>
        // 图片列表
        const images = [
            // "upload/qun/002.png?v=1.0",
            "upload/qun/001.png?v=4.0",
            // "upload/qun/003.png?v=3.0",
            // "upload/qun/004.png?v=1.0",
            // "upload/qun/005.png?v=1.0",
            // "upload/qun/006.png?v=1.0",
            // "upload/qun/007.png?v=1.0",
        ];
        
        // 预加载所有图片
        const preloadImages = images.map(src => {
            const img = new Image();
            img.src = src;
            return img;
        });

        let currentIndex = 0; // 当前显示的图片索引

        // 更新图片显示
        function updateImage() {
            document.getElementById('image').src = images[currentIndex];
            updateNavButtons();
        }

        // 切换图片
        function changeImage(direction) {
            currentIndex += direction;
            if (currentIndex < 0) {
                currentIndex = images.length - 1; // 如果当前是第一张图片，切换到最后一张
            } else if (currentIndex >= images.length) {
                currentIndex = 0; // 如果当前是最后一张图片，切换到第一张
            }
            updateImage();
        }

        // 跳转到指定的图片
        function goToImage(index) {
            currentIndex = index;
            updateImage();
        }

        // 更新导航按钮的状态
        function updateNavButtons() {
            const navButtons = document.querySelectorAll('.nav-button');
            navButtons.forEach((button, index) => {
                if (index === currentIndex) {
                    button.classList.add('active');
                } else {
                    button.classList.remove('active');
                }
            });
        }

        // 初始化页面
        updateImage();
    </script>
</body>
</html>
