<?php
// 标记通知为已读
$iteace = "1"; // 设置API状态，避免触发"请设置状态"错误

if (is_file("../config.php")) {
    require "../config.php";
} else {
    exit("请先安装程序！<a href=\"../install/\">点击安装</a>");
}
require "../api/wz.php";

// 设置HTTP状态码
http_response_code(200);
header('Content-Type: application/json; charset=utf-8');

// 检查用户是否登录
if ($userdlzt != 1) {
    $arr = [["code" => "201", "msg" => "请先登录！"]];
    exit(json_encode($arr, JSON_UNESCAPED_UNICODE));
}

// 获取通知ID
$notification_id = isset($_GET['id']) ? intval($_GET['id']) : 0;
if ($notification_id <= 0) {
    $arr = [["code" => "201", "msg" => "参数错误"]];
    exit(json_encode($arr, JSON_UNESCAPED_UNICODE));
}

// 检查通知是否属于当前用户
$sql_check = "SELECT id FROM notifications WHERE id = '{$notification_id}' AND user_to = '{$user_zh}'";
$result_check = $conn->query($sql_check);
if (!$result_check || $result_check->num_rows == 0) {
    $arr = [["code" => "201", "msg" => "通知不存在或不属于您"]];
    exit(json_encode($arr, JSON_UNESCAPED_UNICODE));
}

// 标记为已读
$sql_update = "UPDATE notifications SET is_read = 1 WHERE id = '{$notification_id}'";
if ($conn->query($sql_update)) {
    $arr = [["code" => "200", "msg" => "标记成功"]];
} else {
    $arr = [["code" => "201", "msg" => "标记失败：" . $conn->error]];
}

// 返回结果
echo json_encode($arr, JSON_UNESCAPED_UNICODE);
?> 