;jQuery( function() {
    // 关闭页面loading
    setTimeout(function () {
        $('#lyear-preloader').fadeOut(500, function () {
            $('#lyear-preloader').removeClass('loading');
        });
    }, 500);
    
    // 停止
    $("body").on('click','[data-stopPropagation]',function (e) {
        e.stopPropagation();
    });
    
    // 滚动条
    if($('.lyear-scroll')[0]) {
        $('.lyear-scroll').each(function(){
            new PerfectScrollbar(this, {
	        	swipeEasing: false,
	        	suppressScrollX: true
	        });
        });
    }
  
    // 侧边栏
    $(document).on('click', '.lyear-aside-toggler', function() {
        $('.lyear-layout-sidebar').toggleClass('lyear-aside-open');
        $("body").toggleClass('lyear-layout-sidebar-close');
        
        if ($('.lyear-mask-modal').length == 0) {
            $('<div class="lyear-mask-modal"></div>').prependTo('body');
        } else {
            $( '.lyear-mask-modal' ).remove();
        }
    });
    
    // 遮罩层
    $(document).on('click', '.lyear-mask-modal', function() {
        $( this ).remove();
    	$('.lyear-layout-sidebar').toggleClass('lyear-aside-open');
        $('body').toggleClass('lyear-layout-sidebar-close');
    });
    
	// 侧边栏导航
    $(document).on('click', '.nav-item-has-subnav > a', function() {
		$subnavToggle = jQuery( this );
		$navHasSubnav = $subnavToggle.parent();
        $topHasSubNav = $subnavToggle.parents('.nav-item-has-subnav').last();
		$subnav       = $navHasSubnav.find('.nav-subnav').first();
        $viSubHeight  = $navHasSubnav.siblings().find('.nav-subnav:visible').outerHeight();
        $scrollBox    = $('.lyear-layout-sidebar-info');
		$navHasSubnav.siblings().find('.nav-subnav:visible').slideUp(500).parent().removeClass('open');
		$subnav.slideToggle( 300, function() {
			$navHasSubnav.toggleClass( 'open' );
			
			// 新增滚动条处理
			var scrollHeight  = 0;
			    pervTotal     = $topHasSubNav.prevAll().length,
			    boxHeight     = $scrollBox.outerHeight(),
		        innerHeight   = $('.sidebar-main').outerHeight(),
                thisScroll    = $scrollBox.scrollTop(),
                thisSubHeight = $(this).outerHeight(),
                footHeight    = 121;
			
			if (footHeight + innerHeight - boxHeight >= (pervTotal * 48)) {
			    scrollHeight = pervTotal * 48;
			}
            if ($subnavToggle.parents('.nav-item-has-subnav').length == 1) {
                $scrollBox.animate({scrollTop: scrollHeight}, 300);
            } else {
                // 子菜单操作
                if (typeof($viSubHeight) != 'undefined' && $viSubHeight != null) {
                    scrollHeight = thisScroll + thisSubHeight - $viSubHeight;
                    $scrollBox.animate({scrollTop: scrollHeight}, 300);
                } else {
                    if ((thisScroll + boxHeight - $scrollBox[0].scrollHeight) == 0) {
                        scrollHeight = thisScroll - thisSubHeight;
                        $scrollBox.animate({scrollTop: scrollHeight}, 300);
                    }
                }
            }
		});
	});
    
    // 读取cookie中的主题设置
    if ($.isFunction($.cookie)) {
	    var the_logo_bg    = $.cookie('the_logo_bg'),
	        the_header_bg  = $.cookie('the_header_bg'),
	        the_sidebar_bg = $.cookie('the_sidebar_bg'),
	        the_site_theme = $.cookie('the_site_theme');
	    
	    if (the_logo_bg) $('body').attr('data-logobg', the_logo_bg);
	    if (the_header_bg) $('body').attr('data-headerbg', the_header_bg);
	    if (the_sidebar_bg) $('body').attr('data-sidebarbg', the_sidebar_bg);
        if (the_site_theme) $('body').attr('data-theme', the_site_theme);
        
        // 处理主题配色下拉选中
        $(".dropdown-skin :radio").each(function(){
            var $this = $(this),
                radioName = $this.attr('name');
            switch (radioName) {
                case 'site_theme':
                    $this.val() == the_site_theme && $this.prop("checked", true);
                    break;
                case 'logo_bg':
                    $this.val() == the_logo_bg && $this.prop("checked", true);
                    break;
                case 'header_bg':
                    $this.val() == the_header_bg && $this.prop("checked", true);
                    break;
                case 'sidebar_bg':
                    $this.val() == the_sidebar_bg && $this.prop("checked", true);
            }
        });
    }
	// 设置主题配色
	setTheme = function(input_name, data_name) {
	    $("input[name='"+input_name+"']").click(function(){
	        $('body').attr(data_name, $(this).val());
	        $.isFunction($.cookie) && $.cookie('the_'+input_name, $(this).val());
	    });
	}
	setTheme('site_theme', 'data-theme');
	setTheme('sidebar_bg', 'data-sidebarbg');
	setTheme('logo_bg', 'data-logobg');
	setTheme('header_bg', 'data-headerbg');
    
    // 工具提示
	if($('[data-toggle="tooltip"]')[0]) {
		$('[data-toggle="tooltip"]').tooltip({
			"container" : 'body',
		});
	}
    
    // POP弹出框
    if($('[data-toggle="popover"]')[0]) {
        $('[data-toggle="popover"]').popover();
    }
  
    // 关闭卡片
    $(document).on('click', '.card-btn-close', function() {
        $(this).closest('.card').fadeOut(150, function() {
            if ($(this).parent().children().length == 1) {
                $(this).parent().remove();
            } else {
                $(this).remove();
            }
        });
    });
  
    // 卡片收缩与打开
    $(document).on('click', '.card-btn-slide', function(){
        $(this).toggleClass('rotate-180').closest('.card').find('.card-body').slideToggle();
    });
    
    // 颜色选取
	jQuery('.js-colorpicker').each(function() {
		var $colorpicker = jQuery(this);
		var $colorpickerMode = $colorpicker.data('colorpicker-mode') ? $colorpicker.data('colorpicker-mode') : 'auto';
		$colorpicker.colorpicker({
			'format': $colorpickerMode,
		});
	});
    
    // 日期选择器
    jQuery("[data-provide = 'datepicker']").each(function() {
        var options = {
            language: 'zh-CN',  // 默认简体中文
            multidateSeparator: ', ' // 默认多个日期用,分隔
        }
  
        options = $.extend( options, getDataOptions( $(this) ));
  
        if ( $(this).prop("tagName") != 'INPUT' ) {
            options.inputs = [$(this).find('input:first'), $(this).find('input:last')];
        }
  
        $(this).datepicker(options);
    });
    
    // 时间选择器
    jQuery("[data-provide = 'clockpicker']").each(function() {
        $(this).clockpicker({
            donetext: 'Done'
        });
    });
    
    // 时间日期选择器
    jQuery("[data-provide = 'datetimepicker']").each(function() {
        var options = {
            locale: moment.locale(),
        }
  
        options = $.extend( options, getDataOptions( $(this) ));
  
        if ( $(this).prop("tagName") != 'INPUT' ) {
            options.inputs = [$(this).find('input:first'), $(this).find('input:last')];
        }

        $(this).datetimepicker(options);
    });
    
    // 标签
	$('.js-tags-input').each(function() {
        var $this = $(this);
        $this.tagsInput({
			height: $this.data('height') ? $this.data('height') : '36px',
			width: '100%',
			defaultText: $this.attr("placeholder"),
			removeWithBackspace: true,
			delimiter: [',']
		});
    });
  
    // 复选框全选
	$("#check-all").change(function () {
        if ($boxname = $(this).data('name')) {
            $(this).closest('table').find("input[name='" + $boxname + "']").prop('checked', $(this).prop("checked"));
        } else {
            $(this).closest('table').find(".custom-checkbox input[type='checkbox']").prop('checked', $(this).prop("checked"));
        }
	});
});

// 参考国外模板的写法，获取当前的配置，以data-*（*指插件原有的配置名）
getDataOptions = function(el, castList) {
    var options = {};
    
    $.each( $(el).data(), function(key, value){
    
        key = dataToOption(key);
        
        if ( key == 'provide' ) {
            return;
        }
        options[key] = value;
    });
    
    return options;
}

dataToOption = function(name) {
    return name.replace(/-([a-z])/g, function(x){return x[1].toUpperCase();});
}


//获取cookie函数
function getCookie(cookieName) {
    var strCookie = document.cookie;
    var arrCookie = strCookie.split("; ");
    for(var i = 0; i < arrCookie.length; i++){
        var arr = arrCookie[i].split("=");
        if(cookieName == arr[0]){
            return arr[1];
        }
    }
    return "";
}
//退出登录
function logut(){
    var user_id = getCookie("username");//取登录的账号
    var user_passid = getCookie("passid");//取登录的passid唯一id
    if (user_id == "" || user_passid == "") {
        alert("当前没有登录");
        return;
    }
    document.cookie = "username=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
    document.cookie = "passid=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
    window.location.href="./index.php";
}


//更新版本
function upfile(){
    // （1）创建异步对象
        var ajaxObj = new XMLHttpRequest();
        // （2）设置请求的参数。包括：请求的方法、请求的url。
        ajaxObj.open('get', './api/updatever.php?up=up');
        // （3）发送请求
        ajaxObj.send();
        //（4）注册事件。 onreadystatechange事件，状态改变时就会调用。
        //如果要在数据完整请求回来的时候才调用，我们需要手动写一些判断的逻辑。
        ajaxObj.onreadystatechange = function () {
            // 为了保证 数据 完整返回，我们一般会判断 两个值
            if (ajaxObj.readyState == 4 && ajaxObj.status == 200) {
                
                if (ajaxObj.responseText == "") {
                    alert("更新失败,请尝试手动更新");
                    return;
                }
                if (ajaxObj.responseText == "更新完成") {
                    alert(ajaxObj.responseText);
                    location.reload();
                }else{
                    alert(ajaxObj.responseText);
                }
                
            }
        }
}