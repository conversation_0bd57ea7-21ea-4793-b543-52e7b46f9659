<?php

//decode by nige112
if ($_SERVER["REQUEST_METHOD"] === "POST") {
	$arr = [["code" => "201", "msg" => "非法请求"]];
	exit(json_encode($arr, JSON_UNESCAPED_UNICODE));
}
$iteace = "0";
if (is_file("./config.php")) {
	require "./config.php";
} else {
	exit("请先安装程序！<a href=\"./install/\">点击安装</a>");
}
require "./api/wz.php";
?><!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>发现 - <?php echo $name;?></title>
    <!--为搜索引擎定义关键词-->
    <meta name="keywords" content="0717谦友圈、谦友、薛之谦粉丝、薛之谦、兴趣爱好分享、生活分享、在线聊天、谦友群聊、粉丝社交平台">
    <!--为网页定义描述内容 用于告诉搜索引擎，你网站的主要内容-->
    <meta name="description" content="<?php echo $name . "," . $subtitle;?>">
    <meta name="author" content="<?php echo $name;?>">
    <meta name="copyright" content="<?php echo $name;?>">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta http-equiv="cache-control" content="no-cache">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0,minimum-scale=1.0, user-scalable=no minimal-ui">
    <link rel="apple-touch-icon" sizes="57x57" href="<?php echo $icon;?>" /><!-- Standard iPhone -->
    <link rel="apple-touch-icon" sizes="114x114" href="<?php echo $icon;?>" /><!-- Retina iPhone -->
    <link rel="apple-touch-icon" sizes="72x72" href="<?php echo $icon;?>" /><!-- Standard iPad -->
    <link rel="apple-touch-icon" sizes="144x144" href="<?php echo $icon;?>" /><!-- Retina iPad -->
    <link rel="icon" href="<?php echo $icon;?>" type="image/x-icon" />
    <meta name="robots" content="index,follow">
    <meta name="format-detection" content="telephone=no">
    <meta http-equiv="x-rim-auto-match" content="none">
    
    <?php 
if ($rosdomain == 1) {
	?><meta name="referrer" content="no-referrer"><?php 
}
?>
    <link rel="stylesheet" href="<?php echo $iconurl_url;?>">
    <link rel="stylesheet" type="text/css" href="./assets/css/style.css?v=<?php echo $resversion;?>">
    <link rel="stylesheet" type="text/css" href="./assets/css/footer-menu.css?v=<?php echo $resversion;?>">
    <link rel="stylesheet" type="text/css" href="./assets/mesg/dist/css/style.css?v=<?php echo $resversion;?>"><!--引入弹窗对话框-->
    <link rel="stylesheet" type="text/css" href="./assets/css/jquery.fancybox.min.css?v=<?php echo $resversion;?>">
    <!-- 引入更丰富的图标库 -->
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">

    <style>
        /* 全局设置 */
        body, html {
            scroll-behavior: smooth;
        }
        
        /* 发现页头部 */
        .discover-container {
            padding-bottom: 70px;
        }
        
        .sh-main-head {
            height: auto;
            min-height: 40px;
            position: sticky;
            top: 0;
            z-index: 100;
            background-color: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            border-bottom: 1px solid rgba(0,0,0,0.05);
            padding: 5px 0;
        }
        
        .dark-theme .sh-main-head {
            background-color: rgba(30, 30, 30, 0.95);
            border-bottom: 1px solid rgba(255,255,255,0.05);
        }
        
        .discover-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
        }
        
        .discover-title {
            font-size: 24px;
            font-weight: bold;
            background: linear-gradient(135deg, #1e88e5, #42a5f5);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            display: flex;
            align-items: center;
        }
        
        .discover-title i {
            margin-right: 8px;
            font-size: 26px;
            background: linear-gradient(135deg, #1e88e5, #42a5f5);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .discover-actions {
            display: flex;
            align-items: center;
        }
        
        .discover-action-btn {
            width: 38px;
            height: 38px;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #f5f5f5;
            margin-left: 10px;
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            cursor: pointer;
        }
        
        .dark-theme .discover-action-btn {
            background-color: #333;
        }
        
        .discover-action-btn:active {
            transform: scale(0.9);
            background-color: #e8e8e8;
        }
        
        .discover-action-btn i {
            font-size: 20px;
            color: #1e88e5;
        }
        
        /* 搜索栏 */
        .discover-search {
            margin: 5px 15px 15px;
            position: relative;
        }
        
        .discover-search-input {
            width: 100%;
            height: 45px;
            background-color: #f5f5f5;
            border-radius: 12px;
            border: none;
            padding: 0 15px 0 45px;
            font-size: 14px;
            color: #333;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(0,0,0,0.05);
        }
        
        .dark-theme .discover-search-input {
            background-color: #333;
            color: #eee;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        
        .discover-search-input:focus {
            box-shadow: 0 5px 15px rgba(30, 136, 229, 0.15);
            outline: none;
        }
        
        .discover-search-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 18px;
            color: #999;
        }
        
        /* 轮播广告区域 */
        .discover-swiper {
            margin: 0 15px 20px;
            position: relative;
            height: 180px;
            overflow: hidden;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
        }
        
        .discover-swiper-wrapper {
            display: flex;
            height: 100%;
            transition: transform 0.3s ease;
        }
        
        .discover-swiper-slide {
            flex-shrink: 0;
            width: 100%;
            height: 100%;
            position: relative;
            overflow: hidden;
        }
        
        .discover-swiper-img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.5s ease;
        }
        
        .discover-swiper-slide:hover .discover-swiper-img {
            transform: scale(1.05);
        }
        
        .discover-swiper-caption {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 15px;
            background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);
            color: white;
        }
        
        .discover-swiper-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }
        
        .discover-swiper-desc {
            font-size: 12px;
            opacity: 0.9;
        }
        
        .discover-swiper-pagination {
            position: absolute;
            bottom: 15px;
            right: 15px;
            display: flex;
        }
        
        .discover-swiper-bullet {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: rgba(255,255,255,0.5);
            margin: 0 3px;
            transition: all 0.3s ease;
        }
        
        .discover-swiper-bullet.active {
            width: 16px;
            border-radius: 4px;
            background-color: white;
        }
        
        /* 快捷入口 */
        .discover-shortcuts {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin: 0 15px 20px;
        }
        
        .discover-shortcut-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 80px;
            background-color: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }
        
        .dark-theme .discover-shortcut-item {
            background-color: #272727;
        }
        
        .discover-shortcut-item:active {
            transform: scale(0.95);
        }
        
        .discover-shortcut-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            background: linear-gradient(135deg, #1e88e5, #42a5f5);
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 8px;
            box-shadow: 0 3px 8px rgba(30, 136, 229, 0.2);
        }
        
        .discover-shortcut-icon i {
            font-size: 20px;
            color: white;
        }
        
        .discover-shortcut-label {
            font-size: 12px;
            color: #666;
            font-weight: 500;
        }
        
        .dark-theme .discover-shortcut-label {
            color: #bbb;
        }
        
        .discover-section {
            margin: 20px 15px;
            padding: 20px;
            background-color: #fff;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }
        
        .discover-section:active {
            transform: scale(0.98);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .dark-theme .discover-section {
            background-color: #272727;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .discover-section-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .dark-theme .discover-section-title {
            color: #eee;
        }
        
        .discover-section-more {
            font-size: 13px;
            color: #1e88e5;
            display: flex;
            align-items: center;
            transition: transform 0.2s ease;
        }
        
        .discover-section-more:active {
            transform: translateX(3px);
        }
        
        .discover-section-more i {
            margin-left: 3px;
            font-size: 16px;
        }
        
        .discover-user-list {
            display: flex;
            overflow-x: auto;
            gap: 15px;
            padding: 5px 0;
            scrollbar-width: none;
            -ms-overflow-style: none;
            scroll-snap-type: x mandatory;
            margin: 0 -5px;
            padding: 5px;
        }
        
        .discover-user-list::-webkit-scrollbar {
            display: none;
        }
        
        .discover-user-item {
            flex: 0 0 auto;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            width: 70px;
            scroll-snap-align: start;
        }
        
        .discover-user-item:active {
            transform: scale(0.95);
        }
        
        .discover-user-avatar {
            width: 55px;
            height: 55px;
            border-radius: 50%;
            margin: 0 auto 8px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            background-color: #f0f0f0;
            position: relative;
        }
        
        .dark-theme .discover-user-avatar {
            border: 3px solid rgba(40, 40, 40, 0.8);
        }
        
        .discover-user-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }
        
        .discover-user-item:hover .discover-user-avatar img {
            transform: scale(1.1);
        }
        
        .discover-user-online {
            position: absolute;
            bottom: 2px;
            right: 2px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #4cd964;
            border: 2px solid white;
        }
        
        .dark-theme .discover-user-online {
            border: 2px solid #272727;
        }
        
        .discover-user-name {
            font-size: 12px;
            color: #666;
            font-weight: 500;
            margin: 0;
            max-width: 65px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .dark-theme .discover-user-name {
            color: #eee;
        }
        
        .discover-user-follow {
            margin-top: 5px;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            background-color: rgba(30, 136, 229, 0.1);
            color: #1e88e5;
            transition: all 0.3s ease;
        }
        
        .discover-user-follow:active {
            background-color: rgba(30, 136, 229, 0.2);
            transform: scale(0.95);
        }
        
        /* 签到卡片特殊样式 */
        .sign-card {
            background: linear-gradient(135deg, #1e88e5, #42a5f5);
            color: white;
            padding: 20px;
            border-radius: 20px;
            margin: 20px 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 8px 30px rgba(30, 136, 229, 0.25);
            position: relative;
            overflow: hidden;
            transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }
        
        .sign-card:active {
            transform: scale(0.98);
            box-shadow: 0 5px 15px rgba(30, 136, 229, 0.15);
        }
        
        .sign-card:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNTAiIGhlaWdodD0iMTUwIj48Y2lyY2xlIGN4PSIxMCIgY3k9IjEwIiByPSIyIiBmaWxsPSIjZmZmIiBmaWxsLW9wYWNpdHk9IjAuMSIvPjwvc3ZnPg==');
            opacity: 0.2;
            z-index: 0;
        }
        
        .sign-icon {
            width: 50px;
            height: 50px;
            border-radius: 15px;
            background-color: rgba(255,255,255,0.2);
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 15px;
        }
        
        .sign-icon i {
            font-size: 28px;
            color: white;
        }
        
        .sign-content {
            flex: 1;
            position: relative;
            z-index: 1;
        }
        
        .sign-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .sign-text {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .sign-btn {
            background-color: rgba(255,255,255,0.25);
            padding: 10px 20px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            z-index: 1;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255,255,255,0.3);
        }
        
        .sign-btn:active {
            background-color: rgba(255,255,255,0.4);
            transform: scale(0.95);
            box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
        }
        
        /* 新增样式 - 发现页轮播 */
        .discover-banner {
            margin: 15px;
            height: 180px;
            background: linear-gradient(135deg, #1e88e5, #42a5f5);
            border-radius: 15px;
            overflow: hidden;
            position: relative;
            display: flex;
            align-items: center;
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
        }
        
        /* 推荐内容卡片 */
        .discover-card-list {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-top: 15px;
        }
        
        .discover-card {
            background-color: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        
        .dark-theme .discover-card {
            background-color: #2d2d2d;
        }
        
        .discover-card:active {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        
        .discover-card-img {
            height: 120px;
            overflow: hidden;
        }
        
        .discover-card-img img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }
        
        .discover-card:hover .discover-card-img img {
            transform: scale(1.05);
        }
        
        .discover-card-content {
            padding: 12px;
        }
        
        .discover-card-title {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .dark-theme .discover-card-title {
            color: #eee;
        }
        
        .discover-card-info {
            display: flex;
            align-items: center;
            font-size: 12px;
            color: #999;
            white-space: nowrap;
            overflow: hidden;
        }
        
        .discover-card-avatar {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 5px;
            flex-shrink: 0;
        }
        
        .discover-card-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .discover-card-stats {
            margin-left: auto;
            display: flex;
            align-items: center;
            flex-shrink: 0;
        }
        
        .discover-card-stat {
            display: flex;
            align-items: center;
            margin-left: 8px;
        }
        
        /* 底部菜单徽章位置调整 */
        .footer-menu .notification-dot {
            position: absolute;
            top: 5px;
            right: 25%;
            width: 8px;
            height: 8px;
            background-color: #ff3b30;
            border-radius: 50%;
            animation: pulse 1.5s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.2); opacity: 0.8; }
            100% { transform: scale(1); opacity: 1; }
        }
    </style>
    
    <?php echo $scfontzt;?>
    
    <?php 
$folderPath = "./user/bobg/";
if (!file_exists($folderPath)) {
	mkdir($folderPath, 511, true);
}
if (file_exists("./user/bobg/bobg.jpg")) {
	?><!--背景图--><style>body{background-image: url(./user/bobg/bobg.jpg);}</style><?php 
} elseif (file_exists("./user/bobg/bobg.png")) {
	?><!--背景图--><style>body{background-image: url(./user/bobg/bobg.png);}</style><?php 
} elseif (file_exists("./user/bobg/bobg.jpeg")) {
	?><!--背景图--><style>body{background-image: url(./user/bobg/bobg.jpeg);}</style><?php 
} elseif (file_exists("./user/bobg/bobg.gif")) {
	?><!--背景图--><style>body{background-image: url(./user/bobg/bobg.gif);}</style><?php 
}
?>
    <?php echo "<style>" . $filtercucss . "</style>";?>
</head>
<body class="<?php 
if (isset($_COOKIE["dark_theme"])) {
	if ($_COOKIE["dark_theme"] == "dark-theme") {
		echo "dark-theme";
	}
}
echo "\">
    ";
if ($pagepass != "") {
	if (isset($_COOKIE["pagepass"])) {
		if ($_COOKIE["pagepass"] != md5(md5($pagepass))) {
			include "./site/pagepass.php";
		}
	} else {
		include "./site/pagepass.php";
	}
}
?>
    <div class="centent">
        <div class="sh-main">
           
            
            <div class="discover-container">
                <!-- 发现页头部 -->
                <div class="discover-header">
                    <div class="discover-title">
                        <i class="ri-compass-3-line"></i>发现
                    </div>
                    <div class="discover-actions">
                       
                        <div class="discover-action-btn" onclick="location.href='./edit.php'">
                            <i class="ri-add-line"></i>
                        </div>
                    </div>
                </div>
              
                
                <!-- 轮播广告区域 -->
                <div class="discover-swiper" id="discover-swiper">
                    <div class="discover-swiper-wrapper">
                        <div class="discover-swiper-slide">
                            <img src="./assets/img/banner1.jpg" class="discover-swiper-img" alt="轮播图1">
                            <div class="discover-swiper-caption">
                                <div class="discover-swiper-title">每日推荐内容</div>
                                <div class="discover-swiper-desc">发现更多精彩内容和有趣的人</div>
                            </div>
                        </div>
                        <div class="discover-swiper-slide" style="display: none;">
                            <img src="./assets/img/banner2.jpg" class="discover-swiper-img" alt="轮播图2">
                            <div class="discover-swiper-caption">
                                <div class="discover-swiper-title">热门话题讨论</div>
                                <div class="discover-swiper-desc">参与热门话题，分享你的观点</div>
                            </div>
                        </div>
                        <div class="discover-swiper-slide" style="display: none;">
                            <img src="./assets/img/banner3.jpg" class="discover-swiper-img" alt="轮播图3">
                            <div class="discover-swiper-caption">
                                <div class="discover-swiper-title">精选创作者</div>
                                <div class="discover-swiper-desc">关注你喜欢的创作者获取更新</div>
                            </div>
                        </div>
                    </div>
                    <div class="discover-swiper-pagination">
                        <div class="discover-swiper-bullet active"></div>
                        <div class="discover-swiper-bullet"></div>
                        <div class="discover-swiper-bullet"></div>
                    </div>
                </div>
                
                <!-- 快捷入口 -->
                <div class="discover-shortcuts">
                    <div class="discover-shortcut-item" onclick="location.href='./archives.php'">
                        <div class="discover-shortcut-icon">
                            <i class="ri-time-line"></i>
                        </div>
                        <div class="discover-shortcut-label">时间线</div>
                    </div>
                    <div class="discover-shortcut-item" onclick="location.href='./phb.php'">
                        <div class="discover-shortcut-icon">
                            <i class="ri-bar-chart-line"></i>
                        </div>
                        <div class="discover-shortcut-label">排行榜</div>
                    </div>
                    <div class="discover-shortcut-item" onclick="location.href='./emojis.php'">
                        <div class="discover-shortcut-icon">
                            <i class="ri-emotion-laugh-line"></i>
                        </div>
                        <div class="discover-shortcut-label">表情包</div>
                    </div>
                    <div class="discover-shortcut-item" onclick="location.href='./map.php'">
                        <div class="discover-shortcut-icon">
                            <i class="ri-map-pin-line"></i>
                        </div>
                        <div class="discover-shortcut-label">附近</div>
                    </div>
                </div>
                
                <!-- 每日签到卡片 -->
                <div class="sign-card" onclick="location.href='./sign.php'">
                    <div class="sign-icon">
                        <i class="ri-calendar-check-line"></i>
                    </div>
                    <div class="sign-content">
                        <div class="sign-title">每日签到</div>
                        <div class="sign-text">连续签到获得更多积分</div>
                    </div>
                    <div class="sign-btn">去签到</div>
                </div>
                
                <!-- 推荐谦友 -->
                <div class="discover-section">
                    <div class="discover-section-title">
                        <span><i class="ri-user-follow-line"></i> 推荐谦友</span>
                    </div>
                    <div class="discover-user-list">
                        <?php
                        // 随机获取推荐用户
                        $sql_users = "SELECT username, name, img FROM user WHERE username != '' AND ban = '0' ORDER BY RAND() LIMIT 10";
                        $result_users = $conn->query($sql_users);
                        
                        if ($result_users && $result_users->num_rows > 0) {
                            while ($user = $result_users->fetch_assoc()) {
                                // 确保头像URL有效
                                $avatar = !empty($user['img']) ? $user['img'] : './assets/img/default-avatar.png';
                                $name = htmlspecialchars($user['name']);
                                $username_hash = md5(md5($user['username']));
                                
                                echo '<a href="./archives.php?user='.$username_hash.'" class="discover-user-item">';
                                echo '<div class="discover-user-avatar"><img src="'.$avatar.'" alt="'.$name.'" onerror="this.src=\'./assets/img/default-avatar.png\'"></div>';
                                echo '<div class="discover-user-name">'.$name.'</div>';
                                echo '</a>';
                            }
                        } else {
                            echo '<div style="width:100%; text-align:center; padding:20px 0; color:#999;">暂无推荐用户</div>';
                        }
                        ?>
                    </div>
                </div>
                
                <!-- 热门内容 -->
                <div class="discover-section">
                    <div class="discover-section-title">
                        <span><i class="ri-fire-line"></i> 热门内容</span>
                    </div>
                    <div class="discover-card-list">
                        <?php
                        // 获取热门内容
                        $sql = "SELECT e.id, e.cid, e.ptpname, e.ptptext, e.ptpimag, e.ptplx 
                               FROM essay e
                               WHERE e.ptpys = 1 AND e.ptpaud = 1
                               ORDER BY RAND()
                               LIMIT 6";
                        $result = $conn->query($sql);
                        
                        if ($result && $result->num_rows > 0) {
                            while ($row = $result->fetch_assoc()) {
                                $image = '';
                                if ($row['ptplx'] == 'img') {
                                    $images = explode('(+@+)', $row['ptpimag']);
                                    if (isset($images[0]) && !empty($images[0])) {
                                        $image = $images[0];
                                    }
                                }
                                
                                // 安全处理文本内容
                                $post_title = isset($row['ptptext']) ? htmlspecialchars($row['ptptext']) : '';
                                $post_title = (mb_strlen($post_title) > 20) ? mb_substr($post_title, 0, 20, 'UTF-8') . '...' : $post_title;
                                
                                echo '<div class="discover-card" onclick="location.href=\'./view.php?cid=' . $row['cid'] . '\'">';
                                echo '<div class="discover-card-img">';
                                echo '<img src="' . ($image ? $image : './assets/img/default-cover.jpg') . '" alt="内容封面" onerror="this.src=\'./assets/img/default-cover.jpg\'">';
                                echo '</div>';
                                echo '<div class="discover-card-content">';
                                echo '<div class="discover-card-title">' . $post_title . '</div>';
                                echo '<div class="discover-card-info">';
                                echo '<i class="ri-user-line"></i> ' . htmlspecialchars($row['ptpname']);
                                echo '<div class="discover-card-stats">';
                                
                                // 获取实际点赞数
                                $like_count = 0;
                                $comment_count = 0;
                                
                                $sql_likes = "SELECT COUNT(*) as count FROM lcke WHERE lwz = '{$row['cid']}'";
                                $like_result = $conn->query($sql_likes);
                                if ($like_result && $like_row = $like_result->fetch_assoc()) {
                                    $like_count = $like_row['count'];
                                }
                                
                                $sql_comments = "SELECT COUNT(*) as count FROM comm WHERE wzcid = '{$row['cid']}' AND comaud = '1'";
                                $comment_result = $conn->query($sql_comments);
                                if ($comment_result && $comment_row = $comment_result->fetch_assoc()) {
                                    $comment_count = $comment_row['count'];
                                }
                                
                                echo '<div class="discover-card-stat"><i class="ri-heart-line"></i> ' . $like_count . '</div>';
                                echo '<div class="discover-card-stat"><i class="ri-chat-1-line"></i> ' . $comment_count . '</div>';
                                echo '</div>';
                                echo '</div>';
                                echo '</div>';
                                echo '</div>';
                            }
                        } else {
                            echo '<div style="width:100%; text-align:center; padding:20px 0; color:#999;">暂无热门内容</div>';
                        }
                        ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 底部菜单 -->
    <div class="footer-menu">
        <a href="./index.php" class="footer-menu-item">
            <i class="ri-home-4-line"></i>
            <span>首页</span>
        </a>
        <a href="./discover.php" class="footer-menu-item active">
            <i class="ri-compass-3-fill"></i>
            <span>发现</span>
        </a>
        <a href="./notify.php" class="footer-menu-item">
            <i class="ri-notification-2-line"></i>
            <span>通知</span>
            <?php
            // 检查是否有未读通知
            if ($userdlzt == 1) {
                $sql_unread = "SELECT COUNT(*) as count FROM notifications WHERE user_to = '{$user_zh}' AND is_read = 0";
                $result_unread = $conn->query($sql_unread);
                $unread_count = 0;
                
                if ($result_unread && $row_unread = $result_unread->fetch_assoc()) {
                    $unread_count = $row_unread['count'];
                }
                
                if ($unread_count > 0) {
                    echo '<div class="notification-dot"></div>';
                }
            }
            ?>
        </a>
        <a href="./home.php" class="footer-menu-item">
            <i class="ri-user-3-line"></i>
            <span>我的</span>
        </a>
    </div>
    
<script charset="UTF-8" id="LA_COLLECT" src="//sdk.51.la/js-sdk-pro.min.js"></script>
<script>LA.init({id:"KbK2AGjkzWGBfylr",ck:"KbK2AGjkzWGBfylr"})</script>
<script type="text/javascript" src="./assets/js/jquery.min.js"></script>
<script type="text/javascript">
    // 添加滚动监听，实现导航背景透明度变化
    window.addEventListener('scroll', function() {
        var header = document.querySelector('.sh-main-head');
        var scrollPosition = window.scrollY;
        
        if (scrollPosition > 30) {
            header.style.backgroundColor = 'var(--background)';
            header.style.boxShadow = '0 2px 10px rgba(0,0,0,0.05)';
        } else {
            header.style.backgroundColor = 'transparent';
            header.style.boxShadow = 'none';
        }
    });
    
    // 轮播图功能
    document.addEventListener('DOMContentLoaded', function() {
        var swiper = document.getElementById('discover-swiper');
        var slides = swiper.querySelectorAll('.discover-swiper-slide');
        var bullets = swiper.querySelectorAll('.discover-swiper-bullet');
        var currentIndex = 0;
        var totalSlides = slides.length;
        
        function showSlide(index) {
            slides.forEach(function(slide, i) {
                slide.style.display = i === index ? 'block' : 'none';
            });
            
            bullets.forEach(function(bullet, i) {
                bullet.classList.toggle('active', i === index);
            });
        }
        
        function nextSlide() {
            currentIndex = (currentIndex + 1) % totalSlides;
            showSlide(currentIndex);
        }
        
        // 设置定时器，自动切换轮播图
        var slideInterval = setInterval(nextSlide, 5000);
        
        // 点击轮播指示器切换图片
        bullets.forEach(function(bullet, i) {
            bullet.addEventListener('click', function() {
                clearInterval(slideInterval);
                currentIndex = i;
                showSlide(currentIndex);
                slideInterval = setInterval(nextSlide, 5000);
            });
        });
        
        // 添加触摸滑动支持
        var startX = 0;
        swiper.addEventListener('touchstart', function(e) {
            startX = e.touches[0].clientX;
        });
        
        swiper.addEventListener('touchend', function(e) {
            var endX = e.changedTouches[0].clientX;
            var diffX = endX - startX;
            
            if (Math.abs(diffX) > 50) { // 滑动距离超过50px才切换
                clearInterval(slideInterval);
                if (diffX > 0) {
                    // 向右滑，上一张
                    currentIndex = (currentIndex - 1 + totalSlides) % totalSlides;
                } else {
                    // 向左滑，下一张
                    currentIndex = (currentIndex + 1) % totalSlides;
                }
                showSlide(currentIndex);
                slideInterval = setInterval(nextSlide, 5000);
            }
        });
    });
</script>
<?php echo "<script type=\"text/javascript\">" . $filtercujs . "</script>";?></body>
</html> 