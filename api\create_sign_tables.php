<?php
// 创建签到相关表
$iteace = "1"; // 设置API状态，避免触发"请设置状态"错误

header('Content-Type: application/json; charset=utf-8');

// 引入配置文件
if (is_file("../config.php")) {
    require "../config.php";
} else {
    exit(json_encode(["code" => 500, "msg" => "配置文件不存在"]));
}

// 检查连接
if ($conn->connect_error) {
    exit(json_encode(["code" => 500, "msg" => "数据库连接失败: " . $conn->connect_error]));
}

try {
    // 检查user_sign表是否存在
    $sql_check_sign = "SHOW TABLES LIKE 'user_sign'";
    $result_check_sign = $conn->query($sql_check_sign);
    $user_sign_exists = ($result_check_sign && $result_check_sign->num_rows > 0);

    if (!$user_sign_exists) {
        // 创建user_sign表
        $sql_create_sign = "CREATE TABLE user_sign (
            id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(255) NOT NULL,
            sign_date DATETIME NOT NULL,
            jifen INT(11) NOT NULL DEFAULT '0',
            created_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
            KEY idx_username (username),
            KEY idx_sign_date (sign_date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC";
        
        if (!$conn->query($sql_create_sign)) {
            throw new Exception("创建user_sign表失败: " . $conn->error);
        }
    }

    // 检查user_sign_stats表是否存在
    $sql_check_stats = "SHOW TABLES LIKE 'user_sign_stats'";
    $result_check_stats = $conn->query($sql_check_stats);
    $user_sign_stats_exists = ($result_check_stats && $result_check_stats->num_rows > 0);

    if (!$user_sign_stats_exists) {
        // 创建user_sign_stats表
        $sql_create_stats = "CREATE TABLE user_sign_stats (
            id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(255) NOT NULL,
            continuous_days INT(11) NOT NULL DEFAULT '1',
            last_sign_date DATE NOT NULL,
            created_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY idx_username (username)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC";
        
        if (!$conn->query($sql_create_stats)) {
            throw new Exception("创建user_sign_stats表失败: " . $conn->error);
        }
    }

    // 成功响应
    exit(json_encode([
        "code" => 200,
        "msg" => "签到表结构检查完成",
        "data" => [
            "user_sign_exists" => $user_sign_exists,
            "user_sign_stats_exists" => $user_sign_stats_exists
        ]
    ]));

} catch (Exception $e) {
    exit(json_encode(["code" => 500, "msg" => "表结构检查失败: " . $e->getMessage()]));
} 