<?php
// 表情包上传API
$iteace = "1"; // 设置API状态，避免触发"请设置状态"错误

header('Content-Type: application/json; charset=utf-8');

// 引入配置文件
if (is_file("../config.php")) {
    require "../config.php";
} else {
    exit(json_encode(["code" => 500, "msg" => "配置文件不存在"]));
}

// 引入用户验证
require "../api/wz.php";

// 检查用户是否登录
if ($userdlzt != 1) {
    exit(json_encode(["code" => 401, "msg" => "请先登录"]));
}

// 检查请求方法
if ($_SERVER["REQUEST_METHOD"] !== "POST") {
    exit(json_encode(["code" => 400, "msg" => "请求方法不正确"]));
}

// 检查是否有文件上传
if (!isset($_FILES['emoji_file']) || $_FILES['emoji_file']['error'] != 0) {
    exit(json_encode(["code" => 400, "msg" => "请选择要上传的表情包文件"]));
}

// 获取表情包名称
$emoji_name = isset($_POST['emoji_name']) ? trim($_POST['emoji_name']) : '';
if (empty($emoji_name)) {
    $emoji_name = pathinfo($_FILES['emoji_file']['name'], PATHINFO_FILENAME); // 如果没有提供名称，使用文件名
}

// 获取用户ID
$user_id = null;
$sql_user = "SELECT id FROM user WHERE username = ?";
$stmt_user = $conn->prepare($sql_user);
$stmt_user->bind_param("s", $user_zh);
$stmt_user->execute();
$result_user = $stmt_user->get_result();

if ($result_user && $row = $result_user->fetch_assoc()) {
    $user_id = $row['id'];
} else {
    exit(json_encode(["code" => 500, "msg" => "无法获取用户ID"]));
}

// 检查文件类型
$allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
$file_type = $_FILES['emoji_file']['type'];

if (!in_array($file_type, $allowed_types)) {
    exit(json_encode(["code" => 400, "msg" => "只支持JPG、PNG、GIF和WEBP格式的图片"]));
}

// 检查文件大小（最大2MB）
$max_size = 2 * 1024 * 1024;
if ($_FILES['emoji_file']['size'] > $max_size) {
    exit(json_encode(["code" => 400, "msg" => "文件大小不能超过2MB"]));
}

// 创建表情包存储目录
$upload_dir = '../upload/emojis/';
if (!file_exists($upload_dir)) {
    mkdir($upload_dir, 0755, true);
}

// 生成唯一文件名
$extension = pathinfo($_FILES['emoji_file']['name'], PATHINFO_EXTENSION);
$filename = uniqid('emoji_') . '.' . $extension;
$upload_path = $upload_dir . $filename;

// 上传文件
if (move_uploaded_file($_FILES['emoji_file']['tmp_name'], $upload_path)) {
    // 检查emojis表是否存在
    $sql_check_table = "SHOW TABLES LIKE 'emojis'";
    $result_check_table = $conn->query($sql_check_table);
    
    if (!$result_check_table || $result_check_table->num_rows == 0) {
        // 创建emojis表
        $sql_create_table = "CREATE TABLE IF NOT EXISTS `emojis` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `user_id` int(11) NOT NULL COMMENT '上传用户ID',
            `username` varchar(255) NOT NULL COMMENT '上传用户名',
            `name` varchar(255) NOT NULL COMMENT '表情包名称',
            `file_path` varchar(255) NOT NULL COMMENT '表情包文件路径',
            `tags` text COMMENT '表情包标签，多个用逗号分隔',
            `used_count` int(11) NOT NULL DEFAULT '0' COMMENT '使用次数',
            `download_count` int(11) NOT NULL DEFAULT '0' COMMENT '下载次数',
            `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1=正常，0=已禁用',
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
            `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            PRIMARY KEY (`id`),
            KEY `idx_user` (`user_id`),
            KEY `idx_status` (`status`),
            KEY `idx_created` (`created_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='表情包表' ROW_FORMAT=DYNAMIC";
        
        $conn->query($sql_create_table);
    }
    
    // 检查emoji_downloads表是否存在
    $sql_check_downloads = "SHOW TABLES LIKE 'emoji_downloads'";
    $result_check_downloads = $conn->query($sql_check_downloads);
    
    if (!$result_check_downloads || $result_check_downloads->num_rows == 0) {
        // 创建emoji_downloads表
        $sql_create_downloads = "CREATE TABLE IF NOT EXISTS `emoji_downloads` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `user_id` int(11) NOT NULL COMMENT '下载用户ID',
            `emoji_id` int(11) NOT NULL COMMENT '表情包ID',
            `download_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '下载时间',
            PRIMARY KEY (`id`),
            UNIQUE KEY `user_emoji` (`user_id`,`emoji_id`),
            KEY `idx_user` (`user_id`),
            KEY `idx_emoji` (`emoji_id`),
            KEY `idx_time` (`download_time`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='表情包下载记录表' ROW_FORMAT=DYNAMIC";
        
        $conn->query($sql_create_downloads);
    }
    
    // 将表情包信息保存到数据库
    $file_path = 'upload/emojis/' . $filename;
    $sql_insert = "INSERT INTO emojis (user_id, username, name, file_path) VALUES (?, ?, ?, ?)";
    
    $stmt = $conn->prepare($sql_insert);
    $stmt->bind_param("isss", $user_id, $user_zh, $emoji_name, $file_path);
    
    if ($stmt->execute()) {
        exit(json_encode([
            "code" => 200, 
            "msg" => "表情包上传成功",
            "data" => [
                "id" => $stmt->insert_id,
                "name" => $emoji_name,
                "file_path" => $file_path
            ]
        ]));
    } else {
        unlink($upload_path); // 删除已上传的文件
        exit(json_encode(["code" => 500, "msg" => "保存表情包信息失败: " . $stmt->error]));
    }
} else {
    exit(json_encode(["code" => 500, "msg" => "上传文件失败，请稍后再试"]));
} 