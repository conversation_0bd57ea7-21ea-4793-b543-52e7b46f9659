<?php

// 引入数据库连接配置文件，假设你的数据库连接配置在config.php文件中
include 'config.php';

// 建立数据库连接
$conn = new mysqli($servername, $username, $password, $dbname);
if ($conn->connect_error) {
    die("连接数据库失败: ". $conn->connect_error);
}

// 查询iszhiding为1的所有文章数据
$sql = "SELECT * FROM essay WHERE iszhiding = 1";
$result = $conn->query($sql);

if ($result && $result->num_rows > 0) {
    // 获取当前时间的时间戳
    $currentTime = strtotime(date('Y-m-d H:i:s'));

    while ($row = $result->fetch_assoc()) {
        // 将文章的置顶时间字符串转换为时间戳
        $zhidingTime = strtotime($row['zhidingtime']);
// print_r($row);die;
        // 假设置顶有效期为24小时，你可以根据实际需求修改这个时间
        $validityPeriodInSeconds = 24 * 60 * 60;

        // 判断置顶时间是否过期
        if ($currentTime - $zhidingTime > $validityPeriodInSeconds) {
            // 如果过期，更新文章的iszhiding为0，zhidingtime为空
            $updateSql = "UPDATE essay SET iszhiding = 0, zhidingtime = '' WHERE id = ". $row['id'];
            $updateResult = $conn->query($updateSql);

            if ($updateResult) {
                echo "文章ID为 ". $row['id']. " 的置顶状态已更新为非置顶，置顶时间已清空。<br>";
            } else {
                echo "更新文章ID为 ". $row['id']. " 的置顶状态和置顶时间时出错: ". $conn->error. "<br>";
            }
        }else{
            echo "文章ID为 ". $row['id']. " 未过期。<br>";
        }
    }
} else {
    echo "没有找到iszhiding为1的文章。<br>";
}

// 关闭数据库连接
$conn->close();