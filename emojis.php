<?php
if ($_SERVER["REQUEST_METHOD"] === "POST") {
    $arr = [["code" => "201", "msg" => "非法请求"]];
    exit(json_encode($arr, JSON_UNESCAPED_UNICODE));
}
$iteace = "0";
if (is_file("./config.php")) {
    require "./config.php";
} else {
    exit("请先安装程序！<a href=\"./install/\">点击安装</a>");
}
require "./api/wz.php";
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>表情包广场 - <?php echo $name; ?></title>
    <!--为搜索引擎定义关键词-->
    <meta name="keywords" content="表情包,<?php echo $name; ?>,表情,emoji">
    <!--为网页定义描述内容 用于告诉搜索引擎，你网站的主要内容-->
    <meta name="description" content="<?php echo $name . "," . $subtitle; ?> - 表情包广场，分享你的快乐">
    <meta name="author" content="<?php echo $name; ?>">
    <meta name="copyright" content="<?php echo $name; ?>">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta http-equiv="cache-control" content="no-cache">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0,minimum-scale=1.0, user-scalable=no minimal-ui">
    <link rel="apple-touch-icon" sizes="57x57" href="<?php echo $icon; ?>" /><!-- Standard iPhone -->
    <link rel="apple-touch-icon" sizes="114x114" href="<?php echo $icon; ?>" /><!-- Retina iPhone -->
    <link rel="apple-touch-icon" sizes="72x72" href="<?php echo $icon; ?>" /><!-- Standard iPad -->
    <link rel="apple-touch-icon" sizes="144x144" href="<?php echo $icon; ?>" /><!-- Retina iPad -->
    <link rel="icon" href="<?php echo $icon; ?>" type="image/x-icon" />
    <meta name="robots" content="index,follow">
    <meta name="format-detection" content="telephone=no">
    <meta http-equiv="x-rim-auto-match" content="none">
    <!-- 引入更丰富的图标库 -->
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <link rel="stylesheet" href="<?php echo $iconurl_url;?>">
    <link rel="stylesheet" type="text/css" href="./assets/css/style.css?v=<?php echo $resversion;?>">
    <link rel="stylesheet" type="text/css" href="./assets/css/footer-menu.css?v=<?php echo $resversion;?>">
    <link rel="stylesheet" type="text/css" href="./assets/mesg/dist/css/style.css?v=<?php echo $resversion;?>">
    <link rel="stylesheet" type="text/css" href="./assets/css/jquery.fancybox.min.css?v=<?php echo $resversion;?>">
    <script type="text/javascript" src="./assets/js/jquery.min.js"></script>
    <script type="text/javascript" src="./assets/js/jquery.fancybox.min.js?v=<?php echo $resversion;?>"></script>
    <?php echo $scfontzt;?>
    <style>
body {
    font-family: Arial, sans-serif;
    background-color: #f4f4f4;
    margin: 0;
    scroll-behavior: smooth;
}

/* 移除sh-main-head的高度限制 */
.sh-main-head {
    height: auto;
    min-height: 40px;
    position: sticky;
    top: 0;
    z-index: 100;
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(0,0,0,0.05);
    padding: 5px 0;
}

.dark-theme .sh-main-head {
    background-color: rgba(30, 30, 30, 0.95);
    border-bottom: 1px solid rgba(255,255,255,0.05);
}

.emoji-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
}

.emoji-page-title {
    font-size: 24px;
    font-weight: bold;
    background: linear-gradient(135deg, #1e88e5, #42a5f5);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    display: flex;
    align-items: center;
}

.emoji-page-title i {
    margin-right: 8px;
    font-size: 26px;
    background: linear-gradient(135deg, #1e88e5, #42a5f5);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.emoji-upload-btn {
    background: linear-gradient(135deg, #1e88e5, #42a5f5);
    color: white;
    border: none;
    border-radius: 50px;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    cursor: pointer;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.emoji-upload-btn:active {
    transform: scale(0.95);
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.emoji-upload-btn i {
    margin-right: 5px;
    font-size: 18px;
}

.search-container {
    padding: 0 15px 15px;
}

.search-box {
    display: flex;
    background-color: white;
    border-radius: 50px;
    overflow: hidden;
    box-shadow: 0 3px 10px rgba(0,0,0,0.05);
    padding: 10px 15px;
    transition: all 0.3s ease;
}

.dark-theme .search-box {
    background-color: #272727;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

.search-box input {
    flex: 1;
    border: none;
    outline: none;
    background: transparent;
    color: #333;
    font-size: 14px;
}

.dark-theme .search-box input {
    color: #eee;
}

.search-box input::placeholder {
    color: #999;
}

.dark-theme .search-box input::placeholder {
    color: #777;
}

.search-box button {
    background: transparent;
    border: none;
    outline: none;
    color: #1e88e5;
    cursor: pointer;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
}

.emoji-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    padding: 0 15px 15px;
}

@media (min-width: 480px) {
    .emoji-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (min-width: 768px) {
    .emoji-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

.emoji-card {
    background-color: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
    position: relative;
}

.dark-theme .emoji-card {
    background-color: #272727;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.emoji-card:active {
    transform: scale(0.98);
    box-shadow: 0 3px 10px rgba(0,0,0,0.05);
}

.emoji-img-container {
    position: relative;
    padding-top: 100%; /* 1:1 Aspect Ratio */
    overflow: hidden;
    background-color: #f0f0f0;
}

.dark-theme .emoji-img-container {
    background-color: #333;
}

.emoji-img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover; /* 改为cover确保填满容器 */
    padding: 5px;
    box-sizing: border-box;
}

.emoji-info {
    padding: 10px;
    display: flex;
    flex-direction: column;
}

.emoji-name {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.dark-theme .emoji-name {
    color: #eee;
}

.emoji-stats {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 12px;
    color: #666;
}

.dark-theme .emoji-stats {
    color: #aaa;
}

.emoji-stats-left {
    display: flex;
    align-items: center;
}

.emoji-stats-left img {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    margin-right: 5px;
    object-fit: cover;
}

.emoji-uploader {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 80px;
}

.emoji-download-count {
    display: flex;
    align-items: center;
}

.emoji-download-count i {
    margin-right: 3px;
    font-size: 14px;
    color: #1e88e5;
}

.dark-theme .emoji-download-count i {
    color: #42a5f5;
}

.no-data {
    padding: 40px 15px;
    text-align: center;
    color: #999;
    font-size: 16px;
}

.dark-theme .no-data {
    color: #777;
}

.no-data i {
    font-size: 48px;
    margin-bottom: 10px;
    display: block;
    color: #ccc;
}

.dark-theme .no-data i {
    color: #555;
}

.emoji-upload-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background-color: white;
    border-radius: 20px;
    padding: 20px;
    width: 90%;
    max-width: 400px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    position: relative;
}

.dark-theme .modal-content {
    background-color: #272727;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.modal-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.dark-theme .modal-title {
    color: #eee;
}

.modal-close {
    background: transparent;
    border: none;
    color: #999;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.dark-theme .modal-close {
    color: #777;
}

.upload-form {
    display: flex;
    flex-direction: column;
}

.upload-area {
    background-color: #f0f0f0;
    border: 2px dashed #ccc;
    border-radius: 10px;
    padding: 30px 20px;
    text-align: center;
    margin-bottom: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.dark-theme .upload-area {
    background-color: #333;
    border-color: #555;
}

.upload-area:hover {
    border-color: #1e88e5;
}

.upload-area i {
    font-size: 36px;
    color: #999;
    margin-bottom: 10px;
}

.dark-theme .upload-area i {
    color: #777;
}

.upload-text {
    font-size: 14px;
    color: #666;
}

.dark-theme .upload-text {
    color: #aaa;
}

.form-group {
    margin-bottom: 15px;
}

.form-label {
    display: block;
    margin-bottom: 5px;
    font-size: 14px;
    font-weight: 500;
    color: #333;
}

.dark-theme .form-label {
    color: #eee;
}

.form-control {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 14px;
    color: #333;
    background-color: white;
    box-sizing: border-box;
}

.dark-theme .form-control {
    background-color: #333;
    border-color: #444;
    color: #eee;
}

.form-control:focus {
    border-color: #1e88e5;
    outline: none;
}

.upload-btn {
    background: linear-gradient(135deg, #1e88e5, #42a5f5);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 15px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.upload-btn:active {
    transform: scale(0.98);
}

.preview-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 15px;
}

.preview-item {
    position: relative;
    width: 60px;
    height: 60px;
    border-radius: 8px;
    overflow: hidden;
    background-color: #f0f0f0;
    box-shadow: 0 3px 10px rgba(0,0,0,0.05);
}

.dark-theme .preview-item {
    background-color: #333;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

.preview-img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.preview-remove {
    position: absolute;
    top: -5px;
    right: -5px;
    width: 20px;
    height: 20px;
    background-color: rgba(255,255,255,0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ff4d4f;
    font-size: 12px;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.dark-theme .preview-remove {
    background-color: rgba(40,40,40,0.9);
}

.emoji-viewer-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.9);
    z-index: 1000;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}

.viewer-content {
    position: relative;
    max-width: 90%;
    max-height: 70vh;
}

.viewer-img {
    max-width: 100%;
    max-height: 70vh;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.viewer-actions {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}

.viewer-btn {
    background: rgba(255,255,255,0.2);
    color: white;
    border: none;
    border-radius: 50px;
    padding: 10px 20px;
    margin: 0 10px;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    cursor: pointer;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
}

.viewer-btn:active {
    transform: scale(0.95);
}

.viewer-btn i {
    margin-right: 8px;
    font-size: 18px;
}

.viewer-close {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    background: rgba(0,0,0,0.5);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    cursor: pointer;
    box-shadow: 0 3px 10px rgba(0,0,0,0.2);
    z-index: 1001;
}

.viewer-info {
    position: absolute;
    bottom: 20px;
    left: 20px;
    background: rgba(0,0,0,0.5);
    color: white;
    padding: 8px 15px;
    border-radius: 50px;
    font-size: 14px;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow: 0 3px 10px rgba(0,0,0,0.2);
    z-index: 1001;
}

.emoji-reward-info {
    background-color: white;
    border-radius: 15px;
    padding: 20px;
    margin: 0 15px 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.05);
}

.dark-theme .emoji-reward-info {
    background-color: #272727;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.reward-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
}

.dark-theme .reward-title {
    color: #eee;
}

.reward-title i {
    margin-right: 8px;
    font-size: 20px;
    color: #1e88e5;
}

.dark-theme .reward-title i {
    color: #42a5f5;
}

.reward-text {
    font-size: 14px;
    color: #666;
    line-height: 1.5;
}

.dark-theme .reward-text {
    color: #aaa;
}

.reward-highlight {
    color: #1e88e5;
    font-weight: 500;
}

.dark-theme .reward-highlight {
    color: #42a5f5;
}

.loader {
    display: none;
    width: 40px;
    height: 40px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #1e88e5;
    border-radius: 50%;
    margin: 20px auto;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.load-more {
    background: linear-gradient(135deg, #1e88e5, #42a5f5);
    color: white;
    border: none;
    border-radius: 50px;
    padding: 10px 20px;
    margin: 0 auto 20px;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    width: 120px;
    box-shadow: 0 5px 15px rgba(30,136,229,0.2);
    transition: all 0.3s ease;
}

.load-more:active {
    transform: scale(0.95);
    box-shadow: 0 3px 10px rgba(30,136,229,0.2);
}

.load-more i {
    margin-right: 8px;
    font-size: 16px;
}

/* 阻止右键菜单 */
.emoji-img-container {
    pointer-events: none;
}

.emoji-card {
    pointer-events: auto;
}

.emoji-viewer-modal {
    pointer-events: auto;
}

.viewer-img {
    pointer-events: none;
}

/* 分页容器 */
.pagination {
    display: flex;
    justify-content: center;
    margin: 20px 0;
}

.page-item {
    background: white;
    border-radius: 8px;
    margin: 0 5px;
    overflow: hidden;
    box-shadow: 0 3px 10px rgba(0,0,0,0.05);
}

.dark-theme .page-item {
    background: #272727;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

.page-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    color: #666;
    font-size: 14px;
    text-decoration: none;
}

.dark-theme .page-link {
    color: #aaa;
}

.page-item.active .page-link {
    background: linear-gradient(135deg, #1e88e5, #42a5f5);
    color: white;
}

.page-item.disabled .page-link {
    color: #ccc;
    pointer-events: none;
}

.dark-theme .page-item.disabled .page-link {
    color: #555;
}
    </style>
</head>
<body class="<?php 
if (isset($_COOKIE["dark_theme"])) {
    if ($_COOKIE["dark_theme"] == "dark-theme") {
        echo "dark-theme";
    }
}
?>">
    <!--网页主体框架-->
    <div class="centent">
        <!-- 页面载体 -->
        <div class="sh-main setup-main">
            
            <!-- 表情包页面标题 -->
            <div class="emoji-header">
                <div class="emoji-page-title">
                    <i class="ri-emotion-laugh-line"></i>表情广场
                </div>
                <button class="emoji-upload-btn" id="openUploadBtn">
                    <i class="ri-upload-2-line"></i>上传表情
                </button>
            </div>
            
            <!-- 搜索框 -->
            <div class="search-container">
                <div class="search-box">
                    <input type="text" id="searchInput" placeholder="搜索表情包...">
                    <button id="searchBtn"><i class="ri-search-line"></i></button>
                </div>
            </div>
            
            <!-- 奖励规则说明 -->
            <div class="emoji-reward-info">
                <div class="reward-title"><i class="ri-gift-2-line"></i>奖励规则</div>
                <div class="reward-text">
                    上传高质量表情包，每被下载一次，您将获得 <span class="reward-highlight">1积分</span> 奖励！
                    积分可用于朋友圈置顶、特权兑换等。分享快乐，收获奖励！
                </div>
            </div>
            
            <!-- 表情包列表（瀑布流） -->
            <div class="emoji-grid" id="emojiGrid">
                <!-- 表情包卡片将通过JS动态加载 -->
            </div>
            
            <!-- 加载中图标 -->
            <div class="loader" id="loader"></div>
            
            <!-- 加载更多按钮 -->
            <button class="load-more" id="loadMoreBtn" style="display: none;">
                <i class="ri-refresh-line"></i>加载更多
            </button>
            
            <!-- 分页控件 -->
            <div class="pagination" id="pagination"></div>
            
            <!-- 无数据提示 -->
            <div class="no-data" id="noData" style="display: none;">
                <i class="ri-emotion-sad-line"></i>
                暂无表情包，快来上传分享吧！
            </div>
        </div>
    </div>
    
    <!-- 底部填充，避免内容被底部菜单遮挡 -->
    <div class="footer-padding"></div>
    
    <!-- 底部菜单 -->
    <div class="footer-menu">
        <a href="./index.php" class="footer-menu-item">
            <i class="ri-home-4-line"></i>
            <span>首页</span>
        </a>
        <a href="./discover.php" class="footer-menu-item active">
            <i class="ri-compass-3-fill"></i>
            <span>发现</span>
        </a>
        <a href="./notify.php" class="footer-menu-item">
            <i class="ri-notification-2-line"></i>
            <span>通知</span>
            <?php
            // 检查是否有未读通知
            if ($userdlzt == 1) {
                $sql_unread = "SELECT COUNT(*) as count FROM notifications WHERE user_to = '{$user_zh}' AND is_read = 0";
                $result_unread = $conn->query($sql_unread);
                $unread_count = 0;
                
                if ($result_unread && $row_unread = $result_unread->fetch_assoc()) {
                    $unread_count = $row_unread['count'];
                }
                
                if ($unread_count > 0) {
                    echo '<div class="notification-dot"></div>';
                }
            }
            ?>
        </a>
        <a href="./home.php" class="footer-menu-item">
            <i class="ri-user-3-line"></i>
            <span>我的</span>
        </a>
    </div>
    
    <!-- 上传表情包弹窗 -->
    <div class="emoji-upload-modal" id="uploadModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">上传表情包</h3>
                <button class="modal-close" id="closeUploadBtn">&times;</button>
            </div>
            <form id="uploadForm" class="upload-form">
                <div class="upload-area" id="dropArea">
                    <input type="file" id="fileInput" accept="image/jpeg,image/png,image/gif,image/webp" multiple style="display: none;">
                    <i class="ri-image-add-line"></i>
                    <div class="upload-text">点击或拖拽图片到此处上传<br><small>(支持JPG、PNG、GIF、WEBP，单个不超过2MB)</small></div>
                </div>
                
                <div class="preview-container" id="previewContainer"></div>
                
                <div class="form-group">
                    <label for="emojiName" class="form-label">表情包名称</label>
                    <input type="text" id="emojiName" class="form-control" placeholder="给表情包起个名字吧">
                </div>
                
                <button type="submit" class="upload-btn" id="submitUploadBtn">上传表情包</button>
            </form>
        </div>
    </div>
    
    <!-- 查看表情包弹窗 -->
    <div class="emoji-viewer-modal" id="viewerModal">
        <button class="viewer-close" id="closeViewerBtn">&times;</button>
        <div class="viewer-content">
            <img src="" alt="表情包" class="viewer-img" id="viewerImg">
            <div class="viewer-info" id="viewerInfo"></div>
        </div>
        <div class="viewer-actions">
            <button class="viewer-btn" id="downloadBtn">
                <i class="ri-download-2-line"></i>下载
            </button>
        </div>
    </div>
    
    <!-- 弹窗提示 -->
    <script type="text/javascript" src="./assets/mesg/dist/js/sh-noytf.js?v=<?php echo $resversion;?>"></script>
    
    <script>
    // 禁用右键菜单
    document.addEventListener('contextmenu', function(e) {
        e.preventDefault();
        return false;
    });
    
    // 禁用F12和开发者工具快捷键
    document.addEventListener('keydown', function(e) {
        // 禁用F12
        if (e.keyCode === 123) {
            e.preventDefault();
            return false;
        }
        
        // 禁用Ctrl+Shift+I, Ctrl+Shift+J, Ctrl+Shift+C, Ctrl+U, Ctrl+S等
        if ((e.ctrlKey && e.shiftKey && (e.keyCode === 73 || e.keyCode === 74 || e.keyCode === 67)) ||
            (e.ctrlKey && (e.keyCode === 85 || e.keyCode === 83))) {
            e.preventDefault();
            return false;
        }
    });
    
    // 禁用长按操作(移动设备)
    document.addEventListener('touchstart', function(e) {
        if (e.touches.length > 1) {
            e.preventDefault();
        }
    }, { passive: false });
    
    let lastTouchEnd = 0;
    document.addEventListener('touchend', function(e) {
        const now = new Date().getTime();
        if (now - lastTouchEnd <= 300) {
            e.preventDefault();
        }
        lastTouchEnd = now;
    }, { passive: false });
    
    // 阻止图片拖拽和保存
    document.addEventListener('dragstart', function(e) {
        e.preventDefault();
    });
    
    // 页面加载完成后执行
    document.addEventListener('DOMContentLoaded', function() {
        const emojiGrid = document.getElementById('emojiGrid');
        const searchInput = document.getElementById('searchInput');
        const searchBtn = document.getElementById('searchBtn');
        const loader = document.getElementById('loader');
        const loadMoreBtn = document.getElementById('loadMoreBtn');
        const noData = document.getElementById('noData');
        const pagination = document.getElementById('pagination');
        
        // 上传弹窗相关
        const uploadModal = document.getElementById('uploadModal');
        const openUploadBtn = document.getElementById('openUploadBtn');
        const closeUploadBtn = document.getElementById('closeUploadBtn');
        const dropArea = document.getElementById('dropArea');
        const fileInput = document.getElementById('fileInput');
        const previewContainer = document.getElementById('previewContainer');
        const uploadForm = document.getElementById('uploadForm');
        const emojiName = document.getElementById('emojiName');
        
        // 查看表情包弹窗相关
        const viewerModal = document.getElementById('viewerModal');
        const closeViewerBtn = document.getElementById('closeViewerBtn');
        const viewerImg = document.getElementById('viewerImg');
        const viewerInfo = document.getElementById('viewerInfo');
        const downloadBtn = document.getElementById('downloadBtn');
        
        // 当前页码和表情包信息
        let currentPage = 1;
        let totalPages = 1;
        let currentKeyword = '';
        let selectedEmojiId = null;
        
        // 获取表情包列表
        function fetchEmojis(page = 1, keyword = '') {
            loader.style.display = 'block';
            noData.style.display = 'none';
            loadMoreBtn.style.display = 'none';
            
            // 如果是第一页，清空表情包列表
            if (page === 1) {
                emojiGrid.innerHTML = '';
            }
            
            const url = `./api/list_emojis.php?page=${page}&limit=12${keyword ? '&keyword=' + encodeURIComponent(keyword) : ''}`;
            
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    loader.style.display = 'none';
                    
                    if (data.code === 200) {
                        if (data.data.list.length === 0 && page === 1) {
                            // 没有数据
                            noData.style.display = 'block';
                        } else {
                            // 渲染表情包列表
                            renderEmojis(data.data.list);
                            
                            // 更新分页信息
                            totalPages = Math.ceil(data.data.total / data.data.limit);
                            currentPage = page;
                            
                            // 显示分页控件
                            renderPagination();
                        }
                    } else {
                        warnpop('获取表情包列表失败');
                    }
                })
                .catch(error => {
                    loader.style.display = 'none';
                    console.error('Error fetching emojis:', error);
                    warnpop('网络错误，请稍后再试');
                });
        }
        
        // 渲染表情包列表
        function renderEmojis(list) {
            list.forEach(emoji => {
                const card = document.createElement('div');
                card.className = 'emoji-card';
                card.dataset.id = emoji.id;
                card.innerHTML = `
                    <div class="emoji-img-container">
                        <img src="${emoji.file_path}" alt="${emoji.name}" class="emoji-img">
                    </div>
                    <div class="emoji-info">
                        <div class="emoji-name">${emoji.name}</div>
                        <div class="emoji-stats">
                            <div class="emoji-stats-left">
                                <img src="${emoji.uploader.avatar}" alt="上传者头像">
                                <span class="emoji-uploader">${emoji.uploader.name}</span>
                            </div>
                            <div class="emoji-download-count">
                                <i class="ri-download-2-line"></i>
                                <span>${emoji.download_count}</span>
                            </div>
                        </div>
                    </div>
                `;
                
                // 点击表情包卡片，打开查看弹窗
                card.addEventListener('click', function() {
                    openViewer(emoji);
                });
                
                emojiGrid.appendChild(card);
            });
        }
        
        // 渲染分页控件
        function renderPagination() {
            if (totalPages <= 1) {
                pagination.style.display = 'none';
                return;
            }
            
            pagination.style.display = 'flex';
            pagination.innerHTML = '';
            
            // 上一页按钮
            const prevItem = document.createElement('div');
            prevItem.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
            prevItem.innerHTML = `<a class="page-link" href="javascript:void(0);">&laquo;</a>`;
            if (currentPage > 1) {
                prevItem.addEventListener('click', () => fetchEmojis(currentPage - 1, currentKeyword));
            }
            pagination.appendChild(prevItem);
            
            // 页码按钮
            let startPage = Math.max(1, currentPage - 2);
            let endPage = Math.min(totalPages, startPage + 4);
            
            if (endPage - startPage < 4) {
                startPage = Math.max(1, endPage - 4);
            }
            
            for (let i = startPage; i <= endPage; i++) {
                const pageItem = document.createElement('div');
                pageItem.className = `page-item ${i === currentPage ? 'active' : ''}`;
                pageItem.innerHTML = `<a class="page-link" href="javascript:void(0);">${i}</a>`;
                pageItem.addEventListener('click', () => fetchEmojis(i, currentKeyword));
                pagination.appendChild(pageItem);
            }
            
            // 下一页按钮
            const nextItem = document.createElement('div');
            nextItem.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
            nextItem.innerHTML = `<a class="page-link" href="javascript:void(0);">&raquo;</a>`;
            if (currentPage < totalPages) {
                nextItem.addEventListener('click', () => fetchEmojis(currentPage + 1, currentKeyword));
            }
            pagination.appendChild(nextItem);
        }
        
        // 搜索表情包
        function searchEmojis() {
            const keyword = searchInput.value.trim();
            currentKeyword = keyword;
            fetchEmojis(1, keyword);
        }
        
        // 搜索按钮点击事件
        searchBtn.addEventListener('click', searchEmojis);
        
        // 搜索框回车事件
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchEmojis();
            }
        });
        
        // 打开上传弹窗
        openUploadBtn.addEventListener('click', function() {
            <?php if ($userdlzt != 1): ?>
            warnpop('请先登录后再上传表情包');
            setTimeout(function() {
                window.location.href = './login.php';
            }, 1500);
            return;
            <?php endif; ?>
            
            uploadModal.style.display = 'flex';
            
            // 清空预览和表单
            previewContainer.innerHTML = '';
            emojiName.value = '';
            fileInput.value = '';
        });
        
        // 关闭上传弹窗
        closeUploadBtn.addEventListener('click', function() {
            uploadModal.style.display = 'none';
        });
        
        // 点击上传区域，触发文件选择
        dropArea.addEventListener('click', function() {
            fileInput.click();
        });
        
        // 处理文件选择
        fileInput.addEventListener('change', function() {
            handleFiles(this.files);
        });
        
        // 处理拖拽上传
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropArea.addEventListener(eventName, preventDefaults, false);
        });
        
        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }
        
        dropArea.addEventListener('dragenter', highlight, false);
        dropArea.addEventListener('dragover', highlight, false);
        dropArea.addEventListener('dragleave', unhighlight, false);
        dropArea.addEventListener('drop', unhighlight, false);
        
        function highlight() {
            dropArea.classList.add('highlight');
        }
        
        function unhighlight() {
            dropArea.classList.remove('highlight');
        }
        
        dropArea.addEventListener('drop', handleDrop, false);
        
        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;
            handleFiles(files);
        }
        
        // 处理文件
        function handleFiles(files) {
            // 最多允许选择5个文件
            const maxFiles = 5;
            const currentFiles = document.querySelectorAll('.preview-item').length;
            const remainingSlots = maxFiles - currentFiles;
            
            // 检查是否超过允许的最大文件数
            if (files.length > remainingSlots) {
                warnpop(`最多只能选择${maxFiles}个文件，当前还可以添加${remainingSlots}个`);
                // 只处理剩余允许的数量
                files = Array.from(files).slice(0, remainingSlots);
            }
            
            Array.from(files).forEach((file, index) => {
                // 检查文件类型
                if (!['image/jpeg', 'image/png', 'image/gif', 'image/webp'].includes(file.type)) {
                    warnpop(`不支持的文件类型: ${file.name}`);
                    return;
                }
                
                // 检查文件大小（2MB）
                if (file.size > 2 * 1024 * 1024) {
                    warnpop(`文件过大: ${file.name}，最大支持2MB`);
                    return;
                }
                
                // 创建预览
                const preview = document.createElement('div');
                preview.className = 'preview-item';
                preview.dataset.fileName = file.name;
                
                const img = document.createElement('img');
                img.className = 'preview-img';
                
                const reader = new FileReader();
                reader.onload = function(e) {
                    img.src = e.target.result;
                };
                reader.readAsDataURL(file);
                
                const removeBtn = document.createElement('div');
                removeBtn.className = 'preview-remove';
                removeBtn.innerHTML = '&times;';
                removeBtn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    preview.remove();
                });
                
                preview.appendChild(img);
                preview.appendChild(removeBtn);
                previewContainer.appendChild(preview);
            });
        }
        
        // 表单提交事件
        uploadForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // 检查是否选择了文件
            const previewItems = previewContainer.querySelectorAll('.preview-item');
            if (previewItems.length === 0) {
                warnpop('请选择要上传的表情包');
                return;
            }
            
            // 禁用提交按钮
            const submitBtn = document.getElementById('submitUploadBtn');
            submitBtn.disabled = true;
            submitBtn.textContent = '上传中...';
            
            // 创建一个计数器，跟踪已上传的文件数量
            let uploadedCount = 0;
            let failedCount = 0;
            let totalCount = previewItems.length;
            
            // 获取表情名称
            const baseName = emojiName.value.trim() || '表情包';
            
            // 循环上传每个文件
            previewItems.forEach((item, index) => {
                // 创建新的FormData
                const formData = new FormData();
                
                // 为每个表情包生成名称（如果用户只提供了一个名称）
                let itemName = baseName;
                if (totalCount > 1 && baseName === '表情包') {
                    itemName = `${baseName}${index + 1}`;
                } else if (totalCount > 1) {
                    itemName = `${baseName} ${index + 1}`;
                }
                
                formData.append('emoji_name', itemName);
                
                // 从FileList中查找对应的文件
                const fileName = item.dataset.fileName;
                let foundFile = null;
                
                // 在fileInput中查找匹配的文件
                for (let i = 0; i < fileInput.files.length; i++) {
                    if (fileInput.files[i].name === fileName) {
                        foundFile = fileInput.files[i];
                        break;
                    }
                }
                
                // 如果找到了文件
                if (foundFile) {
                    formData.append('emoji_file', foundFile);
                    
                    // 发送上传请求
                    fetch('./api/upload_emoji.php', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.code === 200) {
                            uploadedCount++;
                        } else {
                            failedCount++;
                            console.error('上传失败:', data.msg);
                        }
                        
                        // 检查是否所有文件都已处理
                        if (uploadedCount + failedCount === totalCount) {
                            submitBtn.disabled = false;
                            submitBtn.textContent = '上传表情包';
                            
                            if (failedCount === 0) {
                                successpop(`成功上传${uploadedCount}个表情包`);
                                uploadModal.style.display = 'none';
                                
                                // 重新加载表情包列表
                                fetchEmojis(1, currentKeyword);
                            } else {
                                warnpop(`上传完成：${uploadedCount}个成功，${failedCount}个失败`);
                            }
                        }
                    })
                    .catch(error => {
                        failedCount++;
                        console.error('上传请求错误:', error);
                        
                        // 检查是否所有文件都已处理
                        if (uploadedCount + failedCount === totalCount) {
                            submitBtn.disabled = false;
                            submitBtn.textContent = '上传表情包';
                            warnpop(`上传完成：${uploadedCount}个成功，${failedCount}个失败`);
                        }
                    });
                } else {
                    failedCount++;
                    console.error('未找到文件:', fileName);
                    
                    // 检查是否所有文件都已处理
                    if (uploadedCount + failedCount === totalCount) {
                        submitBtn.disabled = false;
                        submitBtn.textContent = '上传表情包';
                        warnpop(`上传完成：${uploadedCount}个成功，${failedCount}个失败`);
                    }
                }
            });
        });
        
        // 打开表情包查看弹窗
        function openViewer(emoji) {
            viewerImg.src = emoji.file_path;
            viewerInfo.textContent = emoji.name;
            selectedEmojiId = emoji.id;
            viewerModal.style.display = 'flex';
        }
        
        // 关闭表情包查看弹窗
        closeViewerBtn.addEventListener('click', function() {
            viewerModal.style.display = 'none';
        });
        
        // 点击模态框背景关闭
        window.addEventListener('click', function(e) {
            if (e.target === uploadModal) {
                uploadModal.style.display = 'none';
            }
            if (e.target === viewerModal) {
                viewerModal.style.display = 'none';
            }
        });
        
        // 下载表情包
        downloadBtn.addEventListener('click', function() {
            <?php if ($userdlzt != 1): ?>
            warnpop('请先登录后再下载表情包');
            setTimeout(function() {
                window.location.href = './login.php';
            }, 1500);
            return;
            <?php endif; ?>
            
            if (!selectedEmojiId) {
                warnpop('表情包信息获取失败');
                return;
            }
            
            // 发送下载请求
            const formData = new FormData();
            formData.append('emoji_id', selectedEmojiId);
            
            fetch('./api/download_emoji.php', {
                method: 'POST',
                body: formData
            })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        successpop('下载成功');
                        
                        // 创建下载链接
                        const link = document.createElement('a');
                        link.href = data.data.file_path;
                        link.download = data.data.name || 'emoji';
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                        
                        // 更新表情包列表中的下载次数
                        const cards = document.querySelectorAll('.emoji-card');
                        cards.forEach(card => {
                            if (card.dataset.id === selectedEmojiId.toString()) {
                                const downloadCountEl = card.querySelector('.emoji-download-count span');
                                if (downloadCountEl) {
                                    downloadCountEl.textContent = data.data.download_count;
                                }
                            }
                        });
                    } else {
                        warnpop(data.msg || '下载失败，请稍后再试');
                    }
                })
                .catch(error => {
                    console.error('Error downloading emoji:', error);
                    warnpop('网络错误，请稍后再试');
                });
        });
        
        // 初始加载表情包列表
        fetchEmojis();
    });
    </script>
</body>
</html> 