/* 底部菜单样式 */
.footer-menu {
    position: fixed;
    bottom: 0px;
    left: 50%;
    transform: translateX(-50%);
    width: 95%;
    height: 65px;
    background-color: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    display: flex;
    justify-content: space-around;
    align-items: center;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    border-radius: 22px 22px 14px 14px;
    margin-bottom: 5px;
    padding: 0 15px;
    transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.footer-menu-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 22%;
    color: #a0a0a0;
    text-decoration: none;
    font-size: 11px;
    padding: 8px 0;
    position: relative; /* 确保相对定位，使小红点能够正确定位 */
    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.footer-menu-item.active {
    color: #007aff;
    transform: translateY(-8px);
}

.footer-menu-item i {
    font-size: 24px;
    margin-bottom: 4px;
    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
    position: relative;
    z-index: 1;
}

.footer-menu-item i:before {
    position: relative;
    z-index: 1;
}

.footer-menu-item.active i {
    transform: scale(1.25);
    filter: drop-shadow(0 5px 15px rgba(0, 122, 255, 0.5));
}

.footer-menu-item span {
    line-height: 1.2;
    font-weight: 600;
    transition: all 0.25s ease;
    opacity: 0.85;
    font-size: 10px;
    letter-spacing: 0.2px;
}

.footer-menu-item.active span {
    opacity: 1;
    font-weight: 700;
}

/* 增强图标后面的圆形效果 */
.footer-menu-item:after {
    content: '';
    position: absolute;
    top: 8px;
    left: 50%;
    transform: translateX(-50%) scale(0);
    width: 40px;
    height: 40px;
    background-color: rgba(0, 122, 255, 0.08);
    border-radius: 50%;
    z-index: 0;
    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.footer-menu-item.active:after {
    transform: translateX(-50%) scale(1);
}

/* 点击效果 */
.footer-menu-item:active {
    transform: scale(0.92);
}

.footer-menu-item.active:active {
    transform: scale(0.92) translateY(-8px);
}

/* 适配暗黑模式 */
.dark-theme .footer-menu {
    background-color: rgba(30, 30, 30, 0.85);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.25);
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.dark-theme .footer-menu-item {
    color: #777;
}

.dark-theme .footer-menu-item.active {
    color: #0a84ff;
}

.dark-theme .footer-menu-item.active i {
    filter: drop-shadow(0 5px 15px rgba(10, 132, 255, 0.3));
}

.dark-theme .footer-menu-item:after {
    background-color: rgba(10, 132, 255, 0.15);
}

/* 为了避免底部内容被遮挡，添加一个底部填充 */
.footer-padding {
    height: 80px;
    width: 100%;
}

/* 通知小红点 */
.notification-dot {
    position: absolute;
    top: 2px;
    right: 28%;
    width: 8px;
    height: 8px;
    background-color: #ff3b30;
    border-radius: 50%;
    box-shadow: 0 0 4px rgba(255, 59, 48, 0.5);
    animation: pulse 1.5s infinite;
    z-index: 2;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.3);
        opacity: 0.7;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* 图标动画效果 */
@keyframes iconBounce {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-3px);
    }
}

@keyframes iconGlow {
    0%, 100% {
        filter: drop-shadow(0 5px 15px rgba(0, 122, 255, 0.2));
    }
    50% {
        filter: drop-shadow(0 5px 20px rgba(0, 122, 255, 0.6));
    }
}

/* 激活动画 */
.footer-menu-item.active i {
    animation: iconBounce 2s ease-in-out infinite, iconGlow 2s ease-in-out infinite;
} 