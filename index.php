<?php

//decode by nige112
if ($_SERVER["REQUEST_METHOD"] === "POST") {
	$arr = [["code" => "201", "msg" => "非法请求"]];
	exit(json_encode($arr, JSON_UNESCAPED_UNICODE));
}
$iteace = "0";
if (is_file("./config.php")) {
	require "./config.php";
} else {
	exit("请先安装程序！<a href=\"./install/\">点击安装</a>");
}
require "./api/wz.php";
?><!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title><?php echo $name;?></title>
    <!--为搜索引擎定义关键词-->
    <meta name="keywords" content="0717谦友圈、谦友、薛之谦粉丝、薛之谦、兴趣爱好分享、生活分享、在线聊天、谦友群聊、粉丝社交平台">
    <!--为网页定义描述内容 用于告诉搜索引擎，你网站的主要内容-->
    <meta name="description" content="<?php echo $name . "," . $subtitle;?>">
    <meta name="author" content="<?php echo $name;?>">
    <meta name="copyright" content="<?php echo $name;?>">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta http-equiv="cache-control" content="no-cache">
    <meta property="og:title" content="0717谦友圈-谦友群聊圈子聚集地">
<meta property="og:description" content="一个全是谦友的圈子,快来共享朋友圈,还有两千人群聊动态">
<meta property="og:image" content="<?php echo $icon;?>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0,minimum-scale=1.0, user-scalable=no minimal-ui">
    <link rel="apple-touch-icon" sizes="57x57" href="<?php echo $icon;?>" /><!-- Standard iPhone -->
    <link rel="apple-touch-icon" sizes="114x114" href="<?php echo $icon;?>" /><!-- Retina iPhone -->
    <link rel="apple-touch-icon" sizes="72x72" href="<?php echo $icon;?>" /><!-- Standard iPad -->
    <link rel="apple-touch-icon" sizes="144x144" href="<?php echo $icon;?>" /><!-- Retina iPad -->
    <link rel="icon" href="<?php echo $icon;?>" type="image/x-icon" />
    <meta name="robots" content="index,follow">
    <meta name="baidu-site-verification" content="codeva-Ym0SwgyUuy" />
<meta name="baidu_union_verify" content="6eb99245f6689d4452542bb0cb16cc18">
    <meta name="format-detection" content="telephone=no">
    <meta http-equiv="x-rim-auto-match" content="none">
    <!-- 添加Font Awesome图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="<?php echo $iconurl_url;?>">
    <link rel="stylesheet" type="text/css" href="./assets/css/style.css?v=<?php echo $resversion;?>">
    <link rel="stylesheet" type="text/css" href="./assets/css/footer-menu.css?v=<?php echo $resversion;?>">
    <link rel="stylesheet" type="text/css" href="./assets/mesg/dist/css/style.css?v=<?php echo $resversion;?>"><!--引入弹窗对话框-->
    <link rel="stylesheet" type="text/css" href="./assets/css/jquery.fancybox.min.css?v=<?php echo $resversion;?>">
    <!-- 引入Remix图标库 -->
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">

    <style>
        body {
  font-family: Arial, sans-serif;
  background-color: #f2f2f2;
  margin: 0;
  padding: 0;
}
   .floating-rectangle {
    position: fixed;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 60px;
    height: 30px;
    background-color: #08c163;
    cursor: pointer;
    font-size: 12px;
    border-radius: 0 20px 20px 0;
    line-height: 30px;
    color: #fff;
    padding: 0 10px;
    text-align: center;
    }
.dark-theme span{
    color:var(--textqh);
}
.overlay {
  display: none;
  justify-content: center;
  align-items: center;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5); /* 半透明背景 */
  z-index: 1000; /* 设置一个较高的 z-index 值，确保弹窗在顶层 */
}

.modal {
  background-color: #fff;
  border-radius: 5px;
  max-width: 80%;
  width: 300px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  padding: 20px;
  text-align: center;
  z-index: 1100; /* 比 .overlay 更高的 z-index 值，确保在最顶层 */
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #ddd;
  padding-bottom: 10px;
  margin-bottom: 10px;
}

.modal-header h2 {
  margin: 0;
}

.close {
  color: #aaa;
  font-size: 28px;
  cursor: pointer;
}

.modal-content {
  padding: 10px 0;
  font-size: 14px;
  line-height: 25px;
      text-align: left;
}

.modal-footer {
  margin-top: 10px;
}

.btn-close {
  background-color: #4CAF50;
  color: white;
  padding: 8px 20px;
  border: none;
  border-radius: 3px;
  cursor: pointer;
}

.btn-close:hover {
  background-color: #45a049;
}

.content {
  padding: 20px;
  text-align: center;
  margin-top: 50px; /* 留出弹窗的空间 */
}

/* 解决图标库冲突 */
.footer-menu i[class^="ri-"], 
.footer-menu i[class*=" ri-"],
.notify-tab i[class^="ri-"],
.notify-tab i[class*=" ri-"],
.notify-mark-read i[class^="ri-"],
.notify-mark-read i[class*=" ri-"],
.notify-item i[class^="ri-"],
.notify-item i[class*=" ri-"],
.ranking-page-title i[class^="ri-"],
.ranking-page-title i[class*=" ri-"],
.ranking-title i[class^="ri-"],
.ranking-title i[class*=" ri-"],
.top-user-rank i[class^="ri-"],
.top-user-rank i[class*=" ri-"] {
    font-family: 'remixicon' !important;
}

i.iconfont {
    font-family: "iconfont" !important;
}

/* 统一交互按钮样式 */
.sh-content-right-time-right-left-z,
.sh-content-right-time-right-left-y,
.sh-content-right-time-right-left-s {
    display: flex;
    align-items: center;
    transition: transform 0.2s ease;
}

.sh-content-right-time-right-left-z:active,
.sh-content-right-time-right-left-y:active,
.sh-content-right-time-right-left-s:active {
    transform: scale(1.1);
}

.sh-content-right-time-right-left-s i {
    margin-right: 5px;
}

/* 收藏按钮选中状态 */
.sh-content-right-time-right-left-s i.active {
    color: #ff6b6b;
}

    </style>
    
   
    <?php echo $scfontzt;?>    <?php 
$folderPath = "./user/bobg/";
if (!file_exists($folderPath)) {
	mkdir($folderPath, 511, true);
}
if (file_exists("./user/bobg/bobg.jpg")) {
	?><!--背景图--><style>body{background-image: url(./user/bobg/bobg.jpg);}</style><?php 
} elseif (file_exists("./user/bobg/bobg.png")) {
	?><!--背景图--><style>body{background-image: url(./user/bobg/bobg.png);}</style><?php 
} elseif (file_exists("./user/bobg/bobg.jpeg")) {
	?><!--背景图--><style>body{background-image: url(./user/bobg/bobg.jpeg);}</style><?php 
} elseif (file_exists("./user/bobg/bobg.gif")) {
	?><!--背景图--><style>body{background-image: url(./user/bobg/bobg.gif);}</style><?php 
}
?>    <?php echo "<style>" . $filtercucss . "</style>";?></head>
<body class="<?php 
if (isset($_COOKIE["dark_theme"])) {
	if ($_COOKIE["dark_theme"] == "dark-theme") {
		echo "dark-theme";
	}
}
echo "\">
    ";
if ($pagepass != "") {
	if (isset($_COOKIE["pagepass"])) {
		if ($_COOKIE["pagepass"] != md5(md5($pagepass))) {
			include "./site/pagepass.php";
		}
	} else {
		include "./site/pagepass.php";
	}
}
?>   <div class="centent">
        <div class="sh-main">
            <div class="sh-main-head">
                <div class="sh-main-head-top" id="sh-main-head-top">
                   <!-- 左边 -->
                   <div class="sh-main-head-top-left">
                        
                        <?php 
$so = addslashes(htmlspecialchars($_GET["so"]));
if ($so != "") {
	?><div class="sh-main-head-top-left-s" onclick="location.href='./index.php'">
                            <i class="iconfont icon-weibiaoti al-sxb" id="top-left-xx"></i>
                        </div><?php 
}
if ($userdlzt == 0) {
	if ($loginkg == 1) {
		?>

                            <div class="sh-main-head-top-left-s" onclick="kqlogin()">
                            <!--img src="./assets/img/wo.svg" alt="" id="top-left-1"-->
                            <i class="iconfont icon-account-circle-fill ri-sxzytx" id="top-left-1"></i>
                            </div><?php 
	}
}
echo "                        <!--音乐-->
                        ";
if ($wzmusic == -1) {
} else {
	$arr = explode(PHP_EOL, $wzmusic);
	$arry = array_filter($arr);
	$ary = count($arry);
	$mu = rand(0, $ary - 1);
	$yiny = $arry[$mu];
	$arys = explode("|", $yiny);
	$mum = $arys[0];
	$muurl = $arys[1];
	$muurl = preg_replace("/[\\s\\r\\n]+/", "", $muurl);
	if ($mum == "网易音乐" && is_numeric($muurl)) {
		$mum = $arys[0];
		$muurl = "//music.163.com/song/media/outer/url?id=" . $muurl . ".mp3";
	}
	echo "
                                <div class=\"sh-main-head-top-left-mu\">
                            <div class=\"sh-main-top-mu\" lang=\"0\" onclick=\"syaudbf()\"><i class=\"iconfont icon-jixu ri-z-sx\" id=\"sh-main-top-mu\" lang=\"0\" data-bfzt=\"bb\"></i></div>
                            <div id=\"sh-main-top-g-m\" class=\"sh-main-top-g-container\" lang=\"" . $mum . "\">
                                        <div id=\"sh-main-top-mucisjd\" lang=\"0\" style=\"display:none\">
                                        <!--音乐动画-->
                                           <div class=\"shaft-load2\">
		                                     <div class=\"shaft1\"></div>
		                                     <div class=\"shaft2\"></div>
		                                     <div class=\"shaft3\"></div>
		                                     <div class=\"shaft4\"></div>
		                                     <div class=\"shaft5\"></div>
		                                     <div class=\"shaft6\"></div>
		                                     <div class=\"shaft7\"></div>
		                                     <div class=\"shaft8\"></div>
		                                     <div class=\"shaft9\"></div>
		                                     <!--div class=\"shaft10\"></div-->
	                                       </div>
                                        </div>
                            <div class=\"sh-main-top-mu-bgmq\" onclick=\"sjsyyy()\"><i class=\"iconfont icon-yinle_2 ri-z-sx\" id=\"sh-main-top-mu-bgmq\"></i></div>
                            <audio id=\"sh-main-top-musicplay-b\" referrerpolicy=\"origin\" src=\"" . $muurl . "\" type=\"audio/mp3\" controls=\"controls\" style=\"display: none;\">
                                        您的浏览器不支持音频元素。
                            </audio>
                        </div>
                                ";
}
?>                        
                        
                        
                    </div>
                    <!-- 右边 -->
                    <div class="sh-main-head-top-right">
                        
                        
                        
                        <?php 
if ($userdlzt == 1) {
	if ($ptpfan == 1) {
		?>

<div class="sh-main-head-top-right-s" onclick="fby()">
  <i class="iconfont icon-xiangji2 ri-sx" id="top-right-3"></i>
</div>
<?php 
	}
	?>

                            <div class="sh-main-head-top-right-s" onclick="kqnews()"><?php 
	if ($userdlzt == 1) {
		include "./config.php";
		$conn = new mysqli($servername, $username, $password, $dbname);
		if ($conn->connect_error) {
			exit("连接失败: " . $conn->connect_error);
		}
		$sqlxx = "SELECT * FROM message order by id desc";
		$resultxx = $conn->query($sqlxx);
		$xxsl = 0;
		while ($rowxx = $resultxx->fetch_assoc()) {
			$xxsl++;
			$fuserxx = $rowxx["fuser"];
			$fimgxx = $rowxx["fimg"];
			$fnamexx = $rowxx["fname"];
			$suserxx = $rowxx["suser"];
			$titlexx = $rowxx["title"];
			$textxx = $rowxx["text"];
			$ftimexx = $rowxx["ftime"];
			$msgxx = $rowxx["msg"];
			$xxid = $rowxx["id"];
			if ($suserxx == $user_zh) {
				if ($msgxx != "-1") {
					if ($msgxx == 0) {
						$xmsgxx = "<p id=\"xiaoxhd\" class=\"xiaoxhd\"></p>";
					}
				}
			}
		}
		echo $xmsgxx;
	}
	?><i class="xxtbcl iconfont icon-lingdang ri-sx" id="top-right-1"></i>
                            </div>
                            <div class="sh-main-head-top-right-s" onclick="msg()">
  <svg t="1729283767430" id="msgxx" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4559" width="20" height="20"><path d="M493.2 170.5l-160.5 98.2c-20.9 12.7-45 19.4-69.5 19.3h-79.4c-66.2 0-119.1 51.5-119.1 114.2v227c0 69.2 57.9 125.5 129.1 125.5h92.6c24.8 0 47.9 6.4 69.5 19.3l138.9 82.1c10 6.5 21.5 9.7 34.7 9.7 36.4 0 66.2-29 66.2-64.4V225.2c0-11.2-3.3-24.2-10-33.8-21.4-30.5-61.1-40.2-92.5-20.9zM924.9 481.1H726.4c-18-0.2-32.8 14.2-33.1 32.2 0 17.7 14.9 32.2 33.1 32.2h198.5c18.2 0 33.1-14.5 33.1-32.2-0.3-18-15.1-32.4-33.1-32.2zM911.7 754.8l-172.1-96.5c-15.4-9.2-35.4-4.2-44.7 11.2-4.5 7.2-5.9 16-3.7 24.2s7.7 15.2 15.2 19.2l172.1 96.6c15.4 9.2 35.4 4.2 44.7-11.2 10-14.5 5-33.8-11.5-43.5zM738 368.5l172-96.6c16.6-9.6 21.5-28.9 11.6-43.5-9.9-16.1-29.7-20.9-44.7-11.2l-172.1 96.6c-16.5 9.7-21.5 29-11.6 43.5 10.1 14.4 29.9 20.8 44.8 11.2z" p-id="4560" fill="#ffffff"></path></svg>
</div>
                            <?php 
}
?>                        <?php 
if ($lnkzt == 0) {
	?>

                            <div class="sh-main-head-top-right-s" onclick="kqlink()">
                            <i class="iconfont icon-tongxunlu-copy ri-sx" id="top-right-2"></i>
                        </div>
                        
                        
                            <?php 
}

?>                        
                    </div>
                </div>

<?php 
if ($userdlzt == 0) {
	$wymz = $glyname;
	$wylj = "JavaScript:;";
	$wyimg = $glyimg;
	$wyqm = $sign;
	$wyhomeimg = $homimg;
} else {
	$wymz = $zjnc;
	$wylj = "./home.php";
	$wyimg = $zjimg;
	$wyqm = $zjsign;
	$wyhomeimg = $homimg;
}
?>

                <div class="sh-main-head-img"
                    style="background-image:url(<?php echo $wyhomeimg;?>)">
                </div>
            </div>
            
            <div class="sh-main-head-headimg">
                <div class="sh-main-head-headimg-tx">
                    <h4><?php echo $wymz;?></h4>
                    
                    <a href="<?php echo $wylj;?>"><img src="./assets/img/thumbnail.svg" referrerpolicy="no-referrer-when-downgrade" data-src="<?php echo $wyimg;?>" alt="头像"></a>
                </div>
                <div class="sh-main-head-headimg-qm">
                    <p><?php echo $wyqm;?></p>
                </div>
            </div>
            
            
            
            
            
            
            

            <div style="display:none;position: absolute;top: -100%;" id="pinglunkfk">
             <div class="sh-pinglunkuang" id="pinglunkuang">
                    
                    
                    <div class="sh-pinglun" id="sh-pinglun">
                        <!--游客模式-->
                    <?php 
if ($userdlzt == 0) {
	if ($viscomm == 0) {
		?>

                        <div class="sh-plk-yk" id="sh-plk-yk">
                        <div class="sh-plk-yk-z" style="margin-left: 8px;"><input id="vis_name" type="text" value="" maxlength="10" minlength="1" placeholder="昵称*" autocomplete="off"></div>
                        <div class="sh-plk-yk-zz"><input id="vis_email" type="text" value="" maxlength="100" minlength="1" placeholder="邮箱*" autocomplete="off"></div>
                        <div class="sh-plk-yk-z" style="margin-right: 8px;"><input id="vis_url" type="text" value="" maxlength="500" minlength="1" placeholder="网站" autocomplete="off"></div>
                    </div>
                        <?php 
	}
}
?>                        <div class="sh-pinglun-s">
                            <textarea name="text" id="bletext" class="form-controll" placeholder="评论" required="" spellcheck="false" maxlength="500" onchange="this.value=this.value.substring(0, 500)" onkeydown="this.value=this.value.substring(0, 500)" onkeyup="this.value=this.value.substring(0, 500)" oninput="myjtbl()"></textarea>
                        </div>
                        <!-- 表情 -->
                        <div class="sh-pinglun-biao" id="biaoqing">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E591B5E591B5_2x.png" alt="::(呵呵)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E59388E59388_2x.png" alt="::(哈哈)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E59090E8888C_2x.png" alt="::(吐舌)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E5A4AAE5BC80E5BF83_2x.png" alt="::(太开心)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E7AC91E79CBC_2x.png" alt="::(笑眼)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E88AB1E5BF83_2x.png" alt="::(花心)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E5B08FE4B996_2x.png" alt="::(小乖)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E4B996_2x.png" alt="::(乖)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E68D82E598B4E7AC91_2x.png" alt="::(捂嘴笑)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E6BB91E7A8BD_2x.png" alt="::(滑稽)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E4BDA0E68782E79A84_2x.png" alt="::(你懂的)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E4B88DE9AB98E585B4_2x.png" alt="::(不高兴)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E68092_2x.png" alt="::(怒)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E6B197_2x.png" alt="::(汗)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E9BB91E7BABF_2x.png" alt="::(黑线)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E6B3AA_2x.png" alt="::(泪)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E79C9FE6A392_2x.png" alt="::(真棒)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E596B7_2x.png" alt="::(喷)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E6838AE593AD_2x.png" alt="::(惊哭)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E998B4E999A9_2x.png" alt="::(阴险)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E98499E8A786_2x.png" alt="::(鄙视)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E985B7_2x.png" alt="::(酷)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E5958A_2x.png" alt="::(啊)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E78B82E6B197_2x.png" alt="::(狂汗)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/what_2x.png" alt="::(what)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E79691E997AE_2x.png" alt="::(疑问)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E985B8E788BD_2x.png" alt="::(酸爽)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E59180E592A9E788B9_2x.png" alt="::(呀咩蹀)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E5A794E5B188_2x.png" alt="::(委屈)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E6838AE8AEB6_2x.png" alt="::(惊讶)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E79DA1E8A789_2x.png" alt="::(睡觉)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E7AC91E5B0BF_2x.png" alt="::(笑尿)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E68C96E9BCBB_2x.png" alt="::(挖鼻)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E59090_2x.png" alt="::(吐)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E78A80E588A9_2x.png" alt="::(犀利)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E5B08FE7BAA2E884B8_2x.png" alt="::(小红脸)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E68792E5BE97E79086_2x.png" alt="::(懒得理)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E58B89E5BCBA_2x.png" alt="::(勉强)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E788B1E5BF83_2x.png" alt="::(爱心)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E5BF83E7A28E_2x.png" alt="::(心碎)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E78EABE791B0_2x.png" alt="::(玫瑰)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E7A4BCE789A9_2x.png" alt="::(礼物)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E5BDA9E899B9_2x.png" alt="::(彩虹)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E5A4AAE998B3_2x.png" alt="::(太阳)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E6989FE6989FE69C88E4BAAE_2x.png" alt="::(星星月亮)"
                                onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E992B1E5B881_2x.png" alt="::(钱币)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E88CB6E69DAF_2x.png" alt="::(茶杯)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E89B8BE7B395_2x.png" alt="::(蛋糕)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E5A4A7E68B87E68C87_2x.png" alt="::(大拇指)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E8839CE588A9_2x.png" alt="::(胜利)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/haha_2x.png" alt="::(haha)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/OK_2x.png" alt="::(OK)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E6B299E58F91_2x.png" alt="::(沙发)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E6898BE7BAB8_2x.png" alt="::手纸" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E9A699E89589_2x.png" alt="::(香蕉)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E4BEBFE4BEBF_2x.png" alt="::(便便)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E88DAFE4B8B8_2x.png" alt="::(药丸)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E7BAA2E9A286E5B7BE_2x.png" alt="::(红领巾)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E89CA1E7839B_2x.png" alt="::(蜡烛)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E99FB3E4B990_2x.png" alt="::(音乐)" onclick="biaoqzj()">
                            <img src="./assets/img/thumbnail.svg" data-src="./assets/owo/paopao/E781AFE6B3A1_2x.png" alt="::(灯泡)" onclick="biaoqzj()">
                        </div>
                        <!-- 表情 end-->
                        
                        <!-- 表情开关与评论发送 -->
                        <div class="sh-pinglun-fs">
                            <div class="sh-pinglun-fs-right">
                                <!-- 游客开关 -->
                                <?php 
if ($userdlzt == 0) {
	if ($viscomm == 0) {
		?>

                                       <div class="sh-pinglun-fs-right-bqimg" id="ykkg" onclick="ykkg()">
                                       <i class="iconfont icon-yonghu1 ri-sxbqxz" id="sh-pinglun-fs-right-ykkgb" title="游客模式"></i>
                                       </div><?php 
	}
}
?>                                <!-- 表情开关 -->
                                <div class="sh-pinglun-fs-right-bqimg" id="bqkg" onclick="bqkg()">
                                    <i class="iconfont icon-biaoqing ri-sxbqxz" id="sh-pinglun-fs-right-bqimg"></i>
                                </div>
                                <!-- 发送按钮 -->
                                <div class="sh-pinglun-fs-right-fs" id="sh-pinglun-fs-right-fs" onclick="fasong()">
                                    <span>发送</span>
                                </div>
                            </div>
                        </div>
                        
                    </div>
                    <!-- cs -->
                    <div class="huifucanshu">
                        <p id="sh-tieid">-</p>
                        <p id="sh-tiehf">-</p>
                        <p id="sh-tieea">-</p>
                    </div>
                </div>
            </div>



<div class="floating-rectangle" >谦友群聊</div>

            <div class="sh-nrbk" id="sh-nrbk" style="width:100%">
                
                        <?php 
include "./api/topes.php";
$wzsl = 0;
$so = addslashes(htmlspecialchars($_GET["so"]));
if ($so!= "" && $so!= "null") {
    $sql = "SELECT * FROM essay where ptpaud<>'0' and ptpaud<>'-1' and ptpys<>'0' and ptptext LIKE '%{$so}%' 
            ORDER BY 
                -- 先按照是否为公告排序，公告（ptpgg = 1）排在最前面
                CASE WHEN ptpgg = 1 THEN 0 ELSE 1 END, 
                -- 再按照是否置顶排序，置顶文章（iszhiding = 1）排在公告之后
                CASE WHEN iszhiding = 1 THEN 0 ELSE 1 END, 
                -- 最后按照原有的排序规则排序，如id等
                ptpgg DESC, id desc 
            limit {$essgs}";
} else {
    $sql = "SELECT * FROM essay where ptpaud<>'0' and ptpaud<>'-1' and ptpys<>'0' 
            ORDER BY 
                -- 先按照是否为公告排序，公告（ptpgg = 1）排在最前面
                CASE WHEN ptpgg = 1 THEN 0 ELSE 1 END, 
                -- 再按照是否置顶排序，置顶文章（iszhiding = 1）排在公告之后
                CASE WHEN iszhiding = 1 THEN 0 ELSE 1 END, 
                -- 最后按照原有的排序规则排序，如id等
                ptpgg DESC, id desc 
            limit {$essgs}";
}
$result = $conn->query($sql);
while ($row = $result->fetch_assoc()) {
	$wzsl = $wzsl + 1;
	$ptpuser = $row["ptpuser"];
	$ptpimg = $row["ptpimg"];
	$ptpname = $row["ptpname"];
	$ptptext = $row["ptptext"];
	$ptpimag = $row["ptpimag"];
	$ptpvideo = $row["ptpvideo"];
	$ptpmusic = $row["ptpmusic"];
	$ptplx = $row["ptplx"];
	$ptpdw = $row["ptpdw"];
	$ptptime = $row["ptptime"];
	$ptpgg = $row["ptpgg"];
	$ptpggurl = $row["ptpggurl"];
	$iszhiding = $row["iszhiding"];
	$ptpys = $row["ptpys"];
	$commauth = $row["commauth"];
	$ptpaud = $row["ptpaud"];
	$ptpip = $row["ip"];
	$cid = $row["cid"];
	$wid = $row["id"];
	$partssp = explode("|", $ptpvideo);
	$ptpvideo = $partssp[0];
	$ptpvideofm = $partssp[1];
	if ($snk == "-201-201-1") {
	} else {
		if (in_array($wid, $ars)) {
			continue;
		}
	}
	$imgar = explode("(+@+)", $ptpimag);
	$coun = count($imgar);
	if ($coun == 1) {
		$tusty = "grid-template-columns:1fr;width: 55%;";
	} else {
		if ($coun == 4 || $coun == 2) {
			$tusty = "grid-template-columns:1fr 1fr;width: 55%;";
		} else {
			$tusty = "grid-template-columns:1fr 1fr 1fr;";
		}
	}
	if ($iszhiding == 1) {
	    $zhidingdiv = "display: flex;";
	}else{
	    $zhidingdiv = "display: none;";
	}
	if ($ptpgg == 1) {
		$ggdiv = "display: flex;";
		$ggurl = "<div class=\"sh-content-right-ggurl\"><i class=\"iconfont icon-lianjie1 ri-sxwzgg\"></i><a href=\"" . $ptpggurl . "\">点击赞助</a></div>";
// 		$ggurl = "";
		$gps = "";
	} else {
		$ggdiv = "display: none;";
		$ggurl = "";
		$gps = "<div class=\"sh-content-right-gps\"><a href=\"javascript:;\">" . $ptpdw . "</a></div>";
	}
	$time = strtotime($ptptime);
	$wzfbsj = ReckonTime($time);
	if ($ptpys == 1 && $ptpaud == 1) {
		$contenttext0 = preg_replace("/<img[^>]+>/i", "", $ptptext);
		$contenttext1 = preg_replace("/<span[^>]+>/i", "", $contenttext0);
		$contenttext = preg_replace("/<a href=[^>]+>/i", "", $contenttext1);
		$contenttext = str_replace(" ", "", $contenttext);
		if (iconv_strlen($contenttext, "UTF-8") > 100) {
			$ptptext = "<span class=\"wzndhycyc\" id=\"sh-content-qwdid-" . $cid . "\">" . $ptptext . "</span><a href=\"JavaScript:;\" class=\"sh-content-quanwenan\" id=\"sh-content-quanwenan-" . $cid . "\" lang=\"0\" onclick=\"quanwenan()\">全文</a>";
		} else {
			$ptptext = "<span>" . $ptptext . "</span>";
		}
		if ($ptpname == "匿名用户") {
			$arcuserurl = "";
		} else {
			$arcuserurl = "onclick=\"location.href='./archives.php?user=" . md5(md5($ptpuser)) . "'\"";
		}
		echo "
		
                    <div class=\"sh-content\" id=\"sh-content-" . $cid . "\">
                <!-- 左边 -->
                <div class=\"sh-content-left\">
                    <!-- 头像 -->
                    <img src=\"./assets/img/thumbnail.svg\" referrerpolicy=\"no-referrer-when-downgrade\" data-src=\"" . $ptpimg . "\" alt=\"头像\" " . $arcuserurl . ">
                </div>
                <!-- 右边 -->
                <div class=\"sh-content-right\">
                    <!-- 昵称与内容 -->
                    <div class=\"sh-content-right-head\" >
                        <!-- 昵称 -->
                        <div class=\"sh-content-right-head-title\" >
                            <p>" . $ptpname . "</p>
                            <div class=\"sh-content-right-head-title-ad\" style=\"" . $ggdiv . "\">
                                <p>公告</p>
                            </div>
                            <div class=\"sh-content-right-head-title-ad\" style=\"" . $zhidingdiv . "\">
                                <p>置顶</p>
                            </div>
                        </div>
                        <!-- 内容 -->
                        <div style=\"font-size:14px\" onclick=\"window.location.href = './view.php?cid=" . $cid . "';\">" . $ptptext . '</div>';
		if ($ptplx == "only") {
			echo "<!-- 图片 -->";
		} elseif ($ptplx == "img") {
			if ($coun > "1") {
				$picimg = "style=\"width: 100%;\"";
			} else {
				$picimg = "";
			}
			echo "<!-- 图片 -->
                        <div class=\"sh-content-right-img\" id=\"imglib-" . $cid . "\" style=\"" . $tusty . "\">";
			for ($i = 0; $i < $coun; $i++) {
				$tuimg = $imgar[$i];
				if ($i > 7) {
					$duoimg = $coun - $i - 1;
					if ($coun > 9) {
						if ($i == 8) {
							echo "<a href=\"" . $tuimg . "\" class=\"sh-content-right-img-pic\" data-fancybox=\"gallery" . $cid . "\" data-caption=\"\">
                                             <span class=\"sh-content-right-img-pic-mask\">+" . $duoimg . "</span>
                                             <img src=\"./assets/img/thumbnail.svg\" referrerpolicy=\"no-referrer-when-downgrade\"  data-src=\"" . $tuimg . "\" alt=\"\" " . $picimg . ">
                                             </a>";
						} else {
							echo "<a href=\"" . $tuimg . "\" class=\"sh-content-right-img-pic\" data-fancybox=\"gallery" . $cid . "\" data-caption=\"\"  style=\"display:none;\">
                                             <img src=\"" . $tuimg . "\" referrerpolicy=\"no-referrer-when-downgrade\" data-src=\"" . $tuimg . "\" alt=\"\" " . $picimg . ">
                                             </a>";
						}
					} else {
						echo "<a href=\"" . $tuimg . "\" class=\"sh-content-right-img-pic\" data-fancybox=\"gallery" . $cid . "\" data-caption=\"\">
                                         <img src=\"./assets/img/thumbnail.svg\" referrerpolicy=\"no-referrer-when-downgrade\" data-src=\"" . $tuimg . "\" alt=\"\" " . $picimg . ">
                                         </a>";
					}
				} else {
					echo "<a href=\"" . $tuimg . "\" class=\"sh-content-right-img-pic\" data-fancybox=\"gallery" . $cid . "\" data-caption=\"\">
                                     <img src=\"./assets/img/thumbnail.svg\" referrerpolicy=\"no-referrer-when-downgrade\" data-src=\"" . $tuimg . "\" alt=\"\" " . $picimg . ">
                                     </a>";
				}
			}
			?></div><?php 
		} elseif ($ptplx == "video") {
			if ($videoauplay == 1) {
				$videobf = "autoplay";
				$videobfplas = "";
			} else {
				$videobf = "";
				$videobfplas = "<i class=\"iconfont icon-sa4f56\" id=\"sh-content-video-videobfb-" . $cid . "\" style=\"width: fit-content;height: fit-content;grid-column: 1;grid-row: 1;z-index: 5;position: absolute;top: 50%;left: 50%;transform: translate(-50%, -50%);font-size: 40px;color: #ffffff;display: flex;cursor: pointer;padding: 15px;pointer-events: none;\"></i>";
			}
			if ($filterpiccir == 1) {
				$vidoyuan = "1";
			} else {
				$vidoyuan = "0";
			}
			echo "
                            <!-- 视频 -->
                        <div class=\"sh-video\" id=\"sh-content-video-" . $cid . "\" onclick=\"videofdgb()\" lang=\"0\">
                            <video name=\"sh-videokid\" referrerpolicy=\"origin\" class=\"sh-content-video\" data-ybs=\"" . $vidoyuan . "\" poster=\"" . $ptpvideofm . "\" id=\"sh-content-videok-" . $cid . "\" src=\"" . $ptpvideo . "\" playsinline webkit-playsinline preload=\"metadata\" " . $videobf . " muted loop onclick=\"videofd()\" lang=\"0\" disablePictureInPicture=\"true\" controlslist=\"nodownload nofullscreen noremoteplayback\"></video>
                            " . $videobfplas . "
                            <i class=\"iconfont icon-quxiao\" id=\"sh-content-videog-" . $cid . "\" lang=\"0\"></i>
                            <span class=\"sh-video-span\" id=\"sh-video-span-" . $cid . "\">MP4</span>
                        </div>
                            ";
		} elseif ($ptplx == "music") {
			echo "<!-- 音乐 -->";
			if (is_numeric($ptpmusic)) {
			} else {
				$mus = explode("|", $ptpmusic);
				include "./site/musicplay.php";
			}
		}
		?>

                    </div>
                    <!-- 地址 --><?php 
		if ($ptpdw != "") {
			echo $gps;
		}
		echo "<!--广告-->";
		echo $ggurl;
		$sql2b = "SELECT * FROM lcke WHERE lwz = '{$cid}' AND lwz NOT IN ('1729412233147865308', '1729281541567427993')";
		$result2b = mysqli_query($conn, $sql2b);
		if (mysqli_num_rows($result2b) > 0) {
			$sql2a = "SELECT * FROM lcke";
			$result2a = $conn->query($sql2a);
			while ($row2a = $result2a->fetch_assoc()) {
				if ($row2a["lwz"] == $cid) {
					$dianzmlnr = $row2a["luser"];
					if ($user_zh == "" || $user_name == "" || $user_img == "" || $user_passid == "") {
						if ($dianzmlnr == $_SESSION["visykmz_userip"]) {
							$dianzmlnr = "取消";
							$dianzmlimg = "iconfont icon-aixin2 ri-sxdzlikehs";
							break;
						} else {
							$dianzmlnr = "赞";
							$dianzmlimg = "iconfont icon-aixin ri-sxdzlike";
						}
					} else {
						if ($dianzmlnr == $user_zh) {
							$dianzmlnr = "取消";
							$dianzmlimg = "iconfont icon-aixin2 ri-sxdzlikehs";
							break;
						} else {
							$dianzmlnr = "赞";
							$dianzmlimg = "iconfont icon-aixin ri-sxdzlike";
						}
					}
				}
			}
		} else {
			$dianzmlnr = "赞";
			$dianzmlimg = "iconfont icon-aixin ri-sxdzlike";
		}
		if ($commauth == 1) {
			$gwzkplzt = "
    <div class=\"sh-content-right-time-right-left-y\" id=\"" . $cid . "\" onclick=\"plkkg()\">
                                    <i class=\"iconfont icon-pinglun2 ri-sxdzcomm\"></i>
                                    <span>评论</span>
                                </div>
    ";
		} else {
			$gwzkplzt = "
    <div class=\"sh-content-right-time-right-left-y\" id=\"" . $cid . "\">
                                    <i class=\"iconfont icon-pinglun2 ri-sxdzcomm\"></i>
                                    <span>评论关闭</span>
                                </div>
    ";
		}
		echo "
                    <!-- 时间与点赞 -->
                    <div class=\"sh-content-right-time\">
                        <!-- 时间 -->
                        <div class=\"sh-content-right-time-left\"><span>" . $wzfbsj . "</span></div>
                        <!-- 点赞 -->
                        <div class=\"sh-content-right-time-right\">
                            <!-- 左边合集 -->
                            <div class=\"sh-content-right-time-right-left\" id=\"pl-" . $cid . "\" name=\"pl\">
                                <div class=\"sh-content-right-time-right-left-z\" onclick=\"dinazan()\">
                                    <i class=\"" . $dianzmlimg . "\" id=\"tiezimg-" . $cid . "\"></i>
                                    <span id=\"tiezdz-" . $cid . "\">" . $dianzmlnr . "</span>
                                </div>
                                <p></p>
                                " . $gwzkplzt . "
                                <p></p>
                                <div class=\"sh-content-right-time-right-left-s\" id=\"" . $cid . "\" onclick=\"toggleFavorite(this)\">
                                    <i class=\"fas fa-star\" id=\"favimg-" . $cid . "\"></i>
                                    <span id=\"favtext-" . $cid . "\">收藏</span>
                                </div>
                            </div>
                            <!-- 右边点赞控制按钮 -->
                            <div class=\"sh-content-right-time-right-right\" id=\"" . $cid . "\" onclick=\"plk()\">
                                <p class=\"zp1\"></p>
                                <p></p>
                            </div>
                        </div>
                    </div>
                    <!-- 点赞列表与评论 -->
                    <div class=\"sh-zanp\" id=\"zanss-" . $cid . "\">
                    ";
		$sql2 = "select * from lcke where lwz= '{$cid}' AND lwz NOT IN ('1730113287704910957', '1729281541567427993')";
		$result2 = mysqli_query($conn, $sql2);
		if (mysqli_num_rows($result2) > 0) {
			echo "
    <!-- 点赞列表 -->
                        <div class=\"sh-zanp-zan\" id=\"zans-" . $cid . "\">
                            <!-- 左侧点赞图标 -->
                            <div class=\"sh-zanp-zan-left\"><!--img src=\"./assets/img/likel.svg\" alt=\"\"--><i class=\"iconfont icon-aixin ri-sxwzlike\"></i></div>
                            <!-- 右边点赞名列表 -->
                            <ul class=\"sh-zanp-zan-right\" id=\"zlbeh-" . $cid . "\">
                                ";
			$iw = 0;
			$dwys = 0;
			while ($row2 = mysqli_fetch_assoc($result2)) {
				$dianzmys = $row2["luser"];
				if (strpos($dianzmys, "vis#-[") !== false || strpos($dianzmys, "]-#vis") !== false) {
					$dwys++;
				} else {
					$iw++;
					$dianzms = $row2["lname"];
					echo "<li id=\"zan-" . $cid . "\" lang=\"" . $dianzms . "\">" . $dianzms . "</li>";
				}
			}
			if ($dwys != 0) {
				echo "<li id=\"fkzan-" . $cid . "\">" . $dwys . "位访客</li>";
			}
			?>

     </ul>
    </div>
    <?php 
		} else {
			echo "
    
    <!-- 点赞列表 -->
                        <div class=\"sh-zanp-zan\" id=\"zans-" . $cid . "\" style=\"display:none;\">
                            <!-- 左侧点赞图标 -->
                            <div class=\"sh-zanp-zan-left\"><i class=\"iconfont icon-aixin ri-sxwzlike\"></i></div>
                            <!-- 右边点赞名列表 -->
                            <ul class=\"sh-zanp-zan-right\" id=\"zlbeh-" . $cid . "\">
                            
                            
     </ul>
    </div>
    ";
		}
		echo "
                        <!-- 评论列表 -->";
		$sql3 = "select * from comm where wzcid= '{$cid}' and comaud<>'0' and comaud<>'-1'";
		$result3 = mysqli_query($conn, $sql3);
		$pls = 0;
		if (mysqli_num_rows($result3) > 0) {
			echo "<ul class=\"sh-zanp-pl\" id=\"sh-zanp-pl-" . $cid . "\">";
			while ($row3 = mysqli_fetch_assoc($result3)) {
				$pls = $pls + 1;
				if ($commgs < $pls) {
					$plgd = "display:flex";
					break;
				} else {
					$plgd = "display:none";
				}
				$couser = $row3["couser"];
				$coname = $row3["coname"];
				$courl = $row3["courl"];
				$cotext = $row3["cotext"];
				$bcouser = $row3["bcouser"];
				$bconame = $row3["bconame"];
				$comaud = $row3["comaud"];
				if ($comaud != 1) {
					$cotext = "该条评论未通过审核!";
				}
				if ($courl == "") {
					$plzwze = "";
				} else {
					$plzwze = "href=\"" . $courl . "\" style=\"pointer-events: all;\"";
				}
				if ($commauth == 1) {
					$pldjhf = "onclick=\"plhuifu()\"";
				} else {
					$pldjhf = "";
				}
				if ($bcouser == "false" || $bconame == "false") {
					echo "
            <li lang=\"" . $coname . "\" " . $pldjhf . " id=\"" . $cid . "\" value=\"" . $couser . "\" data-comkzt=\"0\">
                                <div class=\"sh-zanp-pl-n\">
                                    <a " . $plzwze . " class=\"sh-zanp-pl-n-nc\" onclick=\"hfljurl()\" target=\"_blank\">" . $coname . "</a>：
                                    <span class=\"sh-zanp-pl-n-nr\">" . $cotext . "</span>
                                </div>
                            </li>
            ";
				} else {
					echo "
            <li lang=\"" . $coname . "\" " . $pldjhf . " id=\"" . $cid . "\" value=\"" . $couser . "\" data-comkzt=\"0\">
                                <div class=\"sh-zanp-pl-n\">
                                    <a " . $plzwze . " class=\"sh-zanp-pl-n-nc\" onclick=\"hfljurl()\" target=\"_blank\">" . $coname . "</a>
                                    <span>回复</span>
                                    <span class=\"sh-zanp-pl-n-nc\">" . $bconame . "</span>：
                                    <span class=\"sh-zanp-pl-n-nr\">" . $cotext . "</span>
                                </div>
                            </li>
            ";
				}
			}
			?></ul><?php 
		} else {
			$plgd = "display:none";
			echo "<ul class=\"sh-zanp-pl\" id=\"sh-zanp-pl-" . $cid . "\" style=\"display:none;\"></ul>";
		}
		echo "

                        <!-- 显示更多评论 -->
                        <div class=\"sh-zanp-pl-ku\">
                        <a href=\"./view.php?cid=" . $cid . "\" target=\"_blank\" class=\"sh-zanp-pl-gd\" style=\"" . $plgd . "\">
                            <p class=\"zp1\"></p>
                            <p class=\"zp1\"></p>
                            <p></p>
                        </a>
                        </div>

                    </div>
                </div>
            </div>
                    ";
	}
}
$conn->close();
?>            
            </div>


























<?php 
if ($userdlzt == 0) {
	if ($loginkg == 1) {
		?><div class="sh-login" id="sh-login" onclick="gblogin()">
                <form class="sh-login-main" onsubmit="return false;" autocomplete="off" onclick="hfljurl()">
                    <!-- 顶部 -->
                    <div class="sh-login-main-top">
                        <div class="sh-news-main-top-div" onclick="gblogin()"><i class="iconfont icon-quxiao al-sxb2h"></i></div>
                    </div>
                    
                    
                    <div class="sh-login-main-kko">
                    
                    <div class="sh-login-main-kko-kok">
                    <p class="sh-login-main-kko-kok-p" id="zhdzsx">账号登录</p>
                    <!-- 中间 -->
                    <div class="sh-login-main-con">
                    <div class="sh-login-main-con-anu">
                    <p>账号</p>
                        <input type="text" name="zh" id="login-zh" spellcheck="false" minlength="5" maxlength="32" value="" placeholder="账号" onKeyUp="this.value = this.value.replace(/[^\u0000-\u007F]/g, '');">
                    </div>
                    <div class="sh-login-main-con-anu" id="sh-login-main-con-anu" style="display:none;">
                    <p>邮箱</p>
                        <input type="email" name="email" id="login-email" spellcheck="false" maxlength="100"  value="" placeholder="邮箱" pattern="^[a-z0-9]+([._\-]*[a-z0-9])*@([a-z0-9]+[-a-z0-9]*[a-z0-9]+.){1,63}[a-z0-9]+$" title="请输入正确的邮箱格式">
                    </div>
                    <div class="sh-login-main-con-anu">
                    <p>密码</p>
                        <input type="password" name="pwd" id="login-pass" spellcheck="false" minlength="3" maxlength="16" value="" placeholder="密码">
                    </div><?php 
		if ($regverify == "1") {
			?><div class="sh-login-main-con-anu" id="sh-login-main-con-yzmwk" style="display:none;">
                    <p>验证码</p>
                        <input type="password" name="yzm" id="login-yzm" spellcheck="false" minlength="3" maxlength="16" value="" placeholder="验证码" style="margin-right: 5px;">
                        <p style="color: var(--thetitle);margin: 0 30px 0 0;font-size: 14px;cursor: pointer;" id="yzm">发送</p>
                    </div><?php 
		}
		?>

                    <!--div class="sh-login-main-con-anu">
                    <a href="JavaScript:;" onclick="zhmm()">忘记密码</a>
                    </div-->
                    </div>
                    <!-- 底部 -->
                    <div class="sh-login-main-bot">
                        <input type="submit" value="登录" class="sh-right" id="sh-left-dlzc" onclick="logy()">
                    </div>
                    </div>
                    
                    <div class="sh-login-main-bottom">
                    <a href="JavaScript:;" onclick="zhmm()" onkeydown="return checkKeyDown(event)">忘记密码</a><p></p>
                    <a href="/findzh.php">找回账号</a>
                    
                    <?php 
		if ($regqx == 0) {
			?><p></p>
                    <a href="JavaScript:;" id="sh-zck-an" onclick="zcanxy()" onkeydown="return checkKeyDown(event)">注册账号</a><?php 
		}
		?>

                    </div>

                    </div>
                </form>
            </div><?php 
	}
}
echo "














            

";
if ($userdlzt == 1) {
	?><div class="sh-news" id="sh-news" onclick="gbnews()">
                <div class="sh-news-main" id="sh-news-main" onclick="hfljurl()">
                    <!-- 顶部 -->
                    <div class="sh-news-main-top">
                        <div class="sh-news-main-top-xiaoxih"><span>消息盒子</span></div>
                        <div class="sh-news-main-top-div" style="display:none;" id="xxscztqbk" class="sh-news-main-top-img" onclick="xxscztqb()"><i class="iconfont icon-shanchuwenjian ri-sxhsh ri-sxhs" id="xxscztqb"></i></div>
                        
                        <div class="sh-news-main-top-div sh-news-main-top-div2" onclick="js_menu()">
                          <i class="iconfont icon-xialajiantouxiao ri-sxhqx"></i>
                          <div id="js_menu" class="sh-news-main-top-div-menu" style="display: none;">
                            <a href="JavaScript:;" onclick="xxscyd()" class="iconfont icon-icon-09 ri-sxhs">全部已读</a>
                            <a href="JavaScript:;" onclick="xxsczt()" class="iconfont icon-xuanze ri-sxhs" id="xxsczt" lang="0">选择消息</a>
                            <a href="JavaScript:;" onclick="xxscztqb()" class="iconfont icon-shanchu ri-sxhs">删除所有</a>
                          </div>
          
                        </div>
                        <div class="sh-news-main-top-div" onclick="gbnews()"><i class="iconfont icon-quxiao ri-sxhqx"></i></div>
                    </div>
                    <!-- 内容 -->
                    <div class="sh-news-con" id="sh-news-con"><?php 
	include "./config.php";
	$conn = new mysqli($servername, $username, $password, $dbname);
if ($conn->connect_error) {
    exit("连接失败: ". $conn->connect_error);
}

$sqlCount = "SELECT COUNT(*) AS total FROM message where suser = '{$user_zh}'";
$resultCount = $conn->query($sqlCount);
$rowCount = $resultCount->fetch_assoc();
$xxsl = $rowCount['total'];
echo "</div>
        <!-- 内容 end -->
        <!-- 提示 -->
        <div class=\"sh-news-tishi\">
            <P>共<span id=\"xxtzsul\">". $xxsl. "</span>条消息</P>
        </div>
    </div>
</div>";
}
echo "

";
if ($lnkzt == 0) {
	?><div class="sh-link" id="sh-link" onclick="gblink()">
<div class="sh-link-main" id="sh-link-main" onclick="hfljurl()">
    <!-- 顶部 -->
    <div class="sh-link-main-top">
        <div class="sh-news-main-top-xiaoxih"><span>联系人</span></div>
        <!--img src="./assets/img/light.svg" alt="" class="sh-link-main-top-img" onclick="gblink()"-->
        <div class="sh-news-main-top-div" onclick="gblink()"><i class="iconfont icon-quxiao ri-sxhqx"></i></div>
    </div>
    <!-- 内容 -->
    <div class="sh-link-con"><?php 
	include "./config.php";
	$conn = new mysqli($servername, $username, $password, $dbname);
	if ($conn->connect_error) {
		exit("连接失败: " . $conn->connect_error);
	}
	$iy = 0;
	$sqllink = "SELECT * FROM link";
	$resultlink = $conn->query($sqllink);
	while ($rowlink = $resultlink->fetch_assoc()) {
		$iy++;
		$linkurl = $rowlink["url"];
		$linkurls = $rowlink["urls"];
		$linkurlimg = $rowlink["urlimg"];
		echo "<a href=\"" . $linkurl . "\" class=\"sh-link-con-lie\" target=\"_blank\">
                            <!-- 左 -->
                            <div class=\"sh-link-con-lie-left\">
                                <img src=\"./assets/img/thumbnail.svg\" referrerpolicy=\"no-referrer-when-downgrade\" data-src=\"" . $linkurlimg . "\" alt=\"\" class=\"sh-link-con-lie-left-img\">
                            </div>
                            <!-- 右 -->
                            <div class=\"sh-link-con-lie-right\">
                                <p class=\"sh-link-con-lie-right-title\">" . $linkurls . "</p> 
                            </div>
                        </a>";
	}
	$conn->close();
	echo "</div>
    <!-- 内容 end -->
        <!-- 提示 -->
        <div class=\"sh-link-tishi\">
        <P>共" . $iy . "个朋友</P>
        </div>
    </div>
    </div>";
}
?>




            <div class="footer">
                <div class="footer-text" id="footer-text-zt" onclick="hqgd()">点击加载更多..</div>
                <p style="display:none" id="footer-text-hqgd"><?php echo $wzsl;?></p>
            </div>


        </div>


<?php 
if ($search == "1") {
	?><div class="so" id="so" onclick="gbso()">
    <form class="sobd" method="get" action="./index.php?so=" onclick="hfljurl()">
          <input class="sobd-in" maxlength="50" autocomplete="off" placeholder="请输入关键字.." name="so" value="" type="text" required>
          <button class="sobd-bu" type="submit">搜一搜</button>
        </form>
</div><?php 
}
?>



<!--右下角悬浮菜单-->
<div class="sh-menu" id="sh-menu">
    <?php 
if ($search == "1") {
	?>
	
	<div class="sh-menu-k" id="scrtop" onclick="kqso()"><i class="iconfont icon-sousuoxiao"></i></div>
	
	<?php 
}
if ($daymode == "1") {
	$darktheme = $_COOKIE["dark_theme"];
	if ($darktheme == "dark-theme") {
		?><div class="sh-menu-k" id="day" lang="0"><i class="iconfont icon-yueliang" id="day-i"></i></div><?php 
	} else {
		?><div class="sh-menu-k" id="day" lang="1"><i class="iconfont icon-ai250" id="day-i"></i></div><?php 
	}
}
if ($gotop == "1") {
	?><div class="sh-menu-k" id="scrtop" onclick="scrollToTop()"><i class="iconfont icon-weibiaoti-x-copy"></i></div><?php 
}
?></div>

<style>
.sh-menu {
    bottom: 100px !important;
}
</style>

<div class="overlay" id="overlay">
  <div class="modal">
    <div class="modal-header">
      <h4>温馨提示</h4>
      <span class="close" id="closeModal">&times;</span>
    </div>
    <div class="modal-content">
<p><strong>🛸 我~~开始找你了，让我们一起期待四巡吧</strong></p>

<p><strong>🌟 完全免费，无任何收费项目</strong><br>
本站由谦友圈子提供，致力于为大家提供一个纯净、友好的平台。</p>

<p><strong>💼 运营成本由站长全额承担</strong><br>
所有费用均由站长个人承担，确保我们的服务质量始终如一。</p>

<p><strong>⚡ 高速服务器正在筹备中</strong><br>
由于目前服务器成本较高，我们正在积极筹备更高效的解决方案。请您在上传视频/图片时耐心等待，我们会尽快提升体验。</p>

<p><strong>🔍 请遵守社区规则</strong><br>
为了维护良好的圈子环境，请勿发布与谦友圈子无关或违法的内容。感谢您的理解与配合！</p>

    </div>
    <div class="modal-footer">
      <button class="btn-close" id="closeButton">关闭</button>
    </div>
  </div>
</div>

<?php 
if ($musplay == 0) {
	include "./site/musicbk.php";
} elseif ($musplay == 1) {
	include "./site/musicbkcla.php";
}
?>
<div class="sh-copyright">
    <span class="sh-copyright-banquan" id="sh-copyright-banquan"><?php echo $copyright;?></span>&nbsp;
    <?php 
if ($beian != "") {
	echo "<span class=\"sh-copyright-banquan\">" . html_entity_decode($beian) . "</span>";
}
?></div>

</div>

    <script type="text/javascript" id="script-id1">
    var myallkeyVar ="<?php echo $allkey;?>";//获取发文章用的key
    // 获取要删除的 script 标签的引用  
    var scriptTag = document.getElementById("script-id1");  
    // 删除 script 标签  
    scriptTag.parentNode.removeChild(scriptTag);
    </script>
     <script>
        document.addEventListener('DOMContentLoaded', () => {
            const overlay = document.getElementById('overlay');
            const closeButton = document.getElementById('closeButton');
            const openModalButton = document.getElementById('openModal');
            const sixHoursInMillis = 6 * 60 * 60 * 1000; // 6小时的毫秒数

            // 检查本地存储
            function checkPopupStatus() {
                const lastClosed = localStorage.getItem('modalLastClosed');
                if (lastClosed) {
                    const lastClosedTimestamp = parseInt(lastClosed, 10);
                    const timeSinceLastClose = Date.now() - lastClosedTimestamp;

                    if (timeSinceLastClose < sixHoursInMillis) {
                        console.log('Popup should not be displayed.');
                        return; // 如果在6小时内，阻止弹窗显示
                    }
                }
                console.log('Popup should be displayed.');
                overlay.style.display = 'flex'; // 显示弹窗
            }

            // 关闭弹窗并记录时间
            function closeModalPopup() {
                overlay.style.display = 'none';
                localStorage.setItem('modalLastClosed', Date.now().toString());
            }

            // 关闭弹窗事件
            closeButton.addEventListener('click', closeModalPopup);
            overlay.addEventListener('click', closeModalPopup); // 点击遮罩层关闭弹窗

            // 检查弹窗状态
            checkPopupStatus();
        });

    </script>

    <!-- <script charset="UTF-8" id="LA_COLLECT" src="//sdk.51.la/js-sdk-pro.min.js"></script> -->
<script>LA.init({id:"KbK2AGjkzWGBfylr",ck:"KbK2AGjkzWGBfylr"})</script>
    <script type="text/javascript" src="./assets/js/index.js?v=<?php echo $resversion;?>"></script>
    <script type="text/javascript" src="./assets/js/jquery.min.js"></script>
    <script type="text/javascript" src="./assets/mesg/dist/js/sh-noytf.js?v=<?php echo $resversion;?>"></script>
    <script type="text/javascript" src="./assets/js/jquery.fancybox.min.js?v=<?php echo $resversion;?>"></script>
    <?php echo "<script type=\"text/javascript\">" . $filtercujs . "</script>";?> 
    <script>
    $(document).ready(function () {
        $('.floating-rectangle').click(function () {
            // 检查 PHP 变量是否存在
            <?php if(isset($user_zh)){?>
                var username = '<?php echo $user_zh;?>';
                var encryptedUsername = btoa(username);
                window.location.href = 'https://chat.aizy.site/index/index/groupchat.html?id=2&username=' + encryptedUsername;
            <?php } else {?>
            warnpop('请先登录');
            <?php }?>
        });
    });

</script>

    <!-- 底部填充，避免内容被底部菜单遮挡 -->
    <div class="footer-padding"></div>
    
    <!-- 底部菜单 -->
    <div class="footer-menu">
        <a href="./index.php" class="footer-menu-item active">
            <i class="ri-home-4-fill"></i>
            <span>首页</span>
        </a>
        <a href="./discover.php" class="footer-menu-item">
            <i class="ri-compass-3-line"></i>
            <span>发现</span>
        </a>
        <a href="./notify.php" class="footer-menu-item">
            <i class="ri-notification-2-line"></i>
            <span>通知</span>
            <?php
            // 检查是否有未读通知
            if ($userdlzt == 1) {
                $sql_unread = "SELECT COUNT(*) as count FROM notifications WHERE user_to = '{$user_zh}' AND is_read = 0";
                $result_unread = $conn->query($sql_unread);
                $unread_count = 0;
                
                if ($result_unread && $row_unread = $result_unread->fetch_assoc()) {
                    $unread_count = $row_unread['count'];
                }
                
                if ($unread_count > 0) {
                    echo '<div class="notification-dot"></div>';
                }
            }
            ?>
        </a>
        <a href="./home.php" class="footer-menu-item">
            <i class="ri-user-3-line"></i>
            <span>我的</span>
        </a>
    </div>

   <script>
   
<!--         const isInWeChatMiniProgram =!!window.__wxjs_environment && window.__wxjs_environment === 'miniprogram';-->

<!--    if (!isInWeChatMiniProgram) {-->


    
<!--let ignorePopState = false; // 标志位，控制是否忽略 popstate 事件-->

<!--// 保存页面当前的滚动位置-->
<!--function saveScrollPosition() {-->
<!--    return {-->
<!--        x: window.pageXOffset || document.documentElement.scrollLeft,-->
<!--        y: window.pageYOffset || document.documentElement.scrollTop-->
<!--    };-->
<!--}-->

<!--// 恢复页面的滚动位置-->
<!--function restoreScrollPosition(scrollPos) {-->
<!--    window.scrollTo(scrollPos.x, scrollPos.y);-->
<!--}-->

<!--// 初始化时添加一个虚拟的历史记录状态-->
<!--history.pushState(null, null, window.location.href);-->

<!--// 监听 Fancybox 关闭事件，关闭时禁用 popstate 事件-->
<!--$(document).on('beforeClose.fb', function () {-->
<!--    ignorePopState = true;-->
<!--    let scrollPos = saveScrollPosition(); // 保存当前滚动位置-->

<!--    // 延时后恢复 popstate 事件处理-->
<!--    setTimeout(function () {-->
<!--        ignorePopState = false;-->
<!--        restoreScrollPosition(scrollPos);  // 恢复滚动位置-->
<!--    }, 100);-->
<!--});-->

<!--// 弹窗提醒用户并自动跳转-->
<!--const domains = [-->
<!--    "wonderlandescape.aizy.site",-->
<!--    "brilliantmornings.aizy.site",-->
<!--    "digitaldreamland.aizy.site",-->
<!--    "magicmemorybox.aizy.site",-->
<!--    "colorfulworlds.aizy.site",-->
<!--    "ydy.aizy.site"-->
<!--];-->

<!--function showExitReminder() {-->
<!--    alert("谦友您好，先别走，可以耽误两分钟帮忙刷下广告赞助嘛，感谢您的支持！");-->
<!--    const randomIndex = Math.floor(Math.random() * domains.length);-->
<!--    const randomDomain = `https://${domains[randomIndex]}/ydy`;-->
<!--    setTimeout(function() {-->
<!--        window.location.href = randomDomain;-->
<!--    }, 1000); // 2 秒后跳转-->
<!--}-->

<!--// popstate 事件监听器-->
<!--window.addEventListener('popstate', function (event) {-->
<!--    if (!ignorePopState) {-->
<!--        showExitReminder();-->
<!--    } else {-->
<!--        let scrollPos = saveScrollPosition();  // 保存滚动位置-->
<!--        history.pushState(null, null, window.location.href);-->
<!--        restoreScrollPosition(scrollPos);      // 恢复滚动位置-->
<!--    }-->
<!--});-->

<!--// hashchange 事件监听器，处理某些手机浏览器上的返回操作-->
<!--window.addEventListener('hashchange', function () {-->
<!--    if (!ignorePopState) {-->
<!--        showExitReminder();-->
<!--    }-->
<!--});-->

<!--// 手动设置 hash 值以确保在手机端的 hashchange 事件能被触发-->
<!--let scrollPos = saveScrollPosition(); // 保存滚动位置-->
<!--history.pushState(null, null, '#');  // 执行 hash 改变-->
<!--restoreScrollPosition(scrollPos);    // 恢复滚动位置-->

    
<!--    }-->
    
    
   
// 初始化变量
    var dragging = false;
    var startX, startY, rectLeft, rectTop;

    // 鼠标按下事件处理
    $(".floating-rectangle").mousedown(function (e) {
      dragging = true;
      startX = e.pageX;
      startY = e.pageY;
      rectLeft = parseInt($(this).css("left")) || 0;
      rectTop = parseInt($(this).css("top")) || 0;
    });

    // 鼠标移动事件处理
    $(document).mousemove(function (e) {
      if (dragging) {
        var newLeft = rectLeft + e.pageX - startX;
        var newTop = rectTop + e.pageY - startY;
        $(".floating-rectangle").css({
          left: newLeft + "px",
          top: newTop + "px",
        });
      }
    });

    // 鼠标松开事件处理
    $(document).mouseup(function () {
      dragging = false;
    });

    // 矩形点击事件处理
    
        $("#phb").click(function () {
        window.location.href = './phb.php';
    });
    
    <!--$(".floating-rectangle").click(function () {-->
      <!--alert("谦友您好，进入广告后等几秒返回即可，感谢支持。");-->
      <!--setTimeout(function () {-->
    <!--    window.location.href = 'https://ydy.aizy.site/ydy';-->
      <!--}, 1000);-->
    <!--});-->
</script>
    <script>
    

 document.getElementById('sign').addEventListener('click', function () {
 
  window.location.href = '/sign.php';
        });

       let page = 1;
        let isLoading = false;
        let reachedBottom = false;
        let timer;
        const newsConDiv = $('#sh-news-con');
        const loadMoreData = () => {
            if (!isLoading) {
                isLoading = true;
                $.ajax({
                    url: 'api/jzmess.php?page=' + page,
                    success: function (data) {
                        newsConDiv.append(data);
                        page++;
                        loaddemand();
                        isLoading = false;
                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        console.error('AJAX request error:', textStatus, errorThrown);
                        isLoading = false;
                    }
                });
            }
        };

        newsConDiv.scroll(function () {
            const scrollTop = newsConDiv.scrollTop();
            const height = newsConDiv.height();
            const scrollHeight = newsConDiv[0].scrollHeight;
            if (scrollTop + height >= scrollHeight - 100) {
                reachedBottom = true;
            } else {
                reachedBottom = false;
            }
            if (reachedBottom &&!isLoading) {
                clearTimeout(timer);
                timer = setTimeout(loadMoreData, 500);
            }
        });

        // 初始加载第一页数据
        loadMoreData();
    </script>
    
    <!-- 底部填充，避免内容被底部菜单遮挡 -->
    <div class="footer-padding"></div>
    
    <!-- 底部菜单 -->
    <div class="footer-menu">
        <a href="./index.php" class="footer-menu-item active">
            <i class="ri-home-4-fill"></i>
            <span>首页</span>
        </a>
        <a href="./discover.php" class="footer-menu-item">
            <i class="ri-compass-3-line"></i>
            <span>发现</span>
        </a>
        <a href="./notify.php" class="footer-menu-item">
            <i class="ri-notification-2-line"></i>
            <span>通知</span>
            <?php
            // 检查是否有未读通知
            if ($userdlzt == 1) {
                $sql_unread = "SELECT COUNT(*) as count FROM notifications WHERE user_to = '{$user_zh}' AND is_read = 0";
                $result_unread = $conn->query($sql_unread);
                $unread_count = 0;
                
                if ($result_unread && $row_unread = $result_unread->fetch_assoc()) {
                    $unread_count = $row_unread['count'];
                }
                
                if ($unread_count > 0) {
                    echo '<div class="notification-dot"></div>';
                }
            }
            ?>
        </a>
        <a href="./home.php" class="footer-menu-item">
            <i class="ri-user-3-line"></i>
            <span>我的</span>
        </a>
    </div>
</body>
</html><?php 
function ReckonTime($time)
{
	$NowTime = time();
	if ($NowTime < $time) {
		return false;
	}
	$TimePoor = $NowTime - $time;
	if ($TimePoor == 0) {
		$str = "一眨眼之间";
	} elseif ($TimePoor < 60 && $TimePoor > 0) {
		$str = $TimePoor . "秒之前";
	} elseif ($TimePoor >= 60 && $TimePoor <= 3600) {
		$str = floor($TimePoor / 60) . "分钟前";
	} elseif ($TimePoor > 3600 && $TimePoor <= 86400) {
		$str = floor($TimePoor / 3600) . "小时前";
	} elseif ($TimePoor > 86400 && $TimePoor <= 604800) {
		if (floor($TimePoor / 86400) == 1) {
			$str = "昨天";
		} elseif (floor($TimePoor / 86400) == 2) {
			$str = "前天";
		} else {
			$str = floor($TimePoor / 86400) . "天前";
		}
	} elseif ($TimePoor > 604800) {
		$str = date("Y-m-d", $time);
	}
	return $str;
}
?> 

<script type="text/javascript">
// 收藏功能
function toggleFavorite(element) {
    var cid = element.id;
    var favImg = document.getElementById('favimg-' + cid);
    var favText = document.getElementById('favtext-' + cid);
    var favButton = document.getElementById(cid);
    
    // 检查用户是否登录
    if (<?php echo $userdlzt; ?> == 0) {
        kqlogin(); // 打开登录框
        return;
    }
    
    // 发送收藏请求
    var xhr = new XMLHttpRequest();
    xhr.open('GET', './api/favorite.php?cid=' + cid, true);
    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            if (xhr.status === 200) {
                var response = JSON.parse(xhr.responseText);
                if (response[0].code === "200") {
                    if (response[0].status === "favorite") {
                        // 收藏成功
                        favButton.classList.add('active');
                        favText.innerText = "已收藏";
                        // 显示提示
                        shnotf("收藏成功", "success");
                    } else {
                        // 取消收藏
                        favButton.classList.remove('active');
                        favText.innerText = "收藏";
                        // 显示提示
                        shnotf("已取消收藏", "info");
                    }
                } else {
                    shnotf(response[0].msg, "error");
                }
            } else {
                shnotf("请求失败，请稍后再试", "error");
            }
        }
    };
    xhr.send();
}

// 页面加载时检查收藏状态
document.addEventListener('DOMContentLoaded', function() {
    if (<?php echo $userdlzt; ?> == 1) {
        // 获取所有帖子ID
        var postElements = document.querySelectorAll('.sh-content-right-time-right-left-s');
        var postIds = [];
        
        postElements.forEach(function(element) {
            postIds.push(element.id);
        });
        
        if (postIds.length > 0) {
            // 检查收藏状态
            var xhr = new XMLHttpRequest();
            xhr.open('GET', './api/check_favorites.php?cids=' + postIds.join(','), true);
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4 && xhr.status === 200) {
                    var response = JSON.parse(xhr.responseText);
                    if (response.code === "200") {
                        var favorites = response.data;
                        favorites.forEach(function(cid) {
                            var favButton = document.getElementById(cid);
                            var favText = document.getElementById('favtext-' + cid);
                            if (favButton && favText) {
                                favButton.classList.add('active');
                                favText.innerText = "已收藏";
                            }
                        });
                    }
                }
            };
            xhr.send();
        }
    }
});
</script>