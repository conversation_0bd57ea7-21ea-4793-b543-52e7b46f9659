<?php

if ($_SERVER["REQUEST_METHOD"] === "POST") {
    // 响应数组
    $response = [];

    // 验证邮箱输入
    if (empty($_POST["email"])) {
        $response[] = ["code" => "400", "msg" => "邮箱不能为空"];
        exit(json_encode($response, JSON_UNESCAPED_UNICODE));
    }

    $email = addslashes(htmlspecialchars(trim($_POST["email"])));

    // 从邮箱中提取前缀
    $email_prefix = substr($email, 0, strpos($email, '@'));
$iteace = "0";
    // 引入数据库连接
    require "./config.php";

require "./api/wz.php";

    // 查询包含邮箱前缀的用户名
    $sql = "SELECT username FROM user WHERE email LIKE '%{$email_prefix}%'";
    $result = $conn->query($sql);

    // 检查是否找到用户名
    if ($result && mysqli_num_rows($result) > 0) {
        $usernames = [];
        while ($row = mysqli_fetch_assoc($result)) {
            $usernames[] = $row['username'];
        }
        $response[] = ["code" => "200", "msg" => "找到账号", "usernames" => $usernames];
    } else {
        $response[] = ["code" => "404", "msg" => "未找到与该邮箱相关的账号"];
    }

    // 返回响应
    exit(json_encode($response, JSON_UNESCAPED_UNICODE));
}

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>找回账号 - <?php echo $name;?></title>
    <meta name="keywords" content="<?php echo $name;?>">
    <meta name="description" content="<?php echo $name . "," . $subtitle;?>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no">
    <link rel="stylesheet" type="text/css" href="./assets/css/style.css?v=<?php echo $resversion;?>">
    <style>
        /* CSS样式 */
        html, body {
            height: 100%; /* 设置 html 和 body 的高度为 100% */
            margin: 0; /* 去掉默认 margin */
            display: flex; /* 设置 body 为 Flexbox 容器 */
            justify-content: center; /* 水平居中 */
            align-items: center; /* 垂直居中 */
            background-color: #f4f4f4; /* 背景颜色 */
            color: #333; /* 文本颜色 */
        }

        .content {
            max-width: 400px; /* 设置内容的最大宽度 */
            width: 100%; /* 内容的宽度为 100% */
            padding: 20px; /* 内容的内边距 */
            background: #fff; /* 背景颜色 */
            border-radius: 8px; /* 圆角边框 */
            box-shadow: none; /* 去掉阴影效果，保持扁平化设计 */
        }

        h1 {
            text-align: center;
            margin-bottom: 20px;
            color: #007BFF;
            font-size: 24px; /* 增大标题字体 */
        }

        form {
            display: flex;
            flex-direction: column; /* 垂直排列 */
            gap: 15px; /* 输入框和按钮之间的间距 */
        }

        label {
            margin-bottom: 5px;
            font-weight: bold;
            text-align: left; /* 标签左对齐 */
        }

        input[type="email"],
        input[type="submit"] {
            padding: 15px; /* 增加内边距 */
            border: 1px solid #ccc;
            border-radius: 5px;
            font-size: 16px;
           
            box-shadow: none; /* 去掉阴影效果，保持扁平化设计 */
        }

        input[type="email"]:focus {
            border-color: #007BFF; /* 输入框聚焦时的边框颜色 */
            outline: none; /* 去掉聚焦时的外边框 */
        }

        input[type="submit"] {
            background-color: #007BFF; /* 按钮背景颜色 */
            color: #fff; /* 按钮文本颜色 */
            border: none; /* 去掉按钮边框 */
            cursor: pointer; /* 鼠标悬停时为手型 */
        }

        input[type="submit"]:hover {
            background-color: #0056b3; /* 鼠标悬停时按钮颜色变化 */
        }

        #result {
            margin-top: 20px;
            font-size: 18px;
            text-align: center;
            color: #FF5733; /* 提示信息颜色 */
        }

        @media (max-width: 600px) {
            .content {
                padding: 15px;
                margin: 10px;
            }

            h1 {
                font-size: 24px;
            }

            input[type="email"],
            input[type="submit"] {
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="content">
        
        <h1>找回账号</h1>
        <form method="POST" id="recovery-form">
            <label for="email">请输入您的邮箱：</label>
            <input type="email" name="email" id="email" required placeholder="输入绑定的邮箱">
            <input type="submit" value="查找账号">
            <p style="font-size: 12px;color: #999;">如果不记得具体邮箱 写出大概邮箱即可</p>
        </form>
        <div id="result"></div>
    </div>

   <script>
    document.getElementById('recovery-form').onsubmit = function(e) {
        e.preventDefault(); // 阻止表单的默认提交

        // 获取邮箱输入
        const email = document.getElementById('email').value;

        // 发送 AJAX 请求到服务器
        fetch('', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: new URLSearchParams({ email: email })
        })
        .then(response => response.json())
        .then(data => {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = ''; // 清空之前的结果

            if (data.length > 0) {
                if (data[0].code === "200") {
                    // 把账号展示为可点击的，并添加点击复制功能
                    let usernames = data[0].usernames.map(username => 
                        `<span class="username" onclick="copyToClipboard('${username}')">${username}</span>`
                    ).join(', ');

                    resultDiv.innerHTML = `
                        <p>您的账号为：${usernames}</p>
                        <p style="font-size: 12px; color: #888;    margin-top: 5px;">点击账号复制到剪贴板</p>
                        <a href="/" style="
                            color: #fff;
                            font-size: 14px;
                            background: #00b399;
                            padding: 10px 5px;
                            margin: 10px auto;
                            display: block;
                            width: 100px;
                            text-align: center;
                            border-radius: 5px;
                            text-decoration: none;
                        ">点击登录</a>
                    `;
                } else {
                    resultDiv.innerHTML = `<p>${data[0].msg}</p>`;
                }
            }
        })
        .catch(error => console.error('错误:', error));
    };

    // 复制到剪贴板的功能
    function copyToClipboard(text) {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text).then(() => {
                alert(`已复制账号：${text}`);
            }).catch(err => {
                console.error('复制失败:', err);
            });
        } else {
            // 兼容旧设备的备用方法
            const tempInput = document.createElement("input");
            tempInput.value = text;
            document.body.appendChild(tempInput);
            tempInput.select();
            document.execCommand("copy");
            document.body.removeChild(tempInput);
            alert(`已复制账号：${text}`);
        }
    }
</script>

</body>
</html>
