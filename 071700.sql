-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 4.8.5
-- https://www.phpmyadmin.net/
--
-- 主机： localhost
-- 生成日期： 2025-05-21 04:59:50
-- 服务器版本： 5.7.26
-- PHP 版本： 7.1.9

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET AUTOCOMMIT = 0;
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- 数据库： `071700`
--

-- --------------------------------------------------------

--
-- 表的结构 `admin`
--

CREATE TABLE `admin` (
  `id` int(6) UNSIGNED NOT NULL,
  `name` text NOT NULL COMMENT '站点名称',
  `keywords` text NOT NULL COMMENT '站点关键词',
  `subtitle` text NOT NULL COMMENT '站点介绍',
  `icon` text NOT NULL COMMENT 'icon图标',
  `logo` text NOT NULL COMMENT 'logo图标',
  `zt` text NOT NULL COMMENT '站点状态 1开 0关',
  `username` text NOT NULL COMMENT '管理员账号',
  `homimg` text NOT NULL COMMENT '顶部背景图',
  `sign` text NOT NULL COMMENT '签名(不设置则为空)',
  `music` text NOT NULL COMMENT '网站音乐(-1为不设置)',
  `essgs` text NOT NULL COMMENT '文章输出数量',
  `commgs` text NOT NULL COMMENT '每篇文章的评论输出数量',
  `lnkzt` text NOT NULL COMMENT '是否开启友链显示 0=是 1=否',
  `regqx` text NOT NULL COMMENT '是否开启用户注册 0=开 -1=关',
  `regnb` text NOT NULL COMMENT '单个IP最大用户注册数',
  `kqsy` text NOT NULL COMMENT '是否开启所有用户发布权限(0=关 1=开)',
  `comaud` text NOT NULL COMMENT '评论是否需要审核(0=不需要 1=需要)',
  `ptpaud` text NOT NULL COMMENT '发布文章是否需要审核(0=不需要 1=需要)',
  `ptpfan` text NOT NULL COMMENT '是否显示前台发布文章按钮(0=不显示 1=显示)',
  `loginkg` text NOT NULL COMMENT '是否显示前台登录按钮(0=不显示 1=显示)',
  `notname` text NOT NULL COMMENT '是否开启匿名发布(0=关闭 1=开启)',
  `imgpres` text NOT NULL COMMENT '是否开启文章图片压缩(0=不压缩 1=压缩)',
  `rosdomain` text NOT NULL COMMENT '是否开启跨域资源(0=不开启 1=开启)',
  `daymode` text NOT NULL COMMENT '是否开启日夜模式(0=不开启 1=开启)',
  `gotop` text NOT NULL COMMENT '是否开启回到顶部(0=不开启 1=开启)',
  `search` text NOT NULL COMMENT '是否开启搜索功能(0=不开启 1=开启)',
  `videoauplay` text NOT NULL COMMENT '是否开启视频自动播放(0=不开启 1=开启)',
  `regverify` text NOT NULL COMMENT '是否开启注册验证邮箱(0=不开启 1=开启)',
  `pagepass` text NOT NULL COMMENT '网站访问密码 为空则无需密码',
  `emydz` text NOT NULL COMMENT '邮箱服务器地址',
  `emssl` text NOT NULL COMMENT '邮箱SSL加密方式',
  `emduk` text NOT NULL COMMENT 'SMTP端口',
  `emkey` text NOT NULL COMMENT '邮箱授权码',
  `emzh` text NOT NULL COMMENT 'SMTP账号',
  `emfs` text NOT NULL COMMENT '发送邮箱',
  `emfszm` text NOT NULL COMMENT '发送者名称',
  `date` text NOT NULL COMMENT '安装时间',
  `copyright` text NOT NULL COMMENT '版权所属',
  `beian` text NOT NULL COMMENT '备案号(没有则为空)',
  `topes` text NOT NULL COMMENT '置顶文文章',
  `scfont` text NOT NULL COMMENT '网站字体',
  `viscomm` text NOT NULL COMMENT '游客评论(-1=关 0=开)',
  `plsl` text NOT NULL COMMENT '单个IP每分钟最大评论数',
  `musplay` text NOT NULL COMMENT '悬浮音乐播放器样式'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- 表的结构 `banner`
--

CREATE TABLE `banner` (
  `id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL COMMENT '轮播图标题',
  `image_url` text NOT NULL COMMENT '图片地址',
  `link_url` text COMMENT '跳转链接',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序顺序',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：1启用，0禁用',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- 表的结构 `banners`
--

CREATE TABLE `banners` (
  `id` int(11) NOT NULL,
  `title` varchar(100) NOT NULL,
  `image_url` varchar(255) NOT NULL,
  `link_url` varchar(255) DEFAULT NULL,
  `sort_order` int(11) NOT NULL DEFAULT '0',
  `status` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- 表的结构 `checkins`
--

CREATE TABLE `checkins` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `checkin_date` date NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- 表的结构 `comm`
--

CREATE TABLE `comm` (
  `id` int(6) UNSIGNED NOT NULL,
  `couser` text NOT NULL COMMENT '评论者账号',
  `coimg` text NOT NULL COMMENT '评论者头像',
  `coname` text NOT NULL COMMENT '评论者昵称',
  `courl` text NOT NULL COMMENT '评论者网址',
  `cotext` text NOT NULL COMMENT '评论内容',
  `bcouser` text NOT NULL COMMENT '被评论者账号(没有被评论者则填false)',
  `bconame` text NOT NULL COMMENT '被评论者昵称(没有被评论者则填false)',
  `comaud` text NOT NULL COMMENT '评论审核状态(0=未审核 1=已审核)',
  `cotime` text NOT NULL COMMENT '评论时间',
  `ip` text NOT NULL COMMENT '评论ip',
  `wzcid` text NOT NULL COMMENT '评论所属文章',
  `ecid` text NOT NULL COMMENT '评论cid'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- 表的结构 `configx`
--

CREATE TABLE `configx` (
  `id` int(6) UNSIGNED NOT NULL,
  `title` text NOT NULL COMMENT '配置名称',
  `text` text NOT NULL COMMENT '配置信息'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- 表的结构 `emojis`
--

CREATE TABLE `emojis` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL COMMENT '上传用户ID',
  `file_path` varchar(255) NOT NULL COMMENT '表情包文件路径',
  `tags` text COMMENT '表情包标签，多个用逗号分隔',
  `used_count` int(11) NOT NULL DEFAULT '0' COMMENT '使用次数',
  `download_count` int(11) NOT NULL DEFAULT '0' COMMENT '下载次数',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1=正常，0=已禁用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='表情包表' ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- 表的结构 `emoji_downloads`
--

CREATE TABLE `emoji_downloads` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL COMMENT '下载用户ID',
  `emoji_id` int(11) NOT NULL COMMENT '表情包ID',
  `download_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '下载时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='表情包下载记录表' ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- 表的结构 `emoji_favorites`
--

CREATE TABLE `emoji_favorites` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `emoji_id` int(11) NOT NULL COMMENT '表情包ID',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '收藏时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='表情包收藏表' ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- 表的结构 `favorites`
--

CREATE TABLE `favorites` (
  `id` int(11) NOT NULL,
  `userid` varchar(255) NOT NULL COMMENT '用户账号',
  `cid` varchar(255) NOT NULL COMMENT '收藏的内容ID',
  `addtime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '收藏时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户收藏表' ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- 表的结构 `essay`
--

CREATE TABLE `essay` (
  `id` int(6) UNSIGNED NOT NULL,
  `ptpuser` text NOT NULL COMMENT '发布者账号',
  `ptpimg` text NOT NULL COMMENT '发布者头像',
  `ptpname` text NOT NULL COMMENT '发布者昵称',
  `ptptext` text NOT NULL COMMENT '文章内容',
  `ptpimag` text NOT NULL COMMENT '文章图片',
  `ptpvideo` text NOT NULL COMMENT '文章视频',
  `ptpmusic` text NOT NULL COMMENT '文章音乐',
  `ptplx` text NOT NULL COMMENT '文章类型(img=图文 video=视频 music=音乐 only=仅文字)',
  `ptpdw` text NOT NULL COMMENT '文章发布时定位',
  `ptptime` text NOT NULL COMMENT '文章发布时间',
  `ptpgg` text NOT NULL COMMENT '文章是否为广告(0=不是 1=是)',
  `ptpggurl` text NOT NULL COMMENT '广告跳转链接',
  `ptpys` text NOT NULL COMMENT '文章是否可见(0=不可见 1=可见)',
  `commauth` text NOT NULL COMMENT '是否允许评论(0=关 1=开)',
  `ptpaud` text NOT NULL COMMENT '审核状态(0=未审核 1=已审核)',
  `ip` text NOT NULL COMMENT '文章发布时的ip',
  `cid` text NOT NULL COMMENT '文章cid',
  `iszhiding` int(11) NOT NULL,
  `zhidingtime` text NOT NULL,
  `ptpuid` text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- 表的结构 `lcke`
--

CREATE TABLE `lcke` (
  `id` int(6) UNSIGNED NOT NULL,
  `luser` text NOT NULL COMMENT '点赞者账号',
  `lname` text NOT NULL COMMENT '点赞者昵称',
  `limg` text NOT NULL COMMENT '点赞者头像',
  `lwz` text NOT NULL COMMENT '点赞所属文章',
  `ltime` text NOT NULL COMMENT '点赞时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- 表的结构 `link`
--

CREATE TABLE `link` (
  `id` int(6) UNSIGNED NOT NULL,
  `url` text NOT NULL COMMENT '友链地址',
  `urls` text NOT NULL COMMENT '友链说明',
  `urlimg` text NOT NULL COMMENT '友链图标'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- 表的结构 `mall_categories`
--

CREATE TABLE `mall_categories` (
  `id` int(11) NOT NULL,
  `name` varchar(50) NOT NULL COMMENT '分类名称',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序顺序',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1=启用，0=禁用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分商城分类表' ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- 表的结构 `mall_orders`
--

CREATE TABLE `mall_orders` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `product_id` int(11) NOT NULL COMMENT '商品ID',
  `points` int(11) NOT NULL COMMENT '消费积分',
  `address` varchar(255) DEFAULT NULL COMMENT '收货地址',
  `phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '订单状态：pending=处理中，completed=已完成，cancelled=已取消，shipped=已发货',
  `admin_remark` text COMMENT '管理员备注',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分商城订单表' ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- 表的结构 `mall_products`
--

CREATE TABLE `mall_products` (
  `id` int(11) NOT NULL,
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `name` varchar(100) NOT NULL COMMENT '商品名称',
  `description` text COMMENT '商品描述',
  `image_url` varchar(255) DEFAULT NULL COMMENT '商品图片URL',
  `points` int(11) NOT NULL COMMENT '所需积分',
  `stock` int(11) DEFAULT '-1' COMMENT '库存数量，-1表示不限',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1=上架，0=下架',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序顺序',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分商城商品表' ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- 表的结构 `message`
--

CREATE TABLE `message` (
  `id` int(6) UNSIGNED NOT NULL,
  `fuser` text NOT NULL COMMENT '发送者账号',
  `fimg` text NOT NULL COMMENT '发送者头像',
  `fname` text NOT NULL COMMENT '发送者昵称',
  `suser` text NOT NULL COMMENT '接收者账号',
  `title` text NOT NULL COMMENT '消息标题',
  `text` text NOT NULL COMMENT '消息内容',
  `ftime` text NOT NULL COMMENT '发送时间',
  `msg` text NOT NULL COMMENT '消息状态 0=未读 1=已读 -1=已删除'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- 表的结构 `messages`
--

CREATE TABLE `messages` (
  `id` int(11) NOT NULL,
  `sender_id` varchar(255) DEFAULT NULL,
  `receiver_id` varchar(255) DEFAULT NULL,
  `content` text,
  `is_read` tinyint(1) DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- 表的结构 `notifications`
--

CREATE TABLE `notifications` (
  `id` int(11) NOT NULL,
  `user_from` varchar(255) DEFAULT NULL,
  `user_to` varchar(255) DEFAULT NULL,
  `message` text,
  `type` varchar(50) DEFAULT NULL,
  `content_id` int(11) DEFAULT NULL,
  `is_read` tinyint(1) DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- 表的结构 `orders`
--

CREATE TABLE `orders` (
  `id` int(11) NOT NULL,
  `pid` int(11) NOT NULL,
  `trade_no` varchar(255) NOT NULL,
  `out_trade_no` varchar(255) NOT NULL,
  `type` varchar(20) NOT NULL,
  `name` varchar(255) NOT NULL,
  `money` decimal(10,2) NOT NULL,
  `trade_status` varchar(20) NOT NULL,
  `sign` varchar(255) NOT NULL,
  `sign_type` varchar(10) NOT NULL,
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- 表的结构 `sign`
--

CREATE TABLE `sign` (
  `id` int(11) NOT NULL,
  `name` text NOT NULL,
  `time` text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- 表的结构 `user`
--

CREATE TABLE `user` (
  `id` int(6) UNSIGNED NOT NULL,
  `username` text NOT NULL COMMENT '账号',
  `password` text NOT NULL COMMENT '密码',
  `email` text NOT NULL COMMENT '邮箱',
  `name` text NOT NULL COMMENT '昵称',
  `img` text NOT NULL COMMENT '头像',
  `url` text NOT NULL COMMENT '网址',
  `homeimg` text NOT NULL COMMENT '主页背景图(-1则不设置)',
  `sign` text NOT NULL COMMENT '签名(不设置则为空)',
  `essqx` text NOT NULL COMMENT '是否拥有发布权限(0=否 1=是)',
  `esseam` text NOT NULL COMMENT '收到消息是否发送邮件通知(0=否 1=是)',
  `regtime` text NOT NULL COMMENT '注册时间',
  `regip` text NOT NULL COMMENT '注册ip',
  `logtime` text NOT NULL COMMENT '最后登录时间',
  `logip` text NOT NULL COMMENT '最后登录ip',
  `ban` text NOT NULL COMMENT '账号是否被封禁(0=正常 -1=封禁)',
  `bantime` text NOT NULL COMMENT '账号解封时间(true=永久封禁，否则填写解封日期)',
  `passid` text NOT NULL COMMENT '账号唯一id标识',
  `jifen` int(11) NOT NULL,
  `signtime` varchar(255) NOT NULL,
  `points` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- 表的结构 `user_stats`
--

CREATE TABLE `user_stats` (
  `user_id` int(11) NOT NULL,
  `consecutive_days` int(11) NOT NULL DEFAULT '0',
  `total_checkins` int(11) NOT NULL DEFAULT '0',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- 表的结构 `view_history`
--

CREATE TABLE `view_history` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `post_id` int(11) NOT NULL,
  `view_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `deleted` tinyint(1) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;

--
-- 转储表的索引
--

--
-- 表的索引 `admin`
--
ALTER TABLE `admin`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `banner`
--
ALTER TABLE `banner`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `banners`
--
ALTER TABLE `banners`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `checkins`
--
ALTER TABLE `checkins`
  ADD PRIMARY KEY (`id`) USING BTREE,
  ADD UNIQUE KEY `user_date` (`user_id`,`checkin_date`) USING BTREE,
  ADD KEY `checkin_date` (`checkin_date`) USING BTREE;

--
-- 表的索引 `comm`
--
ALTER TABLE `comm`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `configx`
--
ALTER TABLE `configx`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `emojis`
--
ALTER TABLE `emojis`
  ADD PRIMARY KEY (`id`) USING BTREE,
  ADD KEY `idx_user` (`user_id`) USING BTREE,
  ADD KEY `idx_status` (`status`) USING BTREE,
  ADD KEY `idx_created` (`created_at`) USING BTREE;

--
-- 表的索引 `emoji_downloads`
--
ALTER TABLE `emoji_downloads`
  ADD PRIMARY KEY (`id`) USING BTREE,
  ADD UNIQUE KEY `user_emoji` (`user_id`,`emoji_id`) USING BTREE,
  ADD KEY `idx_user` (`user_id`) USING BTREE,
  ADD KEY `idx_emoji` (`emoji_id`) USING BTREE,
  ADD KEY `idx_time` (`download_time`) USING BTREE;

--
-- 表的索引 `emoji_favorites`
--
ALTER TABLE `emoji_favorites`
  ADD PRIMARY KEY (`id`) USING BTREE,
  ADD UNIQUE KEY `user_emoji` (`user_id`,`emoji_id`) USING BTREE,
  ADD KEY `idx_user` (`user_id`) USING BTREE,
  ADD KEY `idx_emoji` (`emoji_id`) USING BTREE;

--
-- 表的索引 `essay`
--
ALTER TABLE `essay`
  ADD PRIMARY KEY (`id`) USING BTREE,
  ADD KEY `id` (`id`) USING BTREE,
  ADD KEY `id_2` (`id`) USING BTREE;

--
-- 表的索引 `lcke`
--
ALTER TABLE `lcke`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `link`
--
ALTER TABLE `link`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `mall_categories`
--
ALTER TABLE `mall_categories`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `mall_orders`
--
ALTER TABLE `mall_orders`
  ADD PRIMARY KEY (`id`) USING BTREE,
  ADD KEY `idx_user` (`user_id`) USING BTREE,
  ADD KEY `idx_product` (`product_id`) USING BTREE,
  ADD KEY `idx_status` (`status`) USING BTREE,
  ADD KEY `idx_created` (`created_at`) USING BTREE;

--
-- 表的索引 `mall_products`
--
ALTER TABLE `mall_products`
  ADD PRIMARY KEY (`id`) USING BTREE,
  ADD KEY `idx_category` (`category_id`) USING BTREE,
  ADD KEY `idx_status` (`status`) USING BTREE;

--
-- 表的索引 `message`
--
ALTER TABLE `message`
  ADD PRIMARY KEY (`id`),
  ADD KEY `id` (`id`),
  ADD KEY `id_2` (`id`),
  ADD KEY `id_3` (`id`);

--
-- 表的索引 `messages`
--
ALTER TABLE `messages`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `orders`
--
ALTER TABLE `orders`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `sign`
--
ALTER TABLE `sign`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `id` (`id`);

--
-- 表的索引 `user`
--
ALTER TABLE `user`
  ADD PRIMARY KEY (`id`) USING BTREE,
  ADD KEY `id` (`id`) USING BTREE,
  ADD KEY `id_2` (`id`) USING BTREE;

--
-- 表的索引 `user_stats`
--
ALTER TABLE `user_stats`
  ADD PRIMARY KEY (`user_id`) USING BTREE;

--
-- 表的索引 `view_history`
--
ALTER TABLE `view_history`
  ADD PRIMARY KEY (`id`) USING BTREE,
  ADD UNIQUE KEY `unique_view` (`user_id`,`post_id`) USING BTREE,
  ADD KEY `user_idx` (`user_id`) USING BTREE,
  ADD KEY `post_idx` (`post_id`) USING BTREE,
  ADD KEY `time_idx` (`view_time`) USING BTREE;

--
-- 在导出的表使用AUTO_INCREMENT
--

--
-- 使用表AUTO_INCREMENT `admin`
--
ALTER TABLE `admin`
  MODIFY `id` int(6) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `banner`
--
ALTER TABLE `banner`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `banners`
--
ALTER TABLE `banners`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `checkins`
--
ALTER TABLE `checkins`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `comm`
--
ALTER TABLE `comm`
  MODIFY `id` int(6) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `configx`
--
ALTER TABLE `configx`
  MODIFY `id` int(6) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `emojis`
--
ALTER TABLE `emojis`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `emoji_downloads`
--
ALTER TABLE `emoji_downloads`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `emoji_favorites`
--
ALTER TABLE `emoji_favorites`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `essay`
--
ALTER TABLE `essay`
  MODIFY `id` int(6) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `lcke`
--
ALTER TABLE `lcke`
  MODIFY `id` int(6) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `link`
--
ALTER TABLE `link`
  MODIFY `id` int(6) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `mall_categories`
--
ALTER TABLE `mall_categories`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `mall_orders`
--
ALTER TABLE `mall_orders`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `mall_products`
--
ALTER TABLE `mall_products`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `message`
--
ALTER TABLE `message`
  MODIFY `id` int(6) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `messages`
--
ALTER TABLE `messages`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `notifications`
--
ALTER TABLE `notifications`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `orders`
--
ALTER TABLE `orders`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `sign`
--
ALTER TABLE `sign`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `user`
--
ALTER TABLE `user`
  MODIFY `id` int(6) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `view_history`
--
ALTER TABLE `view_history`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
