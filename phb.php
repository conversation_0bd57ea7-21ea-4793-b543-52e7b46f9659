<?php

//decode by nige112
if ($_SERVER["REQUEST_METHOD"] === "POST") {
	$arr = [["code" => "201", "msg" => "非法请求"]];
	exit(json_encode($arr, JSON_UNESCAPED_UNICODE));
}
$iteace = "0";
if (is_file("./config.php")) {
	require "./config.php";
} else {
	exit("请先安装程序！<a href=\"./install/\">点击安装</a>");
}
require "./api/wz.php";

// 获取发帖排行榜数据
$ranking_list = [];
$title = "发帖排行榜";
$icon = "ri-file-list-3-fill";
$count_text = "发帖";

try {
    // 优化SQL查询，使用INNER JOIN替代LEFT JOIN，只查询需要的字段
    $sql = "SELECT u.username, u.name, u.img, COUNT(e.id) as count 
            FROM user u 
            INNER JOIN essay e ON u.username = e.ptpuser 
            GROUP BY u.username, u.name, u.img 
            ORDER BY count DESC 
            LIMIT 20";
    
    $result = $conn->query($sql);
    
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $ranking_list[] = [
                'username' => $row['username'],
                'name' => $row['name'] ? $row['name'] : ($row['username'] ? $row['username'] : '未知用户'),
                'img' => $row['img'] ?: './assets/img/default-avatar.png',
                'count' => $row['count'] ? intval($row['count']) : 0
            ];
        }
    }
} catch (Exception $e) {
    // 错误处理
    error_log("排行榜查询错误: " . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title><?php echo $title; ?> - <?php echo $name; ?></title>
    <!--为搜索引擎定义关键词-->
    <meta name="keywords" content="排行榜,<?php echo $name; ?>,用户排名">
    <!--为网页定义描述内容 用于告诉搜索引擎，你网站的主要内容-->
    <meta name="description" content="<?php echo $name . "," . $subtitle; ?>">
    <meta name="author" content="<?php echo $name; ?>">
    <meta name="copyright" content="<?php echo $name; ?>">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta http-equiv="cache-control" content="no-cache">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0,minimum-scale=1.0, user-scalable=no minimal-ui">
    <link rel="apple-touch-icon" sizes="57x57" href="<?php echo $icon; ?>" /><!-- Standard iPhone -->
    <link rel="apple-touch-icon" sizes="114x114" href="<?php echo $icon; ?>" /><!-- Retina iPhone -->
    <link rel="apple-touch-icon" sizes="72x72" href="<?php echo $icon; ?>" /><!-- Standard iPad -->
    <link rel="apple-touch-icon" sizes="144x144" href="<?php echo $icon; ?>" /><!-- Retina iPad -->
    <link rel="icon" href="<?php echo $icon; ?>" type="image/x-icon" />
    <meta name="robots" content="index,follow">
    <meta name="format-detection" content="telephone=no">
    <meta http-equiv="x-rim-auto-match" content="none">
    <!-- 引入更丰富的图标库 -->
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <style>
body {
    font-family: Arial, sans-serif;
    background-color: #f4f4f4;
    margin: 0;
    scroll-behavior: smooth;
}

/* 移除sh-main-head的高度限制 */
.sh-main-head {
    height: auto;
    min-height: 40px;
    position: sticky;
    top: 0;
    z-index: 100;
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(0,0,0,0.05);
    padding: 5px 0;
}

.dark-theme .sh-main-head {
    background-color: rgba(30, 30, 30, 0.95);
    border-bottom: 1px solid rgba(255,255,255,0.05);
}

.ranking-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
}

.ranking-page-title {
    font-size: 24px;
    font-weight: bold;
    background: linear-gradient(135deg, #1e88e5, #42a5f5);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    display: flex;
    align-items: center;
}

.ranking-page-title i {
    margin-right: 8px;
    font-size: 26px;
    background: linear-gradient(135deg, #1e88e5, #42a5f5);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

 .ranking-container {
    background-color: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.05);
    margin: 15px;
    padding: 20px;
    overflow: hidden;
    animation: fadeIn 0.6s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.dark-theme .ranking-container {
    background-color: #272727;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.ranking-title {
    text-align: left;
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}

.dark-theme .ranking-title {
    color: #eee;
}

.ranking-title i {
    margin-right: 8px;
    font-size: 22px;
    color: #1e88e5;
}

.ranking-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.ranking-list li {
    border-bottom: 1px solid #f0f0f0;
    padding: 15px 0;
    display: flex;
    align-items: center;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
}

.dark-theme .ranking-list li {
    border-bottom: 1px solid #333;
}

.ranking-list li:active {
    transform: translateX(5px);
    background-color: rgba(30, 136, 229, 0.05);
}

.ranking-list li:last-child {
    border-bottom: none;
}

.ranking-position {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #f5f5f5;
    color: #666;
    font-size: 16px;
    font-weight: bold;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 15px;
    flex-shrink: 0;
}

.dark-theme .ranking-position {
    background-color: #333;
    color: #ccc;
}

.ranking-position.top1 {
    background: linear-gradient(135deg, #FFD700, #FFA500);
    color: white;
    box-shadow: 0 2px 10px rgba(255, 215, 0, 0.3);
}

.ranking-position.top2 {
    background: linear-gradient(135deg, #C0C0C0, #A9A9A9);
    color: white;
    box-shadow: 0 2px 10px rgba(192, 192, 192, 0.3);
}

.ranking-position.top3 {
    background: linear-gradient(135deg, #CD7F32, #8B4513);
    color: white;
    box-shadow: 0 2px 10px rgba(205, 127, 50, 0.3);
}

.ranking-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 15px;
    position: relative;
    background-color: #f0f0f0;
    flex-shrink: 0;
}

.dark-theme .ranking-avatar {
    background-color: #333;
}

.ranking-info {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.ranking-name {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
}

.dark-theme .ranking-name {
    color: #eee;
}

.ranking-count {
    font-size: 14px;
    color: #666;
}

.dark-theme .ranking-count {
    color: #aaa;
}

.ranking-count strong {
    color: #1e88e5;
    font-weight: 600;
}

.dark-theme .ranking-count strong {
    color: #42a5f5;
}

.no-data {
    padding: 40px 0;
    text-align: center;
    color: #999;
    font-size: 16px;
}

.dark-theme .no-data {
    color: #777;
}

.no-data i {
    font-size: 48px;
    margin-bottom: 10px;
    display: block;
    color: #ccc;
}

.dark-theme .no-data i {
    color: #555;
}
    </style>
    <?php 
if ($rosdomain == 1) {
	?><meta name="referrer" content="no-referrer"><?php 
}
?>    <link rel="stylesheet" href="<?php echo $iconurl_url;?>">
    <link rel="stylesheet" type="text/css" href="./assets/css/style.css?v=<?php echo $resversion;?>">
    <link rel="stylesheet" type="text/css" href="./assets/css/footer-menu.css?v=<?php echo $resversion;?>">
    <link rel="stylesheet" type="text/css" href="./assets/mesg/dist/css/style.css?v=<?php echo $resversion;?>">
    <link rel="stylesheet" type="text/css" href="./assets/css/jquery.fancybox.min.css?v=<?php echo $resversion;?>">
         <script type="text/javascript" src="./assets/js/jquery.min.js"></script>
    <script type="text/javascript" src="./assets/js/jquery.fancybox.min.js?v=<?php echo $resversion;?>"></script>
    <?php echo $scfontzt;?>    <?php 
$folderPath = "./user/bobg/";
if (!file_exists($folderPath)) {
	mkdir($folderPath, 511, true);
}
if (file_exists("./user/bobg/bobg.jpg")) {
	?><!--背景图--><style>body{background-image: url(./user/bobg/bobg.jpg);}</style><?php 
} elseif (file_exists("./user/bobg/bobg.png")) {
	?><!--背景图--><style>body{background-image: url(./user/bobg/bobg.png);}</style><?php 
} elseif (file_exists("./user/bobg/bobg.jpeg")) {
	?><!--背景图--><style>body{background-image: url(./user/bobg/bobg.jpeg);}</style><?php 
} elseif (file_exists("./user/bobg/bobg.gif")) {
	?><!--背景图--><style>body{background-image: url(./user/bobg/bobg.gif);}</style><?php 
}
?>    <?php echo "<style>" . $filtercucss . "</style>";?></head>
<body class="<?php 
if (isset($_COOKIE["dark_theme"])) {
	if ($_COOKIE["dark_theme"] == "dark-theme") {
		echo "dark-theme";
	}
}
echo "\">
    ";
if ($pagepass != "") {
	if (isset($_COOKIE["pagepass"])) {
		if ($_COOKIE["pagepass"] != md5(md5($pagepass))) {
			include "./site/pagepass.php";
		}
	} else {
		include "./site/pagepass.php";
	}
}
?>    <!--网页主体框架-->
    <div class="centent">
        <!-- 页面载体 -->
        <div class="sh-main setup-main">
          
            
            <!-- 排行榜标题 -->
            <div class="ranking-header">
                <div class="ranking-page-title">
                    <i class="ri-bar-chart-grouped-line"></i>发帖排行榜
                </div>
            </div>
 
            <!-- 创作者排行榜 -->
            <div class="ranking-container">
                <h2 class="ranking-title"><i class="ri-user-star-line"></i>创作者排行</h2>
                
                <!-- 前三名荣誉榜 -->
                <div class="top-users-container" style="display:flex; justify-content:space-between; margin:0 auto; width:100%; padding:0 10px; box-sizing:border-box;">
                    <?php 
                    // 获取前三名用户
                    $top_three = array_slice($ranking_list, 0, 3);
                    
                    // 确保有足够的用户数据
                    if (count($top_three) > 0) {
                        foreach ($top_three as $index => $user) {
                            $rank = $index + 1;
                            $rank_class = "rank-" . $rank;
                            $medal_icon = "";
                            
                            switch ($rank) {
                                case 1:
                                    $medal_icon = "trophy";
                                    $gradient = "linear-gradient(135deg, #FFD700, #FFA500)";
                                    break;
                                case 2:
                                    $medal_icon = "medal-line";
                                    $gradient = "linear-gradient(135deg, #C0C0C0, #A9A9A9)";
                                    break;
                                case 3:
                                    $medal_icon = "award-line";
                                    $gradient = "linear-gradient(135deg, #CD7F32, #A0522D)";
                                    break;
                            }
                            
                            echo '<div class="top-user-card ' . $rank_class . '" style="width:32%; flex:none; background: white; border-radius: 15px; padding: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); position: relative; overflow: hidden; text-align: center;" onclick="location.href=\'./archives.php?user=' . md5(md5($user['username'])) . '\'">';
                            echo '<div class="top-user-rank" style="background: ' . $gradient . '; width: 30px; height: 30px; border-radius: 50%; display: flex; align-items: center; justify-content: center; position: absolute; top: 5px; right: 5px; box-shadow: 0 3px 8px rgba(0,0,0,0.2);"><span style="color:white; font-weight:bold; font-size:14px;">' . $rank . '</span></div>';
                            echo '<div class="top-user-avatar" style="width:50px; height:50px; border-radius: 50%; overflow: hidden; margin: 0 auto 5px; border: 2px solid ' . $gradient . ';"><img src="' . (!empty($user['img']) ? $user['img'] : './assets/img/default-avatar.jpg') . '" alt="排名第' . $rank . '" style="width: 100%; height: 100%; object-fit: cover;"></div>';
                            echo '<div class="top-user-info">';
                            echo '<div class="top-user-name" style="font-size:14px; white-space:nowrap; overflow:hidden; text-overflow:ellipsis;">' . $user['name'] . '</div>';
                            echo '<div class="top-user-value" style="font-size:12px; padding:2px 4px;">' . $user['count'] . ' ' . $count_text . '</div>';
                            echo '</div>';
                            echo '</div>';
                        }
                    } else {
                        // 如果不足三人，显示提示
                        echo '<div class="no-data-tip">';
                        echo '<i class="ri-error-warning-line"></i>';
                        echo '<p>暂无数据</p>';
                        echo '</div>';
                    }
                    ?>
                </div>
                
    <ul class="ranking-list">
                    <?php 
                    // 从第4名开始显示
                    for ($i = 3; $i < count($ranking_list); $i++):
                        $item = $ranking_list[$i];
                    ?>
            <li onclick="location.href='./archives.php?user=<?php echo md5(md5($item['username']));?>'">
                            <span class="ranking-position"><?php echo $i + 1; ?></span>
                            <div class="ranking-avatar"><img src="<?php echo $item['img']; ?>" alt="头像"></div>
                            <div class="ranking-info">
                                <div class="ranking-name"><?php echo $item['name']; ?></div>
                                <div class="ranking-count"><strong><?php echo $item['count']; ?></strong> 篇</div>
                            </div>
            </li>
                    <?php endfor; ?>
    </ul>
</div>
            


 
<!--版权-->
<div class="sh-copyright">
    <span class="sh-copyright-banquan" id="sh-copyright-banquan"><?php echo $copyright;?></span>&nbsp;
    <?php 
if ($beian != "") {
	echo "<span class=\"sh-copyright-banquan\">" . html_entity_decode($beian) . "</span>";
}
?></div>
<!--版权-->


    </div>
    
    <!-- 底部填充，避免内容被底部菜单遮挡 -->
    <div class="footer-padding"></div>
    
    <!-- 底部菜单 -->
    <div class="footer-menu">
        <a href="./index.php" class="footer-menu-item">
            <i class="ri-home-4-line"></i>
            <span>首页</span>
        </a>
        <a href="./discover.php" class="footer-menu-item active">
            <i class="ri-compass-3-line"></i>
            <span>发现</span>
        </a>
        <a href="./notify.php" class="footer-menu-item">
            <i class="ri-notification-2-line"></i>
            <span>通知</span>
            <?php
            // 检查是否有未读通知
            if ($userdlzt == 1) {
                $sql_unread = "SELECT COUNT(*) as count FROM notifications WHERE user_to = '{$user_zh}' AND is_read = 0";
                $result_unread = $conn->query($sql_unread);
                $unread_count = 0;
                
                if ($result_unread && $row_unread = $result_unread->fetch_assoc()) {
                    $unread_count = $row_unread['count'];
                }
                
                if ($unread_count > 0) {
                    echo '<div class="notification-dot"></div>';
                }
            }
            ?>
        </a>
        <a href="./home.php" class="footer-menu-item">
            <i class="ri-user-3-line"></i>
            <span>我的</span>
        </a>
    </div>

<script>
$(document).ready(function() {
    $('.fancybox').fancybox();
    
    // 添加滚动监听，实现导航背景透明度变化
    window.addEventListener('scroll', function() {
        var header = document.querySelector('.sh-main-head');
        var scrollPosition = window.scrollY;
        
        if (scrollPosition > 30) {
            header.style.backgroundColor = 'var(--background)';
            header.style.boxShadow = '0 2px 10px rgba(0,0,0,0.05)';
        } else {
            header.style.backgroundColor = 'transparent';
            header.style.boxShadow = 'none';
        }
    });
    
    // 为排行榜项添加入场动画
    document.querySelectorAll('.ranking-list li').forEach(function(item, index) {
        item.style.animationDelay = (index * 0.05) + 's';
        item.style.opacity = '0';
        item.style.animation = 'fadeIn 0.5s ease-out forwards';
        item.style.animationDelay = (index * 0.05) + 's';
    });
});
</script>
    <script type="text/javascript" src="./assets/js/repass.js?v=<?php echo $resversion;?>"></script>
    <script type="text/javascript" src="./assets/mesg/dist/js/sh-noytf.js?v=<?php echo $resversion;?>"></script><!--引入弹窗对话框-->
    <?php echo "<script type=\"text/javascript\">" . $filtercujs . "</script>";?></body>
</html>