<?php
// 表情包下载API
$iteace = "1"; // 设置API状态，避免触发"请设置状态"错误

header('Content-Type: application/json; charset=utf-8');

// 引入配置文件
if (is_file("../config.php")) {
    require "../config.php";
} else {
    exit(json_encode(["code" => 500, "msg" => "配置文件不存在"]));
}

// 引入用户验证
require "../api/wz.php";

// 检查用户是否登录
if ($userdlzt != 1) {
    exit(json_encode(["code" => 401, "msg" => "请先登录"]));
}

// 检查请求方法
if ($_SERVER["REQUEST_METHOD"] !== "POST") {
    exit(json_encode(["code" => 400, "msg" => "请求方法不正确"]));
}

// 获取表情包ID
$emoji_id = isset($_POST['emoji_id']) ? intval($_POST['emoji_id']) : 0;
if ($emoji_id <= 0) {
    exit(json_encode(["code" => 400, "msg" => "表情包ID不正确"]));
}

// 获取用户ID
$user_id = null;
$sql_user = "SELECT id FROM user WHERE username = ?";
$stmt_user = $conn->prepare($sql_user);
$stmt_user->bind_param("s", $user_zh);
$stmt_user->execute();
$result_user = $stmt_user->get_result();

if ($result_user && $row = $result_user->fetch_assoc()) {
    $user_id = $row['id'];
} else {
    exit(json_encode(["code" => 500, "msg" => "无法获取用户ID"]));
}

// 检查表情包是否存在
$sql_check = "SELECT e.*, u.img as user_img, u.name as user_name 
              FROM emojis e 
              LEFT JOIN user u ON e.user_id = u.id 
              WHERE e.id = ? AND e.status = 1";
$stmt_check = $conn->prepare($sql_check);
$stmt_check->bind_param("i", $emoji_id);
$stmt_check->execute();
$result_check = $stmt_check->get_result();

if ($result_check->num_rows == 0) {
    exit(json_encode(["code" => 404, "msg" => "表情包不存在或已被禁用"]));
}

$emoji_data = $result_check->fetch_assoc();
$file_path = '../' . $emoji_data['file_path'];

// 检查文件是否存在
if (!file_exists($file_path)) {
    exit(json_encode(["code" => 404, "msg" => "表情包文件不存在"]));
}

// 检查是否已经下载过（每个用户对每个表情包只记录一次下载）
$sql_check_download = "SELECT id FROM emoji_downloads WHERE user_id = ? AND emoji_id = ?";
$stmt_check_download = $conn->prepare($sql_check_download);
$stmt_check_download->bind_param("ii", $user_id, $emoji_id);
$stmt_check_download->execute();
$result_check_download = $stmt_check_download->get_result();

// 开始事务
$conn->begin_transaction();

try {
    if ($result_check_download->num_rows == 0) {
        // 记录下载
        $sql_insert = "INSERT INTO emoji_downloads (user_id, emoji_id) VALUES (?, ?)";
        $stmt_insert = $conn->prepare($sql_insert);
        $stmt_insert->bind_param("ii", $user_id, $emoji_id);
        $stmt_insert->execute();
        
        // 更新下载次数
        $sql_update = "UPDATE emojis SET download_count = download_count + 1 WHERE id = ?";
        $stmt_update = $conn->prepare($sql_update);
        $stmt_update->bind_param("i", $emoji_id);
        $stmt_update->execute();
        
        // 给表情包上传者增加积分（每个表情被不同用户下载，上传者获得1积分）
        // 如果下载者不是上传者自己
        if ($user_id != $emoji_data['user_id']) {
            $uploader_id = $emoji_data['user_id'];
            $sql_add_points = "UPDATE user SET jifen = jifen + 1, points = points + 1 WHERE id = ?";
            $stmt_add_points = $conn->prepare($sql_add_points);
            $stmt_add_points->bind_param("i", $uploader_id);
            $stmt_add_points->execute();
            
            // 给上传者发送通知
            $notification_message = "您的表情包「{$emoji_data['name']}」被下载了，获得1积分奖励！";
            $uploader_username = "";
            
            // 获取上传者用户名
            $sql_get_username = "SELECT username FROM user WHERE id = ?";
            $stmt_get_username = $conn->prepare($sql_get_username);
            $stmt_get_username->bind_param("i", $uploader_id);
            $stmt_get_username->execute();
            $result_get_username = $stmt_get_username->get_result();
            
            if ($result_get_username->num_rows > 0) {
                $uploader_data = $result_get_username->fetch_assoc();
                $uploader_username = $uploader_data['username'];
                
                // 添加通知
                $sql_notify = "INSERT INTO notifications (user_from, user_to, message, type, content_id, is_read, created_at) 
                              VALUES (?, ?, ?, 'emoji_download', ?, 0, NOW())";
                $stmt_notify = $conn->prepare($sql_notify);
                if ($stmt_notify) {
                    $stmt_notify->bind_param("ssis", $user_zh, $uploader_username, $notification_message, $emoji_id);
                    $stmt_notify->execute();
                }
            }
        }
    }
    
    // 提交事务
    $conn->commit();
    
    // 返回文件信息
    exit(json_encode([
        "code" => 200,
        "msg" => "下载成功",
        "data" => [
            "id" => $emoji_data['id'],
            "name" => $emoji_data['name'],
            "file_path" => $emoji_data['file_path'],
            "download_count" => $emoji_data['download_count'] + 1
        ]
    ]));
    
} catch (Exception $e) {
    // 回滚事务
    $conn->rollback();
    exit(json_encode(["code" => 500, "msg" => "下载处理失败: " . $e->getMessage()]));
} 