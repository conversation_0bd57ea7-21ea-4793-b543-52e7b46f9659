<?php
// 收藏功能API
$iteace = "1"; // 设置API状态，避免触发"请设置状态"错误

header('Content-Type: application/json; charset=utf-8');
http_response_code(200); // 明确设置HTTP状态码

if ($_SERVER["REQUEST_METHOD"] === "POST") {
    $arr = [["code" => "201", "msg" => "非法请求"]];
    exit(json_encode($arr, JSON_UNESCAPED_UNICODE));
}

// 引入配置文件
require "../config.php";
require "../api/wz.php";

// 检查用户是否登录
if ($userdlzt == 0) {
    $arr = [["code" => "201", "msg" => "请先登录"]];
    exit(json_encode($arr, JSON_UNESCAPED_UNICODE));
}

// 获取帖子ID
$cid = addslashes(htmlspecialchars($_GET["cid"]));
if (empty($cid)) {
    $arr = [["code" => "201", "msg" => "参数错误"]];
    exit(json_encode($arr, JSON_UNESCAPED_UNICODE));
}

// 检查帖子是否存在
$sql_check_post = "SELECT * FROM essay WHERE cid = '{$cid}'";
$result_check_post = $conn->query($sql_check_post);
if ($result_check_post->num_rows == 0) {
    $arr = [["code" => "201", "msg" => "内容不存在"]];
    exit(json_encode($arr, JSON_UNESCAPED_UNICODE));
}

// 检查是否已经收藏
$sql_check_favorite = "SELECT * FROM favorites WHERE userid = '{$user_zh}' AND cid = '{$cid}'";
$result_check_favorite = $conn->query($sql_check_favorite);

if ($result_check_favorite->num_rows > 0) {
    // 已收藏，取消收藏
    $sql_delete = "DELETE FROM favorites WHERE userid = '{$user_zh}' AND cid = '{$cid}'";
    if ($conn->query($sql_delete) === TRUE) {
        $arr = [["code" => "200", "msg" => "取消收藏成功", "status" => "unfavorite"]];
        exit(json_encode($arr, JSON_UNESCAPED_UNICODE));
    } else {
        $arr = [["code" => "201", "msg" => "取消收藏失败"]];
        exit(json_encode($arr, JSON_UNESCAPED_UNICODE));
    }
} else {
    // 未收藏，添加收藏
    $sql_insert = "INSERT INTO favorites (userid, cid) VALUES ('{$user_zh}', '{$cid}')";
    if ($conn->query($sql_insert) === TRUE) {
        // 获取帖子作者
        $post_row = $result_check_post->fetch_assoc();
        $post_author = $post_row["ptpuser"];
        
        // 如果不是收藏自己的帖子，添加通知
        if ($post_author != $user_zh) {
            // 获取用户信息
            $sql_user = "SELECT name FROM user WHERE username = '{$user_zh}'";
            $result_user = $conn->query($sql_user);
            $user_row = $result_user->fetch_assoc();
            $user_name = $user_row["name"];
            
            // 截取帖子内容
            $post_text = $post_row["ptptext"];
            $post_text = mb_substr($post_text, 0, 20, 'UTF-8');
            if (mb_strlen($post_row["ptptext"]) > 20) {
                $post_text .= '...';
            }
            
            // 添加通知
            $message = "{$user_name} 收藏了你的帖子：{$post_text}";
            $sql_notify = "INSERT INTO notifications (user_from, user_to, message, type, content_id, is_read) 
                          VALUES ('{$user_zh}', '{$post_author}', '{$message}', 'favorite', '{$cid}', 0)";
            $conn->query($sql_notify);
        }
        
        $arr = [["code" => "200", "msg" => "收藏成功", "status" => "favorite"]];
        exit(json_encode($arr, JSON_UNESCAPED_UNICODE));
    } else {
        $arr = [["code" => "201", "msg" => "收藏失败"]];
        exit(json_encode($arr, JSON_UNESCAPED_UNICODE));
    }
}

$conn->close();
?> 