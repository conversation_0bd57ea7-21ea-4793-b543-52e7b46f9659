/* 枫瑞博客二开修改 */
@media(max-width:636px)
{
  #downloadBtn{width: 100% !important;}
  .frbkw{
	  position: relative;
	  z-index: 2;
	  left: 50%;
	  transform: translate(-50%,0%);
  }
}
.frbkw{
	position: relative;
	width: 200px;
	margin-top: 30px;
}

html {
	line-height: 1.15;
	-ms-text-size-adjust: 100%;
	-webkit-text-size-adjust: 100%
}

body {
	margin: 0
}

article,
aside,
footer,
header,
nav,
section {
	display: block
}

h1 {
	font-size: 2em;
	margin: 0.67em 0
}

figcaption,
figure,
main {
	display: block
}

figure {
	margin: 1em 40px
}

hr {
	box-sizing: content-box;
	height: 0;
	overflow: visible
}

pre {
	font-family: monospace, monospace;
	font-size: 1em
}

a {
	background-color: transparent;
	-webkit-text-decoration-skip: objects
}

abbr[title] {
	border-bottom: none;
	text-decoration: underline;
	-webkit-text-decoration: underline dotted;
	text-decoration: underline dotted
}

b,
strong {
	font-weight: inherit
}

b,
strong {
	font-weight: bolder
}

code,
kbd,
samp {
	font-family: monospace, monospace;
	font-size: 1em
}

dfn {
	font-style: italic
}

mark {
	background-color: #ff0;
	color: #000
}

small {
	font-size: 80%
}

sub,
sup {
	font-size: 75%;
	line-height: 0;
	position: relative;
	vertical-align: baseline
}

sub {
	bottom: -0.25em
}

sup {
	top: -0.5em
}

audio,
video {
	display: inline-block
}

audio:not([controls]) {
	display: none;
	height: 0
}

img {
	border-style: none
}

svg:not(:root) {
	overflow: hidden
}

button,
input,
optgroup,
select,
textarea {
	font-family: sans-serif;
	font-size: 100%;
	line-height: 1.15;
	margin: 0
}

button,
input {
	overflow: visible
}

button,
select {
	text-transform: none
}

button,
html [type="button"],
[type="reset"],
[type="submit"] {
	-webkit-appearance: button
}

button::-moz-focus-inner,
[type="button"]::-moz-focus-inner,
[type="reset"]::-moz-focus-inner,
[type="submit"]::-moz-focus-inner {
	border-style: none;
	padding: 0
}

button:-moz-focusring,
[type="button"]:-moz-focusring,
[type="reset"]:-moz-focusring,
[type="submit"]:-moz-focusring {
	outline: 1px dotted ButtonText
}

fieldset {
	padding: 0.35em 0.75em 0.625em
}

legend {
	box-sizing: border-box;
	color: inherit;
	display: table;
	max-width: 100%;
	padding: 0;
	white-space: normal
}

progress {
	display: inline-block;
	vertical-align: baseline
}

textarea {
	overflow: auto
}

[type="checkbox"],
[type="radio"] {
	box-sizing: border-box;
	padding: 0
}

[type="number"]::-webkit-inner-spin-button,
[type="number"]::-webkit-outer-spin-button {
	height: auto
}

[type="search"] {
	-webkit-appearance: textfield;
	outline-offset: -2px
}

[type="search"]::-webkit-search-cancel-button,
[type="search"]::-webkit-search-decoration {
	-webkit-appearance: none
}

::-webkit-file-upload-button {
	-webkit-appearance: button;
	font: inherit
}

details,
menu {
	display: block
}

summary {
	display: list-item
}

canvas {
	display: inline-block
}

template {
	display: none
}

[hidden] {
	display: none
}

html {
	box-sizing: border-box
}

*,
*:before,
*:after {
	box-sizing: inherit
}

body {
	background: #fff;
	-moz-osx-font-smoothing: grayscale;
	-webkit-font-smoothing: antialiased
}

hr {
	border: 0;
	display: block;
	height: 1px;
	background: #DDE7EF;
	margin-top: 24px;
	margin-bottom: 24px
}

ul,
ol {
	margin-top: 0;
	margin-bottom: 24px;
	padding-left: 24px
}

ul {
	list-style: disc
}

ol {
	list-style: decimal
}

li>ul,
li>ol {
	margin-bottom: 0
}

dl {
	margin-top: 0;
	margin-bottom: 24px
}

dt {
	font-weight: 500
}

dd {
	margin-left: 24px;
	margin-bottom: 24px
}

img {
	height: auto;
	max-width: 100%;
	vertical-align: middle
}

figure {
	margin: 24px 0
}

figcaption {
	font-size: 15px;
	line-height: 22px;
	padding: 8px 0
}

img,
svg {
	display: block
}

table {
	border-collapse: collapse;
	margin-bottom: 24px;
	width: 100%
}

tr {
	border-bottom: 1px solid #DDE7EF
}

th {
	text-align: left
}

th,
td {
	padding: 10px 16px
}

th:first-child,
td:first-child {
	padding-left: 0
}

th:last-child,
td:last-child {
	padding-right: 0
}

html {
	font-size: 20px;
	line-height: 30px
}

body {
	color: #768696;
	font-size: 1rem
}

body,
button,
input,
select,
textarea {
	font-family: "Fira Sans", sans-serif
}

a {
	color: inherit;
	text-decoration: underline
}

a:hover,
a:active {
	outline: 0;
	text-decoration: none
}

h1,
h2,
h3,
h4,
h5,
h6,
.h1,
.h2,
.h3,
.h4,
.h5,
.h6 {
	clear: both;
	color: #30323D;
	font-weight: 500
}

h1,
.h1 {
	font-size: 42px;
	line-height: 52px;
	letter-spacing: -0.1px
}

@media (min-width: 641px) {

	h1,
	.h1 {
		font-size: 52px;
		line-height: 62px;
		letter-spacing: -0.1px
	}
}

h2,
.h2 {
	font-size: 36px;
	line-height: 46px;
	letter-spacing: -0.1px
}

@media (min-width: 641px) {

	h2,
	.h2 {
		font-size: 42px;
		line-height: 52px;
		letter-spacing: -0.1px
	}
}

h3,
.h3,
blockquote {
	font-size: 24px;
	line-height: 34px;
	letter-spacing: -0.1px
}

@media (min-width: 641px) {

	h3,
	.h3,
	blockquote {
		font-size: 36px;
		line-height: 46px;
		letter-spacing: -0.1px
	}
}

h4,
h5,
h6,
.h4,
.h5,
.h6 {
	font-size: 20px;
	line-height: 30px;
	letter-spacing: -0.1px
}

@media (min-width: 641px) {

	h4,
	h5,
	h6,
	.h4,
	.h5,
	.h6 {
		font-size: 24px;
		line-height: 34px;
		letter-spacing: -0.1px
	}
}

@media (max-width: 640px) {
	.h1-mobile {
		font-size: 42px;
		line-height: 52px;
		letter-spacing: -0.1px
	}

	.h2-mobile {
		font-size: 36px;
		line-height: 46px;
		letter-spacing: -0.1px
	}

	.h3-mobile {
		font-size: 24px;
		line-height: 34px;
		letter-spacing: -0.1px
	}

	.h4-mobile,
	.h5-mobile,
	.h6-mobile {
		font-size: 20px;
		line-height: 30px;
		letter-spacing: -0.1px
	}
}

.text-light {
	color: rgba(255, 255, 255, 0.8)
}

.text-light a {
	color: rgba(255, 255, 255, 0.8)
}

.text-light h1,
.text-light h2,
.text-light h3,
.text-light h4,
.text-light h5,
.text-light h6,
.text-light .h1,
.text-light .h2,
.text-light .h3,
.text-light .h4,
.text-light .h5,
.text-light .h6 {
	color: #fff !important
}

.text-sm {
	font-size: 18px;
	line-height: 27px;
	letter-spacing: -0.1px
}

.text-xs {
	font-size: 15px;
	line-height: 22px;
	letter-spacing: 0px
}

h1,
h2,
.h1,
.h2 {
	margin-top: 48px;
	margin-bottom: 16px
}

h3,
.h3 {
	margin-top: 36px;
	margin-bottom: 12px
}

h4,
h5,
h6,
.h4,
.h5,
.h6 {
	margin-top: 24px;
	margin-bottom: 4px
}

p {
	margin-top: 0;
	margin-bottom: 24px
}

dfn,
cite,
em,
i {
	font-style: italic
}

blockquote {
	color: #9BACBD;
	font-style: italic;
	margin-top: 24px;
	margin-bottom: 24px;
	margin-left: 24px
}

blockquote::before {
	content: "\201C"
}

blockquote::after {
	content: "\201D"
}

blockquote p {
	display: inline
}

address {
	color: #768696;
	border-width: 1px 0;
	border-style: solid;
	border-color: #DDE7EF;
	padding: 24px 0;
	margin: 0 0 24px
}

pre,
pre h1,
pre h2,
pre h3,
pre h4,
pre h5,
pre h6,
pre .h1,
pre .h2,
pre .h3,
pre .h4,
pre .h5,
pre .h6 {
	font-family: "Courier 10 Pitch", Courier, monospace
}

pre,
code,
kbd,
tt,
var {
	background: #F5F9FC
}

pre {
	font-size: 15px;
	line-height: 22px;
	margin-bottom: 1.6em;
	max-width: 100%;
	overflow: auto;
	padding: 24px;
	margin-top: 24px;
	margin-bottom: 24px
}

code,
kbd,
tt,
var {
	font-family: Monaco, Consolas, "Andale Mono", "DejaVu Sans Mono", monospace;
	font-size: 15px;
	padding: 2px 4px
}

abbr,
acronym {
	cursor: help
}

mark,
ins {
	text-decoration: none
}

small {
	font-size: 18px;
	line-height: 27px;
	letter-spacing: -0.1px
}

b,
strong {
	font-weight: 700
}

button,
input,
select,
textarea,
label {
	font-size: 20px;
	line-height: 30px
}

.container,
.container-sm {
	width: 100%;
	margin: 0 auto;
	padding-left: 16px;
	padding-right: 16px
}

@media (min-width: 481px) {

	.container,
	.container-sm {
		padding-left: 24px;
		padding-right: 24px
	}
}

.container {
	max-width: 1128px
}

.container-sm {
	max-width: 848px
}

.container .container-sm {
	max-width: 800px;
	padding-left: 0;
	padding-right: 0
}

.screen-reader-text {
	clip: rect(1px, 1px, 1px, 1px);
	position: absolute !important;
	height: 1px;
	width: 1px;
	overflow: hidden;
	word-wrap: normal !important
}

.screen-reader-text:focus {
	border-radius: 2px;
	box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.6);
	clip: auto !important;
	display: block;
	font-size: 15px;
	letter-spacing: 0px;
	font-weight: 500;
	line-height: 16px;
	text-transform: uppercase;
	text-decoration: none;
	background-color: #fff;
	color: #9B95F3 !important;
	border: none;
	height: auto;
	left: 8px;
	padding: 16px 32px;
	top: 8px;
	width: auto;
	z-index: 100000
}

.list-reset {
	list-style: none;
	padding: 0
}

.text-left {
	text-align: left
}

.text-center {
	text-align: center
}

.text-right {
	text-align: right
}

.text-primary {
	color: #9B95F3
}

.has-top-divider {
	position: relative
}

.has-top-divider::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	display: block;
	height: 1px;
	background: #DDE7EF
}

.has-bottom-divider {
	position: relative
}

.has-bottom-divider::after {
	content: '';
	position: absolute;
	bottom: 0;
	left: 0;
	width: 100%;
	display: block;
	height: 1px;
	background: #DDE7EF
}

.m-0 {
	margin: 0
}

.mt-0 {
	margin-top: 0
}

.mr-0 {
	margin-right: 0
}

.mb-0 {
	margin-bottom: 0
}

.ml-0 {
	margin-left: 0
}

.m-8 {
	margin: 8px
}

.mt-8 {
	margin-top: 8px
}

.mr-8 {
	margin-right: 8px
}

.mb-8 {
	margin-bottom: 8px
}

.ml-8 {
	margin-left: 8px
}

.m-16 {
	margin: 16px
}

.mt-16 {
	margin-top: 16px
}

.mr-16 {
	margin-right: 16px
}

.mb-16 {
	margin-bottom: 16px
}

.ml-16 {
	margin-left: 16px
}

.m-24 {
	margin: 24px
}

.mt-24 {
	margin-top: 24px
}

.mr-24 {
	margin-right: 24px
}

.mb-24 {
	margin-bottom: 24px
}

.ml-24 {
	margin-left: 24px
}

.m-32 {
	margin: 32px
}

.mt-32 {
	margin-top: 32px
}

.mr-32 {
	margin-right: 32px
}

.mb-32 {
	margin-bottom: 32px
}

.ml-32 {
	margin-left: 32px
}

.m-40 {
	margin: 40px
}

.mt-40 {
	margin-top: 40px
}

.mr-40 {
	margin-right: 40px
}

.mb-40 {
	margin-bottom: 40px
}

.ml-40 {
	margin-left: 40px
}

.m-48 {
	margin: 48px
}

.mt-48 {
	margin-top: 48px
}

.mr-48 {
	margin-right: 48px
}

.mb-48 {
	margin-bottom: 48px
}

.ml-48 {
	margin-left: 48px
}

.m-56 {
	margin: 56px
}

.mt-56 {
	margin-top: 56px
}

.mr-56 {
	margin-right: 56px
}

.mb-56 {
	margin-bottom: 56px
}

.ml-56 {
	margin-left: 56px
}

.m-64 {
	margin: 64px
}

.mt-64 {
	margin-top: 64px
}

.mr-64 {
	margin-right: 64px
}

.mb-64 {
	margin-bottom: 64px
}

.ml-64 {
	margin-left: 64px
}

.p-0 {
	padding: 0
}

.pt-0 {
	padding-top: 0
}

.pr-0 {
	padding-right: 0
}

.pb-0 {
	padding-bottom: 0
}

.pl-0 {
	padding-left: 0
}

.p-8 {
	padding: 8px
}

.pt-8 {
	padding-top: 8px
}

.pr-8 {
	padding-right: 8px
}

.pb-8 {
	padding-bottom: 8px
}

.pl-8 {
	padding-left: 8px
}

.p-16 {
	padding: 16px
}

.pt-16 {
	padding-top: 16px
}

.pr-16 {
	padding-right: 16px
}

.pb-16 {
	padding-bottom: 16px
}

.pl-16 {
	padding-left: 16px
}

.p-24 {
	padding: 24px
}

.pt-24 {
	padding-top: 24px
}

.pr-24 {
	padding-right: 24px
}

.pb-24 {
	padding-bottom: 24px
}

.pl-24 {
	padding-left: 24px
}

.p-32 {
	padding: 32px
}

.pt-32 {
	padding-top: 32px
}

.pr-32 {
	padding-right: 32px
}

.pb-32 {
	padding-bottom: 32px
}

.pl-32 {
	padding-left: 32px
}

.p-40 {
	padding: 40px
}

.pt-40 {
	padding-top: 40px
}

.pr-40 {
	padding-right: 40px
}

.pb-40 {
	padding-bottom: 40px
}

.pl-40 {
	padding-left: 40px
}

.p-48 {
	padding: 48px
}

.pt-48 {
	padding-top: 48px
}

.pr-48 {
	padding-right: 48px
}

.pb-48 {
	padding-bottom: 48px
}

.pl-48 {
	padding-left: 48px
}

.p-56 {
	padding: 56px
}

.pt-56 {
	padding-top: 56px
}

.pr-56 {
	padding-right: 56px
}

.pb-56 {
	padding-bottom: 56px
}

.pl-56 {
	padding-left: 56px
}

.p-64 {
	padding: 64px
}

.pt-64 {
	padding-top: 64px
}

.pr-64 {
	padding-right: 64px
}

.pb-64 {
	padding-bottom: 64px
}

.pl-64 {
	padding-left: 64px
}

.sr .has-animations .is-revealing {
	visibility: hidden
}

.input,
.textarea {
	background-color: #fff;
	border-width: 1px;
	border-style: solid;
	border-color: #DDE7EF;
	color: #768696;
	max-width: 100%;
	width: 100%
}

.input::-webkit-input-placeholder,
.textarea::-webkit-input-placeholder {
	color: #9BACBD
}

.input:-ms-input-placeholder,
.textarea:-ms-input-placeholder {
	color: #9BACBD
}

.input::-ms-input-placeholder,
.textarea::-ms-input-placeholder {
	color: #9BACBD
}

.input::placeholder,
.textarea::placeholder {
	color: #9BACBD
}

.input::-ms-input-placeholder,
.textarea::-ms-input-placeholder {
	color: #9BACBD
}

.input:-ms-input-placeholder,
.textarea:-ms-input-placeholder {
	color: #9BACBD
}

.input:hover,
.textarea:hover {
	border-color: #ccdbe7
}

.input:active,
.input:focus,
.textarea:active,
.textarea:focus {
	outline: none;
	border-color: #9B95F3
}

.input[disabled],
.textarea[disabled] {
	cursor: not-allowed;
	background-color: #F5F9FC;
	border-color: #F5F9FC
}

.input {
	-moz-appearance: none;
	-webkit-appearance: none;
	font-size: 15px;
	letter-spacing: 0px;
	line-height: 20px;
	border-radius: 2px;
	padding: 13px 16px;
	height: 48px;
	box-shadow: none
}

.input .inline-input {
	display: inline;
	width: auto
}

.textarea {
	display: block;
	min-width: 100%;
	resize: vertical
}

.textarea .inline-textarea {
	display: inline;
	width: auto
}

.field-grouped>.control:not(:last-child) {
	margin-bottom: 8px
}

@media (min-width: 641px) {
	.field-grouped {
		display: flex
	}

	.field-grouped>.control {
		flex-shrink: 0
	}

	.field-grouped>.control.control-expanded {
		flex-grow: 1;
		flex-shrink: 1
	}

	.field-grouped>.control:not(:last-child) {
		margin-bottom: 0;
		margin-right: 8px
	}
}

.button {
	display: inline-flex;
	font-family: "Fira Sans", sans-serif;
	font-size: 15px;
	letter-spacing: 0px;
	font-weight: 700;
	line-height: 16px;
	text-decoration: none !important;
	background-color: #fff;
	color: #9B95F3 !important;
	border: none;
	border-radius: 2px;
	cursor: pointer;
	justify-content: center;
	padding: 16px 32px;
	height: 48px;
	text-align: center;
	white-space: nowrap
}

.button:active {
	outline: 0
}

.button::before {
	border-radius: 2px
}

.button-shadow {
	position: relative
}

.button-shadow::before {
	content: '';
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	box-shadow: 0 8px 16px rgba(48, 50, 61, 0.12);
	mix-blend-mode: multiply;
	transition: box-shadow .15s ease
}

.button-shadow:hover::before {
	box-shadow: 0 8px 16px rgba(48, 50, 61, 0.25)
}

.button-sm {
	padding: 8px 24px;
	height: 32px
}

.button-sm.button-shadow::before {
	box-shadow: 0 4px 16px rgba(48, 50, 61, 0.12)
}

.button-sm.button-shadow:hover::before {
	box-shadow: 0 4px 16px rgba(48, 50, 61, 0.25)
}

.button-primary {
	color: #fff !important;
	transition: background .15s ease
}
.button-primary2 {
	color: #fff !important;
	transition: background .15s ease
}

.button-primary {
	background: #fc6b74;
	background: linear-gradient(65deg, #FF5E5B 0, #F8778D 100%)
}

.button-primary:hover {
	background: #fc7079;
	background: linear-gradient(65deg, #ff6360 0, #f87c91 100%)
}

.button-primary.button-shadow::before {
	box-shadow: 0 8px 16px rgba(255, 94, 91, 0.25)
}

.button-primary.button-shadow:hover::before {
	box-shadow: 0 8px 16px rgba(255, 94, 91, 0.4)
}

.button-primary .button-sm.button-shadow::before {
	box-shadow: 0 4px 16px rgba(255, 94, 91, 0.25)
}

.button-primary .button-sm.button-shadow:hover::before {
	box-shadow: 0 4px 16px rgba(255, 94, 91, 0.4)
}

.button-block {
	display: flex;
	overflow: hidden;
}

.site-header {
	position: relative;
	padding: 24px 0
}

.site-header-inner {
	position: relative;
	display: flex;
	justify-content: space-between;
	align-items: center
}

.header-links {
	display: inline-flex
}

.header-links li {
	display: inline-flex
}

.header-links a:not(.button) {
	font-family: "Fira Sans", sans-serif;
	font-size: 15px;
	line-height: 22px;
	letter-spacing: 0px;
	font-weight: 700;
	color: rgba(255, 255, 255, 0.8);
	text-decoration: none;
	line-height: 16px;
	padding: 8px 24px
}

.header-links a:not(.button):hover,
.header-links a:not(.button):active {
	color: #fff
}

@media (min-width: 1025px) {
	.site-header-large-bg {
		position: relative
	}

	.site-header-large-bg span {
		position: absolute;
		top: -24px;
		bottom: 0;
		left: 648px;
		width: 9999px;
		height: 100vh;
		background: #8179f2;
		background: linear-gradient(to bottom, #665DF0 0, #9B95F3 100%);
		background-repeat: no-repeat
	}
}

.hero {
	text-align: center
}

.hero-inner {
	position: relative
}

.hero-copy {
	padding-top: 40px;
	padding-bottom: 60px
}

.hero-title {
	font-weight: 700
}

.hero-paragraph {
	margin-bottom: 32px
}

.hero-form {
	max-width: 435px;
	margin: 0 auto
}

.hero-illustration {
	position: relative;
	padding-top: 40px;
	padding-bottom: 40px;
	right: -40px
}

.hero-illustration::before {
	content: '';
	position: absolute;
	left: -150px;
	right: 0;
	top: 0;
	bottom: -300px;
	background: #8179f2;
	background: linear-gradient(to bottom, #665DF0 0, #9B95F3 100%)
}

.hero-illustration img,
.hero-illustration svg {
	overflow: visible
}

.hero-illustration-browser {
	position: relative
}

.hero-illustration-browser svg {
	width: 427px;
	height: auto;
	margin-left: auto
}

.hero-ball,
.hero-squares {
	position: absolute
}

.hero-squares-1 {
	top: 16px;
	left: -24px
}

.hero-squares-2,
.hero-ball-1,
.hero-ball-3 {
	display: none
}

.hero-ball-2 {
	top: 208px;
	left: -40px
}

.hero-ball-2 svg {
	width: 104px;
	height: auto
}

.hero-ball-4 {
	top: -20px;
	left: 100px
}

.hero-ball-5 {
	top: 308px;
	left: 185px
}

@media (min-width: 641px) {
	.hero-paragraph {
		padding-left: 72px;
		padding-right: 72px
	}

	.hero-illustration {
		right: -80px
	}

	.hero-illustration-browser svg {
		width: 640px
	}

	.hero-ball-2 {
		top: 294px;
		left: -64px
	}

	.hero-ball-2 svg {
		width: 150px;
		height: auto
	}

	.hero-ball-5 {
		top: 450px;
		left: 330px
	}
}

@media (min-width: 1025px) {
	.hero {
		text-align: left;
		padding-top: 80px;
		padding-bottom: 80px
	}

	.hero::before {
		top: -80px;
		left: 620px;
		height: 100vh;
		width: 100%
	}

	.hero-inner {
		display: flex
	}

	.hero-copy {
		padding-top: 60px;
		padding-right: 88px;
		min-width: 600px
	}

	.hero-paragraph {
		padding-left: 0;
		padding-right: 0
	}

	.hero-illustration {
		right: 0;
		padding: 0
	}

	.hero-illustration::before {
		content: normal
	}

	.hero-illustration-browser svg {
		width: 800px
	}

	.hero-form {
		margin: 0
	}

	.hero-squares-2,
	.hero-ball-1,
	.hero-ball-3 {
		display: block
	}

	.hero-squares-1 {
		top: -88px;
		left: 72px
	}

	.hero-squares-2 {
		top: 474px;
		left: 165px
	}

	.hero-ball-1 {
		top: -190px;
		left: 417px
	}

	.hero-ball-2 {
		top: 335px;
		left: -64px
	}

	.hero-ball-2 svg {
		width: 200px;
		height: auto
	}

	.hero-ball-3 {
		top: 402px;
		left: 440px
	}

	.hero-ball-4 {
		top: -75px;
		left: 290px
	}

	.hero-ball-5 {
		top: 500px
	}
}

.is-boxed {
	background: #F5F9FC
}

.body-wrap {
	background: #fff;
	/*overflow: hidden;*/
	display: flex;
	flex-direction: column;
	min-height: 100vh
}

.boxed-container {
	max-width: 1440px;
	margin: 0 auto;
	box-shadow: 0 16px 48px #DDE7EF
}

@supports (-ms-ime-align: auto) {
	.boxed-container {
		box-shadow: 0 16px 48px rgba(48, 50, 61, 0.12)
	}
}

main {
	flex: 1 0 auto
}

.section-inner {
	position: relative;
	padding-top: 48px;
	padding-bottom: 48px
}

@media (min-width: 641px) {
	.section-inner {
		padding-top: 80px;
		padding-bottom: 80px
	}
}

@media (min-width: 1025px) {
	main {
		display: flex;
		align-items: center
	}

	.hero {
		width: 100%
	}
}

.site-footer {
	font-size: 14px;
	line-height: 20px;
	letter-spacing: 0px;
	color: #9BACBD
}

.site-footer a {
	color: #9BACBD;
	text-decoration: none
}

.site-footer a:hover,
.site-footer a:active {
	color: #768696;
	text-decoration: underline
}

.site-footer-inner {
	position: relative;
	display: flex;
	flex-wrap: wrap;
	padding-top: 40px;
	padding-bottom: 40px
}

.footer-links,
.footer-social-links,
.footer-copyright {
	flex: none;
	width: 100%;
	display: inline-flex;
	justify-content: center
}

.footer-brand,
.footer-links,
.footer-social-links {
	margin-bottom: 24px
}

.footer-links li+li,
.footer-social-links li+li {
	margin-left: 16px
}

.footer-social-links li {
	display: inline-flex
}

.footer-social-links li a {
	padding: 8px
}

.footer-links a {
	color: #fff
}

.footer-links a:hover {
	color: #fff
}

@media (min-width: 641px) {
	.site-footer-inner {
		justify-content: space-between
	}

	.footer-social-links {
		flex: 100%
	}

	.footer-links,
	.footer-copyright {
		flex: 50%
	}

	.footer-brand,
	.footer-copyright {
		justify-content: flex-start
	}

	.footer-links,
	.footer-social-links {
		justify-content: flex-end
	}

	.footer-links {
		order: 1;
		margin-bottom: 0
	}
}

@media (max-width: 1024px) {
	.footer-copyright {
		color: #fff
	}
}

.tlinks {
	text-indent: -9999px;
	height: 0;
	line-height: 0;
	font-size: 0;
	overflow: hidden;
}

/*# sourceMappingURL=data:application/json;base64,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 */
