<?php

// decode by nige112
$ip = $_GET["ip"];
if ($ip!= "ok") {
    exit("错误的指令");
}

if ($_SERVER["HTTP_X_FORWARDED_FOR"]) {
    $ip = $_SERVER["HTTP_X_FORWARDED_FOR"];
} elseif ($_SERVER["HTTP_CLIENT_IP"]) {
    $ip = $_SERVER["HTTP_CLIENT_IP"];
} elseif ($_SERVER["REMOTE_ADDR"]) {
    $ip = $_SERVER["REMOTE_ADDR"];
} elseif (getenv("HTTP_X_FORWARDED_FOR")) {
    $ip = getenv("HTTP_X_FORWARDED_FOR");
} elseif (getenv("HTTP_CLIENT_IP")) {
    $ip = getenv("HTTP_CLIENT_IP");
} elseif (getenv("REMOTE_ADDR")) {
    $ip = getenv("REMOTE_ADDR");
} else {
    $ip = "Unknown";
}

get_ip_city($ip);

function get_ip_city($ip)
{

    $url = "http://ip-api.com/json/". $ip."?lang=zh-CN";
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 5);
    $location = curl_exec($ch);
    curl_close($ch);
    if ($location === false) {
        $arr = [["code" => "201", "ipcd" => $ip, "region" => "", "city" => "", "addr" => ""]];
    } else {
        $locationData = json_decode($location, true);
        if ($locationData && isset($locationData['status']) && $locationData['status'] === 'success') {
            $region = $locationData['regionName'];
            $city = $locationData['city'];
            $addr = $locationData['country']. ', '. $region. ', '. $city;
            $arr = [["code" => "200", "ipcd" => $ip, "region" => $region, "city" => $city, "addr" => $addr]];
        } else {
            $arr = [["code" => "201", "ipcd" => $ip, "region" => "", "city" => "", "addr" => ""]];
        }
    }
    echo json_encode($arr, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
}