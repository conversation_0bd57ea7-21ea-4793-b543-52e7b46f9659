<?php
// 包含数据库配置文件等必要的设置
include "../config.php";
$user_zh = $_COOKIE["username"];
$user_passid = $_COOKIE["passid"];
$conn = new mysqli($servername, $username, $password, $dbname);
if ($conn->connect_error) {
    exit("连接失败: ". $conn->connect_error);
}

// 获取页码参数
$page = isset($_GET['page'])? $_GET['page'] : 1;
$perPage = 50;
$offset = ($page - 1) * $perPage;

$sqlxx = "SELECT * FROM message WHERE suser = '$user_zh' ORDER BY id DESC LIMIT $offset, $perPage";
$resultxx = $conn->query($sqlxx);

$xxsl = 0;
$output = '';
while ($rowxx = $resultxx->fetch_assoc()) {
//   print_r($rowxx);
    $xxsl++;
    $fuserxx = $rowxx["fuser"];
    $fimgxx = $rowxx["fimg"];
    $fnamexx = $rowxx["fname"];
    $suserxx = $rowxx["suser"];
    $titlexx = $rowxx["title"];
    $textxx = $rowxx["text"];
    $ftimexx = $rowxx["ftime"];
    $msgxx = $rowxx["msg"];
    $xxid = $rowxx["id"];
    if ($titlexx == "新的点赞") {
        $sql = "select * from lcke where luser= '{$fuserxx}' and ltime='{$ftimexx}'";
        $result = $conn->query($sql);
        $row = mysqli_fetch_array($result);
        if ($row) {
            $wzssid = $row["lwz"];
        } else {
            $wzssid = "#-1";
        }
    } else {
        $sql = "select * from comm where couser= '{$fuserxx}' and cotime='{$ftimexx}'";
        $result = $conn->query($sql);
        $row = mysqli_fetch_array($result);
        if ($row) {
            $wzssid = $row["wzcid"];
        } else {
            $wzssid = "#-1";
        }
    }
    $data_result = mysqli_query($conn, "select * from essay where cid='{$wzssid}'");
    $data_row = mysqli_fetch_array($data_result);
    //  print_r($data_row);
   
    $wzdlx = $data_row["ptplx"];
    $wzdtp = $data_row["ptpimag"];
    $wzdnr = $data_row["ptptext"];
    //  print_r($wzdtp);die;
    if ($wzdlx == "img") {
        if ($wzdtp!= "") {
            $imgar = explode("(+@+)", $wzdtp);
            $coun = count($imgar);
            $wzdtp = $imgar[0];
        } else {
            $wzdtp = "./assets/img/thumbnailbg.svg";
        }
        $wzfmd = "<img src=\"./assets/img/thumbnailbg.svg\" referrerpolicy=\"no-referrer-when-downgrade\" data-src=\"". $wzdtp. "\" alt=\"动态封面\">";
    } elseif ($wzdlx == "only") {
        $wzfmd = "<div class=\"sh-xxliebwb\"><span>". $wzdnr. "</span></div>";
    } else {
        $wzdtp = "./assets/img/thumbnailbg.svg";
        $wzfmd = "<img src=\"./assets/img/thumbnailbg.svg\" referrerpolicy=\"no-referrer-when-downgrade\" data-src=\"". $wzdtp. "\" alt=\"动态封面\">";
    }
    $time = strtotime($ftimexx);
    $ftimexx = ReckonTime($time);
    // print_r($suserxx);die;
    if ($suserxx == $user_zh) {
        if ($msgxx!= "-1") {
            if ($msgxx == 0) {
                $output.= "<div id=\"". $xxid. "\" class=\"sh-news-con-lie\" lang=\"". $wzssid. "\" onclick=\"mesgxq()\">
                            <!-- 左 -->
                            <div class=\"sh-news-con-lie-left\">
                                <p id=\"xxztx-". $xxid. "\"></p>
                                <div class=\"sh-news-con-lie-left-imgt\">
                                <img src=\"./assets/img/thumbnail.svg\" referrerpolicy=\"no-referrer-when-downgrade\" data-src=\"". $fimgxx. "\" alt=\"头像\" class=\"sh-news-con-lie-left-img\"></div>
                            </div>
                            <!-- 右 -->
                            <div class=\"sh-news-con-lie-right\">
                                <p id=\"xxtzidtitle-". $xxid. "\" class=\"sh-news-con-lie-right-title\">". $fnamexx. "<span class=\"sh-news-con-lie-right-time\">". $ftimexx. "</span>". "</p>
                                <p id=\"xxtzidtext-". $xxid. "\" class=\"sh-news-con-lie-right-text\" lang=\"". $titlexx. "\">". $textxx. "</p>  
                            </div>
                            <div class=\"sh-xxliebfm\">". $wzfmd. "<div class=\"delmes\" id=\"". $xxid. "\" onclick=\"demes()\">删除</div></div>
                        </div>";
            } elseif ($msgxx == 1) {
                $output.= "<div id=\"". $xxid. "\" class=\"sh-news-con-lie\" lang=\"". $wzssid. "\" onclick=\"mesgxq()\">
                            <!-- 左 -->
                            <div class=\"sh-news-con-lie-left\">
                                <p id=\"xxztx-". $xxid. "\" style=\"display:none\"></p>
                                <div class=\"sh-news-con-lie-left-imgt\">
                                <img src=\"./assets/img/thumbnail.svg\" referrerpolicy=\"no-referrer-when-downgrade\" data-src=\"". $fimgxx. "\" alt=\"头像\" class=\"sh-news-con-lie-left-img\"></div>
                            </div>
                            <!-- 右 -->
                            <div class=\"sh-news-con-lie-right\">
                                <p id=\"xxtzidtitle-". $xxid. "\" class=\"sh-news-con-lie-right-title\">". $fnamexx. "<span class=\"sh-news-con-lie-right-time\">". $ftimexx. "</span>". "</p>
                                <p id=\"xxtzidtext-". $xxid. "\" class=\"sh-news-con-lie-right-text\" lang=\"". $titlexx. "\">". $textxx. "</p>  
                            </div>
                            <div class=\"sh-xxliebfm\">". $wzfmd. "<div class=\"delmes\" id=\"". $xxid. "\" onclick=\"demes()\">删除</div></div>
                        </div>";
            }
        } else {
            $xxsl--;
        }
    } else {
        $xxsl--;
    }
}

echo $output;


function ReckonTime($time)
{
	$NowTime = time();
	if ($NowTime < $time) {
		return false;
	}
	$TimePoor = $NowTime - $time;
	if ($TimePoor == 0) {
		$str = "一眨眼之间";
	} elseif ($TimePoor < 60 && $TimePoor > 0) {
		$str = $TimePoor . "秒之前";
	} elseif ($TimePoor >= 60 && $TimePoor <= 3600) {
		$str = floor($TimePoor / 60) . "分钟前";
	} elseif ($TimePoor > 3600 && $TimePoor <= 86400) {
		$str = floor($TimePoor / 3600) . "小时前";
	} elseif ($TimePoor > 86400 && $TimePoor <= 604800) {
		if (floor($TimePoor / 86400) == 1) {
			$str = "昨天";
		} elseif (floor($TimePoor / 86400) == 2) {
			$str = "前天";
		} else {
			$str = floor($TimePoor / 86400) . "天前";
		}
	} elseif ($TimePoor > 604800) {
		$str = date("Y-m-d", $time);
	}
	return $str;
}

$conn->close();


?>