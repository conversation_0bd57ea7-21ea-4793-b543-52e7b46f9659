<?php
// getUserLocations.php

// 包含数据库配置文件等必要的设置
include "../config.php";
$user_zh = $_COOKIE["username"];
$user_passid = $_COOKIE["passid"];
$conn = new mysqli($servername, $username, $password, $dbname);
if ($conn->connect_error) {
    exit("连接失败: ". $conn->connect_error);
}

$user_zh = $_COOKIE["username"];
$user_passid = $_COOKIE["passid"];
// if ($user_zh == "" || $user_passid == "") {
// 	if ($getuser == "") {
// 		exit("请先登录!");
// 	}
// }

$redis = new Redis();
$redis->connect('127.0.0.1', 6379);  // 假设 Redis 服务在本地运行，端口为 6379

// 获取页码参数，默认第一页
$page = isset($_GET['page']) ? intval($_GET['page']) : 0;
$limit = 30;  // 每次获取 10 条数据
$offset = $page * $limit;  // 计算偏移量

// 尝试从 Redis 获取数据缓存
$cachedData = $redis->get("user_locations_page_{$page}");
if ($cachedData) {
    // 如果缓存有数据，直接返回缓存数据
    echo $cachedData;
    exit; // 数据缓存已经返回，不再执行后续代码
}

// 查询数据库获取用户数据
$sqlNewData = "SELECT logip,img FROM user ORDER BY id LIMIT {$limit} OFFSET {$offset}";

$resultNewData = $conn->query($sqlNewData);

$newUserLocations = [];
if ($resultNewData->num_rows > 0) {
    while ($row = $resultNewData->fetch_assoc()) {
        $userIp = $row["logip"];
        $avatar = $row["img"];
        $locationInfo = getLocationByIP($userIp);  // 通过 IP 获取位置信息
        if ($locationInfo) {
            $newUserLocations[] = [
                "province" => $locationInfo["province"],
                "latitude" => $locationInfo["latitude"],
                "longitude" => $locationInfo["longitude"],
                "avatar" => $avatar
            ];
        }
    }
}

// 将查询结果缓存到 Redis，设置过期时间为 1 小时
$redis->setex("user_locations_page_{$page}", 3600, json_encode($newUserLocations, JSON_UNESCAPED_UNICODE));

// 返回查询结果
echo json_encode($newUserLocations, JSON_UNESCAPED_UNICODE);

// 获取 IP 定位信息
function getLocationByIP($ip) {
    global $redis;
    // 先尝试从 Redis 缓存中获取位置信息
    $cachedLocation = $redis->get($ip);
    if ($cachedLocation) {
        return json_decode($cachedLocation, true);
    }

    // ip-api.com 接口的请求 URL
    $url = "http://ip-api.com/json/{$ip}?lang=zh-CN";
    $response = file_get_contents($url);
    $data = json_decode($response, true);

    if ($data && $data["status"] == "success") {
        $province = $data["regionName"];
        $latitude = $data["lat"];
        $longitude = $data["lon"];
        $locationInfo = [
            "latitude" => $latitude,
            "longitude" => $longitude,
            "province" => $province
        ];

        // 将获取到的位置信息存入 Redis 缓存，设置过期时间为 1 小时
        $redis->setex($ip, 3600, json_encode($locationInfo));

        return $locationInfo;
    }

    return null;
}
