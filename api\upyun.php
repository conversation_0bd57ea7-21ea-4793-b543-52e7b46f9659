<?php
require '../tencent-php/vendor/autoload.php'; // 包含腾讯云 SDK 自动加载文件

use Qcloud\Cos\Client;
use Qcloud\Cos\Exception\ServiceResponseException;

// 处理非法请求
if ($_SERVER["REQUEST_METHOD"]!== "POST") {
    $arr = [["code" => "201", "msg" => "非法请求"]];
    echo json_encode($arr, JSON_UNESCAPED_UNICODE);
    return;
}

// 你的腾讯云配置参数
$secretId = 'AKIDtb8VfbKrxqWzHzXv65Nl5EAtQ3YxFNbh';
$secretKey = 'HXeZLwMgornYBg2BPSp23tcBdt9HVfpr';
$bucketName = '0717-1314972303';
$region = 'ap-guangzhou';
$operatorurl = 'https://0717-1314972303.cos.ap-guangzhou.myqcloud.com';

// 从前端接收到的文件路径 $upylu 表示文件名，例如 'xxxx.mp4'
// $upylu = 'xxxx.mp4'; // 假设文件名通过上面的逻辑获得

// 获取文件扩展名
$fileExtension = strtolower(pathinfo($upylu, PATHINFO_EXTENSION));

// 允许的文件类型列表（图片和视频）
$allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'mp4', 'avi', 'mov', 'mkv'];
$newFileb = $newFile;

// 检查文件类型是否在允许范围内
if (!in_array($fileExtension, $allowedExtensions)) {
    exit("<script language=\"JavaScript\">alert(\"不支持的文件类型: {$fileExtension}!\");location.href=\"../index.php\";</script>");
}

// 假设你从前端接收到的文件路径
// $newFile = '../upload/xxxx.mp4'; // 这里直接使用单个文件路径

// 检查文件路径是否为空
if (empty($newFile) || !file_exists($newFile)) {
    exit("<script language=\"JavaScript\">alert(\"文件不存在!\");location.href=\"../index.php\";</script>");
}

// 检查文件大小（图片限制为 2MB，视频限制为 10MB）
$maxSize = ($fileExtension === 'jpg' || $fileExtension === 'jpeg' || $fileExtension === 'png' || $fileExtension === 'gif') ? 2 * 1024 * 1024 : 8 * 1024 * 1024; // 2MB for images, 10MB for videos
if (filesize($newFile) > $maxSize) {
      if (file_exists($newFileb)) {
        unlink($newFileb);
    }
    
    exit("<script language=\"JavaScript\">alert(\"文件大小不能超过 " . ($maxSize / (1024 * 1024)) . "MB!\");location.href=\"../index.php\";</script>");
}

// 仅对图片进行压缩处理，视频不压缩
if (in_array($fileExtension, ['jpg', 'jpeg', 'png', 'gif'])) {
    // 使用 GD 库压缩图片
    $compressedImagePath = '/tmp/compressed_' . basename($newFile); // 临时存储压缩文件
    $imageInfo = getimagesize($newFile);

    if ($imageInfo === false) {
        exit("<script language=\"JavaScript\">alert(\"无法获取图片信息!\");location.href=\"../index.php\";</script>");
    }

    // 根据图片类型进行处理
    if ($imageInfo['mime'] == 'image/jpeg') {
        $image = imagecreatefromjpeg($newFile);
        imagejpeg($image, $compressedImagePath, 50); // 压缩到 50%质量
    } elseif ($imageInfo['mime'] == 'image/png') {
        $image = imagecreatefrompng($newFile);
        imagepng($image, $compressedImagePath, 9); // 压缩到 9 级质量
    } else {
        exit("<script language=\"JavaScript\">alert(\"不支持的图片类型!\");location.href=\"../index.php\";</script>");
    }
    imagedestroy($image); // 释放内存

    $newFile = $compressedImagePath; // 更新 $newFile 为压缩后的文件
}

// 初始化腾讯云 COS 客户端
$cosClient = new Client([
    'region' => $region,
    'credentials' => [
        'secretId' => $secretId,
        'secretKey' => $secretKey,
    ]
]);

try {
    // 上传文件到腾讯云 COS
    $result = $cosClient->putObject([
        'Bucket' => $bucketName,
        'Key' => 'lan/'. $upylu, // 使用上传后的文件名
        'Body' => fopen($newFile, 'rb'), // 上传文件
    ]);

    // 删除临时压缩文件
    if (file_exists($compressedImagePath)) {
        unlink($compressedImagePath);
    }
    
    // 获取文件 URL
    $fileUrl = $operatorurl. '/lan/'. $upylu;
    array_push($arr, $operatorurl. '/lan/'. $upylu);
    // print_r($fileUrl);die;
    // 删除本地上传的文件
    if (file_exists($newFileb)) {
        unlink($newFileb);
    }
   
$upy = 1;

} catch (ServiceResponseException $e) {
   $upy = 0; 
}
