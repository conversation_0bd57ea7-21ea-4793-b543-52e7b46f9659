<?php

//decode by nige112
$iteace = "1";
if (is_file("../config.php")) {
	include "../config.php";
}
include "../api/wz.php";
if ($userdlzt == 0) {
	header("location: ./login.php");
	exit;
}
if ($user_zh == $glyadmin) {
	if ($user_passid != $passid) {
		exit("<script language=\"JavaScript\">;alert(\"账号信息异常,请重新登录!\");location.href=\"./login.php\";</script>;");
	}
} else {
	exit("<script language=\"JavaScript\">;alert(\"没有权限!\");location.href=\"../index.php\";</script>;");
}
?><!DOCTYPE html>
<html lang="zh">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimal-ui">
<meta name="keywords" content="<?php echo $name;?>">
<meta name="description" content="<?php echo $name . " ," . $subtitle;?>">
<meta name="author" content="<?php echo $name;?>">
<title>用户列表 - <?php echo $name;?></title>
<link rel="shortcut icon" type="image/x-icon" href="<?php 
if (strpos($icon, "http") !== false) {
	echo $icon;
} else {
	echo "." . $icon;
}
?>">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="apple-touch-fullscreen" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="default">
<meta name="format-detection" content="telephone=no">
<meta http-equiv="x-rim-auto-match" content="none">
<link rel="stylesheet" type="text/css" href="./assets/css/materialdesignicons.min.css">
<link rel="stylesheet" type="text/css" href="./assets/css/bootstrap.min.css">
<link rel="stylesheet" type="text/css" href="./assets/css/animate.min.css">
<link rel="stylesheet" type="text/css" href="./assets/css/style.min.css">
<link rel="stylesheet" type="text/css" href="../assets/mesg/dist/css/style.css">
</head>

<body>
<!--页面loading-->
<div id="lyear-preloader" class="loading">
  <div class="ctn-preloader">
    <div class="round_spinner">
      <div class="spinner"></div>
      <img src="<?php 
if (strpos($logo, "http") !== false) {
	echo $logo;
} else {
	echo "." . $logo;
}
?>" alt="">
    </div>
  </div>
</div>
<!--页面loading end-->
<div class="lyear-layout-web">
  <div class="lyear-layout-container">
    <!--左侧导航-->
    <aside class="lyear-layout-sidebar">
      
      <!-- logo -->
      <div id="logo" class="sidebar-header">
        <a href="./index.php"><img src="<?php 
if (strpos($logo, "http") !== false) {
	echo $logo;
} else {
	echo "." . $logo;
}
?>" title="<?php echo $name;?>" alt="<?php echo $name;?>" /></a>
      </div>
      <div class="lyear-layout-sidebar-info lyear-scroll"> 
        
        <nav class="sidebar-main">
          <ul class="nav-drawer">
            <li class="nav-item"> <a href="index.php"><i class="mdi mdi-home"></i> <span>后台首页</span></a> </li>
            <li class="nav-item nav-item-has-subnav">
              <a href="javascript:void(0)"><i class="mdi mdi-wan"></i> <span>网站设置</span></a>
              <ul class="nav nav-subnav">
                <li> <a href="./basic.php">基础设置</a> </li>
                <li> <a href="./authority.php">权限设置</a> </li>
                <li> <a href="./imgset.php">图像设置</a> </li>
                <li> <a href="./emailset.php">邮箱配置</a> </li>
              </ul>
            </li>
            <li class="nav-item nav-item-has-subnav open active">
              <a href="javascript:void(0)"><i class="mdi mdi-account-multiple"></i> <span>用户管理</span></a>
              <ul class="nav nav-subnav">
                <li class="active"> <a href="./userlist.php">用户列表</a> </li>
              </ul>
            </li>
            <li class="nav-item nav-item-has-subnav">
              <a href="javascript:void(0)"><i class="mdi mdi-stamper"></i> <span>审核中心</span>
              <?php 
$Query = "Select count(*) as AllNum from essay WHERE ptpaud='0'";
$aes = mysqli_query($conn, $Query);
$escount = mysqli_fetch_assoc($aes);
$essl = $escount["AllNum"];
$Query = "Select count(*) as AllNum from comm WHERE comaud='0'";
$aco = mysqli_query($conn, $Query);
$cocount = mysqli_fetch_assoc($aco);
$cosl = $cocount["AllNum"];
$dshzl = $essl + $cosl;
echo "              ";
if ($dshzl != 0 && $dshzl != "") {
	echo "<span class=\"badge badge-danger\" style=\"margin-left: 10px;\">" . $dshzl . "</span>";
}
?>              </a>
              <ul class="nav nav-subnav">
                <li> <a href="./audites.php">审核文章<?php 
if ($essl != 0 && $essl != "") {
	echo "<span class=\"badge badge-danger\" style=\"margin-left: 10px;\">" . $essl . "</span>";
}
?></a></li>
                <li> <a href="./auditco.php">审核评论<?php 
if ($cosl != 0 && $cosl != "") {
	echo "<span class=\"badge badge-danger\" style=\"margin-left: 10px;\">" . $cosl . "</span>";
}
?></a></li>
              </ul>
            </li>
            <li class="nav-item nav-item-has-subnav">
              <a href="javascript:void(0)"><i class="mdi mdi-link"></i> <span>友链管理</span></a>
              <ul class="nav nav-subnav">
                    <li> <a href="./linkset.php">友链列表</a> </li>
              </ul>
            </li>
            <li class="nav-item nav-item-has-subnav">
              <a href="javascript:void(0)"><i class="mdi mdi-folder-open-outline"></i> <span>资源管理</span></a>
              <ul class="nav nav-subnav">
                <li> <a href="./rm.php">资源列表</a> </li>                 <li> <a href="./article.php">文章列表</a> </li>
                <!--li> <a href="./rmnew.php">新增资源</a> </li-->
              </ul>
            </li>
          </ul>
        </nav>
        
        <div class="sidebar-footer">
          <p class="copyright">Copyright &copy; <?php echo date("Y");?>. <a target="_blank" href="<?php echo $_SERVER["REQUEST_SCHEME"] . "://" . $_SERVER["HTTP_HOST"];?>"><?php echo $glyname;?></a> All rights reserved.</p>
        </div>
      </div>
      
    </aside>
    <!--End 左侧导航-->
    
    <!--头部信息-->
    <header class="lyear-layout-header">
      
      <nav class="navbar">
      
        <div class="navbar-left">
          <div class="lyear-aside-toggler">
            <span class="lyear-toggler-bar"></span>
            <span class="lyear-toggler-bar"></span>
            <span class="lyear-toggler-bar"></span>
          </div>
        </div>
        
        <ul class="navbar-right d-flex align-items-center">
            <li onclick='window.location.href = "../edit.php"'>
                <span class="icon-item"><i class="mdi mdi-pencil-box-outline"></i></span>
		    </li>
          <!--切换主题配色-->
		  <li class="dropdown dropdown-skin">
		    <span data-toggle="dropdown" class="icon-item"><i class="mdi mdi-palette"></i></span>
			<ul class="dropdown-menu dropdown-menu-right" data-stopPropagation="true">
              <li class="drop-title"><p>主题</p></li>
              <li class="drop-skin-li clearfix">
                <span class="inverse">
                  <input type="radio" name="site_theme" value="default" id="site_theme_1" checked>
                  <label for="site_theme_1"></label>
                </span>
                <span>
                  <input type="radio" name="site_theme" value="dark" id="site_theme_2">
                  <label for="site_theme_2"></label>
                </span>
                <span>
                  <input type="radio" name="site_theme" value="translucent" id="site_theme_3">
                  <label for="site_theme_3"></label>
                </span>
              </li>
			  <li class="drop-title"><p>LOGO</p></li>
			  <li class="drop-skin-li clearfix">
                <span class="inverse">
                  <input type="radio" name="logo_bg" value="default" id="logo_bg_1" checked>
                  <label for="logo_bg_1"></label>
                </span>
                <span>
                  <input type="radio" name="logo_bg" value="color_2" id="logo_bg_2">
                  <label for="logo_bg_2"></label>
                </span>
                <span>
                  <input type="radio" name="logo_bg" value="color_3" id="logo_bg_3">
                  <label for="logo_bg_3"></label>
                </span>
                <span>
                  <input type="radio" name="logo_bg" value="color_4" id="logo_bg_4">
                  <label for="logo_bg_4"></label>
                </span>
                <span>
                  <input type="radio" name="logo_bg" value="color_5" id="logo_bg_5">
                  <label for="logo_bg_5"></label>
                </span>
                <span>
                  <input type="radio" name="logo_bg" value="color_6" id="logo_bg_6">
                  <label for="logo_bg_6"></label>
                </span>
                <span>
                  <input type="radio" name="logo_bg" value="color_7" id="logo_bg_7">
                  <label for="logo_bg_7"></label>
                </span>
                <span>
                  <input type="radio" name="logo_bg" value="color_8" id="logo_bg_8">
                  <label for="logo_bg_8"></label>
                </span>
			  </li>
			  <li class="drop-title"><p>头部</p></li>
			  <li class="drop-skin-li clearfix">
                <span class="inverse">
                  <input type="radio" name="header_bg" value="default" id="header_bg_1" checked>
                  <label for="header_bg_1"></label>                      
                </span>                                                    
                <span>                                                     
                  <input type="radio" name="header_bg" value="color_2" id="header_bg_2">
                  <label for="header_bg_2"></label>                      
                </span>                                                    
                <span>                                                     
                  <input type="radio" name="header_bg" value="color_3" id="header_bg_3">
                  <label for="header_bg_3"></label>
                </span>
                <span>
                  <input type="radio" name="header_bg" value="color_4" id="header_bg_4">
                  <label for="header_bg_4"></label>                      
                </span>                                                    
                <span>                                                     
                  <input type="radio" name="header_bg" value="color_5" id="header_bg_5">
                  <label for="header_bg_5"></label>                      
                </span>                                                    
                <span>                                                     
                  <input type="radio" name="header_bg" value="color_6" id="header_bg_6">
                  <label for="header_bg_6"></label>                      
                </span>                                                    
                <span>                                                     
                  <input type="radio" name="header_bg" value="color_7" id="header_bg_7">
                  <label for="header_bg_7"></label>
                </span>
                <span>
                  <input type="radio" name="header_bg" value="color_8" id="header_bg_8">
                  <label for="header_bg_8"></label>
                </span>
				</li>
			  <li class="drop-title"><p>侧边栏</p></li>
			  <li class="drop-skin-li clearfix">
                <span class="inverse">
                  <input type="radio" name="sidebar_bg" value="default" id="sidebar_bg_1" checked>
                  <label for="sidebar_bg_1"></label>
                </span>
                <span>
                  <input type="radio" name="sidebar_bg" value="color_2" id="sidebar_bg_2">
                  <label for="sidebar_bg_2"></label>
                </span>
                <span>
                  <input type="radio" name="sidebar_bg" value="color_3" id="sidebar_bg_3">
                  <label for="sidebar_bg_3"></label>
                </span>
                <span>
                  <input type="radio" name="sidebar_bg" value="color_4" id="sidebar_bg_4">
                  <label for="sidebar_bg_4"></label>
                </span>
                <span>
                  <input type="radio" name="sidebar_bg" value="color_5" id="sidebar_bg_5">
                  <label for="sidebar_bg_5"></label>
                </span>
                <span>
                  <input type="radio" name="sidebar_bg" value="color_6" id="sidebar_bg_6">
                  <label for="sidebar_bg_6"></label>
                </span>
                <span>
                  <input type="radio" name="sidebar_bg" value="color_7" id="sidebar_bg_7">
                  <label for="sidebar_bg_7"></label>
                </span>
                <span>
                  <input type="radio" name="sidebar_bg" value="color_8" id="sidebar_bg_8">
                  <label for="sidebar_bg_8"></label>
                </span>
			  </li>
		    </ul>
		  </li>
          <!--切换主题配色-->
          <li class="dropdown dropdown-profile">
            <a href="javascript:void(0)" data-toggle="dropdown" class="dropdown-toggle">
              <img class="img-avatar img-avatar-48 m-r-10" src="<?php 
if (strpos($user_img, "http") !== false) {
	echo $user_img;
} else {
	echo "." . $user_img;
}
?>" alt="头像" />
              <span><?php echo $user_name;?></span>
            </a>
            <ul class="dropdown-menu dropdown-menu-right">
              <li>
                <a class="dropdown-item" href="../index.php"><i class="mdi mdi-home-export-outline"></i> 回到首页</a>
              </li>
              <li class="dropdown-divider"></li>
              <li>
                <a class="dropdown-item" href="JavaScript:;" onclick="logut()"><i class="mdi mdi-logout-variant"></i> 退出登录</a>
              </li>
            </ul>
          </li>
        </ul>
        
      </nav>
      
    </header>
    <!--End 头部信息-->
    
    <!--页面主要内容-->
    <main class="lyear-layout-content">
      
      <div class="container-fluid p-t-15">
   <?php
    // 查询orders表获取支付列表数据
    $sqlOrders = "SELECT * FROM orders";
    $resultOrders = $conn->query($sqlOrders);
   ?>

        
        <div class="row">
          <div class="col-lg-12">
            <div class="card">
              
              <div class="card-body">
                <div class="table-responsive">
                  <?php if ($resultOrders->num_rows > 0) {?>
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>商户ID</th>
                        <th>平台订单号</th>
                        <th>商户订单号</th>
                        <th>支付方式</th>
                        <th>商品名称</th>
                        <th>商品金额</th>
                        <th>支付状态</th>
                        <th>签名字符串</th>
                        <th>签名类型</th>
                        <th>创建时间</th>
                        <th>更新时间</th>
                    </tr>
                </thead>
                <tbody>
                    <?php while ($rowOrders = $resultOrders->fetch_assoc()) {?>
                        <tr>
                            <td><?php echo $rowOrders['id'];?></td>
                            <td><?php echo $rowOrders['pid'];?></td>
                            <td><?php echo $rowOrders['trade_no'];?></td>
                            <td><?php echo $rowOrders['out_trade_no'];?></td>
                            <td><?php echo $rowOrders['type'];?></td>
                            <td><?php echo $rowOrders['name'];?></td>
                            <td><?php echo $rowOrders['money'];?></td>
                            <td><?php echo $rowOrders['trade_status'];?></td>
                            <td><?php echo $rowOrders['sign'];?></td>
                            <td><?php echo $rowOrders['sign_type'];?></td>
                            <td><?php echo $rowOrders['create_time'];?></td>
                            <td><?php echo $rowOrders['update_time'];?></td>
                        </tr>
                    <?php }?>
                </tbody>
            </table>
        <?php } else {?>
            <p>暂无支付订单记录。</p>
        <?php }?>
                </div>
                
                
                
                
       
              </div>
            </div>
          </div>
          
          
          
        </div>
        
        
        
        
      </div>
      
    </main>
    <!--End 页面主要内容-->
  </div>
</div>

<script type="text/javascript" src="./assets/js/jquery.min.js"></script>
<script type="text/javascript" src="./assets/js/popper.min.js"></script>
<script type="text/javascript" src="./assets/js/bootstrap.min.js"></script>
<script type="text/javascript" src="./assets/js/perfect-scrollbar.min.js"></script>
<script type="text/javascript" src="./assets/js/jquery.cookie.min.js"></script>
<script type="text/javascript" src="./assets/js/main.min.js"></script>
<script type="text/javascript" src="./assets/js/Chart.min.js"></script>
<script type="text/javascript" src="../assets/mesg/dist/js/sh-noytf.js"></script>
<script type="text/javascript">
if (document.getElementById('deluser')) {
    // 如果元素存在，则给它绑定事件
    document.getElementById('deluser').addEventListener('click', function() {
        if (confirm("这将删除该账号的所有数据且不可撤销,是否继续?")) {
            // 用户点击了确认按钮
            document.getElementById("lx").value="deluser";
        } else {
            // 用户点击了取消按钮或关闭了弹窗
            event.preventDefault(); // 阻止表单的默认提交行为
            return;
        }
    });
}

//解封
function idsx(){
    // 获取所有具有特定类名的checkbox元素  
var checkboxes = document.querySelectorAll('.custom-control-input.ids');  
// 创建一个空数组来存储被勾选元素的值  
var selectedValues = [];  
// 遍历所有checkbox元素  
for (var i = 0; i < checkboxes.length; i++) {  
    // 检查每个checkbox元素是否被勾选  
    if (checkboxes[i].checked) {  
        // 如果被勾选，则将该元素的值添加到selectedValues数组中  
        selectedValues.push(checkboxes[i].value);  
    }  
}  
// 将被勾选元素的值拼接为字符串，每个值用逗号分隔  
var data = selectedValues.join(',');  
if (data == "") {
    return;
}

loadpop("正在执行,请稍后",'ok');
// 创建一个XMLHttpRequest对象  
var xhr = new XMLHttpRequest();  
// 设置属性  
xhr.open('post', './api/userinfo.php', true); // 注意这里设置为异步请求  
// 设置请求头为application/x-www-form-urlencoded，以便能够通过POST请求发送数据  
xhr.setRequestHeader("Content-type", "application/x-www-form-urlencoded");  
// 将数据通过send方法传递，注意这里将数据转换为键值对形式  
xhr.send('ids=' + data+'&lx=0');  
// 发送并处理返回值  
xhr.onreadystatechange = function () {  
    // 这步为判断服务器是否正确响应  
    if (xhr.readyState == 4 && xhr.status == 200) {
        if (xhr.responseText == "") {
            warnpop("未获取到数据");
            return;
        }
        var obj = JSON.parse(xhr.responseText); //由JSON字符串转换为JSON对象
        //console.log(obj);
        var code =obj[0].code;//获取第一个的值 状态码
        var msg=obj[0].msg;//获取返回内容昵称
        if (code == 200) {
            successpop(msg);
            location.reload();
        }else{
            warnpop(msg);
        }
    }  
};
}

//封禁
function idsxf(){
    // 获取所有具有特定类名的checkbox元素  
var checkboxes = document.querySelectorAll('.custom-control-input.ids');  
// 创建一个空数组来存储被勾选元素的值  
var selectedValues = [];  
// 遍历所有checkbox元素  
for (var i = 0; i < checkboxes.length; i++) {  
    // 检查每个checkbox元素是否被勾选  
    if (checkboxes[i].checked) {  
        // 如果被勾选，则将该元素的值添加到selectedValues数组中  
        selectedValues.push(checkboxes[i].value);  
    }  
}  
// 将被勾选元素的值拼接为字符串，每个值用逗号分隔  
var data = selectedValues.join(',');  
if (data == "") {
    return;
}

loadpop("正在执行,请稍后",'ok');
// 创建一个XMLHttpRequest对象  
var xhr = new XMLHttpRequest();  
// 设置属性  
xhr.open('post', './api/userinfo.php', true); // 注意这里设置为异步请求  
// 设置请求头为application/x-www-form-urlencoded，以便能够通过POST请求发送数据  
xhr.setRequestHeader("Content-type", "application/x-www-form-urlencoded");  
// 将数据通过send方法传递，注意这里将数据转换为键值对形式  
xhr.send('ids=' + data+'&lx=-1');  
// 发送并处理返回值  
xhr.onreadystatechange = function () {  
    // 这步为判断服务器是否正确响应  
    if (xhr.readyState == 4 && xhr.status == 200) {
        if (xhr.responseText == "") {
            warnpop("未获取到数据");
            return;
        }
        var obj = JSON.parse(xhr.responseText); //由JSON字符串转换为JSON对象
        //console.log(obj);
        var code =obj[0].code;//获取第一个的值 状态码
        var msg=obj[0].msg;//获取返回内容昵称
        if (code == 200) {
            successpop(msg);
            location.reload();
        }else{
            warnpop(msg);
        }
    }  
};
}
</script>
</body>
</html>