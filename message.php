<?php

//decode by nige112
if ($_SERVER["REQUEST_METHOD"] === "POST") {
	$arr = [["code" => "201", "msg" => "非法请求"]];
	exit(json_encode($arr, JSON_UNESCAPED_UNICODE));
}
$iteace = "0";
if (is_file("./config.php")) {
	require "./config.php";
} else {
	exit("请先安装程序！<a href=\"./install/\">点击安装</a>");
}
require "./api/wz.php";

// 如果用户未登录，跳转到首页
if ($userdlzt == 0) {
    echo "<script>location.href='./index.php';</script>";
    exit;
}

// 创建消息表（如果不存在）
$sql_create_table = "CREATE TABLE IF NOT EXISTS messages (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    sender_id VARCHAR(255),
    receiver_id VARCHAR(255),
    content TEXT,
    is_read TINYINT(1) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";
$conn->query($sql_create_table);

// 创建聊天会话表（如果不存在）
$sql_create_sessions = "CREATE TABLE IF NOT EXISTS chat_sessions (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    user1_id VARCHAR(255),
    user2_id VARCHAR(255),
    last_message TEXT,
    last_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    unread_count INT(11) DEFAULT 0,
    UNIQUE KEY `unique_users` (`user1_id`, `user2_id`)
)";
$conn->query($sql_create_sessions);

// 获取当前用户的所有聊天会话
$sql_sessions = "SELECT s.*,
                u.name as other_name, 
                u.img as other_avatar,
                u.username as other_username
                FROM chat_sessions s
                LEFT JOIN user u ON (
                    (s.user1_id = '{$user_zh}' AND s.user2_id = u.username) OR
                    (s.user2_id = '{$user_zh}' AND s.user1_id = u.username)
                )
                WHERE s.user1_id = '{$user_zh}' OR s.user2_id = '{$user_zh}'
                ORDER BY s.last_time DESC";
                
$result_sessions = $conn->query($sql_sessions);

// 记录聊天会话数据
$chat_sessions = [];
if ($result_sessions && $result_sessions->num_rows > 0) {
    while ($row = $result_sessions->fetch_assoc()) {
        $chat_sessions[] = $row;
    }
}

// 获取未读消息总数
$sql_unread = "SELECT SUM(unread_count) as total_unread 
              FROM chat_sessions 
              WHERE (user1_id = '{$user_zh}' OR user2_id = '{$user_zh}')";
$result_unread = $conn->query($sql_unread);
$total_unread = 0;
if ($result_unread && $row_unread = $result_unread->fetch_assoc()) {
    $total_unread = $row_unread['total_unread'] ?: 0;
}

// 如果没有会话数据，获取一些推荐联系人
$recommended_users = [];
if (empty($chat_sessions)) {
    $sql_recommend = "SELECT username, name, img FROM user 
                     WHERE username != '{$user_zh}' 
                     ORDER BY RAND() LIMIT 5";
    $result_recommend = $conn->query($sql_recommend);
    
    if ($result_recommend && $result_recommend->num_rows > 0) {
        while ($row = $result_recommend->fetch_assoc()) {
            $recommended_users[] = $row;
        }
    }
}

?><!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>消息 - <?php echo $name;?></title>
    <!--为搜索引擎定义关键词-->
    <meta name="keywords" content="0717谦友圈、谦友、薛之谦粉丝、薛之谦、兴趣爱好分享、生活分享、在线聊天、谦友群聊、粉丝社交平台">
    <!--为网页定义描述内容 用于告诉搜索引擎，你网站的主要内容-->
    <meta name="description" content="<?php echo $name . "," . $subtitle;?>">
    <meta name="author" content="<?php echo $name;?>">
    <meta name="copyright" content="<?php echo $name;?>">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta http-equiv="cache-control" content="no-cache">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0,minimum-scale=1.0, user-scalable=no minimal-ui">
    <link rel="apple-touch-icon" sizes="57x57" href="<?php echo $icon;?>" /><!-- Standard iPhone -->
    <link rel="apple-touch-icon" sizes="114x114" href="<?php echo $icon;?>" /><!-- Retina iPhone -->
    <link rel="apple-touch-icon" sizes="72x72" href="<?php echo $icon;?>" /><!-- Standard iPad -->
    <link rel="apple-touch-icon" sizes="144x144" href="<?php echo $icon;?>" /><!-- Retina iPad -->
    <link rel="icon" href="<?php echo $icon;?>" type="image/x-icon" />
    <meta name="robots" content="index,follow">
    <meta name="format-detection" content="telephone=no">
    <meta http-equiv="x-rim-auto-match" content="none">
    
    <?php 
if ($rosdomain == 1) {
	?><meta name="referrer" content="no-referrer"><?php 
}
?>
    <link rel="stylesheet" href="<?php echo $iconurl_url;?>">
    <link rel="stylesheet" type="text/css" href="./assets/css/style.css?v=<?php echo $resversion;?>">
    <link rel="stylesheet" type="text/css" href="./assets/css/footer-menu.css?v=<?php echo $resversion;?>">
    <link rel="stylesheet" type="text/css" href="./assets/mesg/dist/css/style.css?v=<?php echo $resversion;?>"><!--引入弹窗对话框-->
    <link rel="stylesheet" type="text/css" href="./assets/css/jquery.fancybox.min.css?v=<?php echo $resversion;?>">

    <style>
        /* iOS风格UI */
        .message-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            position: sticky;
            top: 0;
            background-color: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            z-index: 100;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }
        
        .dark-theme .message-header {
            background-color: rgba(40, 40, 40, 0.95);
            border-bottom: 1px solid rgba(255,255,255,0.05);
        }
        
        .message-title {
            font-size: 24px;
            font-weight: bold;
            background: linear-gradient(135deg, #1e88e5, #42a5f5);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .message-actions {
            display: flex;
            align-items: center;
        }
        
        .message-action-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #f5f5f5;
            margin-left: 10px;
            transition: all 0.2s ease;
        }
        
        .dark-theme .message-action-btn {
            background-color: #333;
        }
        
        .message-action-btn:active {
            transform: scale(0.9);
            background-color: #e0e0e0;
        }
        
        .dark-theme .message-action-btn:active {
            background-color: #444;
        }
        
        .message-action-btn i {
            font-size: 20px;
            color: #1e88e5;
        }
        
        .message-search {
            margin: 10px 15px;
            position: relative;
        }
        
        .message-search-input {
            width: 100%;
            height: 40px;
            background-color: #f5f5f5;
            border-radius: 10px;
            border: none;
            padding: 0 15px 0 40px;
            font-size: 14px;
            transition: all 0.2s ease;
        }
        
        .dark-theme .message-search-input {
            background-color: #333;
            color: #eee;
        }
        
        .message-search-input:focus {
            outline: none;
            box-shadow: 0 0 0 2px rgba(30, 136, 229, 0.2);
        }
        
        .message-search-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
            font-size: 18px;
        }
        
        .message-list {
            margin: 20px 15px;
        }
        
        .message-item {
            display: flex;
            align-items: center;
            padding: 15px;
            margin-bottom: 15px;
            background-color: #fff;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            transition: transform 0.3s ease;
            position: relative;
            animation: slideIn 0.3s ease-out forwards;
            opacity: 0;
            transform: translateY(10px);
        }
        
        @keyframes slideIn {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .message-item:nth-child(1) { animation-delay: 0.05s; }
        .message-item:nth-child(2) { animation-delay: 0.1s; }
        .message-item:nth-child(3) { animation-delay: 0.15s; }
        .message-item:nth-child(4) { animation-delay: 0.2s; }
        .message-item:nth-child(5) { animation-delay: 0.25s; }
        
        .dark-theme .message-item {
            background-color: #272727;
            box-shadow: 0 5px 15px rgba(0,0,0,0.15);
        }
        
        .message-item:active {
            transform: scale(0.98);
        }
        
        .message-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 15px;
            border: 2px solid rgba(255,255,255,0.8);
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        
        .dark-theme .message-avatar {
            border: 2px solid rgba(40,40,40,0.8);
        }
        
        .message-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .message-content {
            flex: 1;
        }
        
        .message-name {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
            display: flex;
            align-items: center;
        }
        
        .dark-theme .message-name {
            color: #eee;
        }
        
        .message-text {
            font-size: 14px;
            color: #666;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 200px;
        }
        
        .dark-theme .message-text {
            color: #aaa;
        }
        
        .message-meta {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            margin-left: 10px;
        }
        
        .message-time {
            font-size: 12px;
            color: #999;
            margin-bottom: 5px;
        }
        
        .message-badge {
            background-color: #ff4d4f;
            color: white;
            font-size: 12px;
            width: 20px;
            height: 20px;
            border-radius: 10px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .message-empty {
            text-align: center;
            padding: 30px 0;
            color: #999;
        }
        
        .dark-theme .message-empty {
            color: #aaa;
        }
        
        .message-empty-icon {
            font-size: 50px;
            color: #ddd;
            margin-bottom: 15px;
        }
        
        .dark-theme .message-empty-icon {
            color: #444;
        }
        
        .message-empty-text {
            font-size: 16px;
            margin-bottom: 20px;
        }
        
        .message-empty-btn {
            display: inline-block;
            background-color: #1e88e5;
            color: white;
            font-size: 14px;
            padding: 8px 20px;
            border-radius: 20px;
            text-decoration: none;
            box-shadow: 0 3px 10px rgba(30, 136, 229, 0.3);
            transition: all 0.2s ease;
        }
        
        .message-empty-btn:active {
            transform: scale(0.95);
            box-shadow: 0 2px 5px rgba(30, 136, 229, 0.2);
        }
        
        /* 推荐联系人 */
        .recommended-section {
            margin: 20px 15px;
            padding: 15px;
            background-color: #fff;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }
        
        .dark-theme .recommended-section {
            background-color: #272727;
            box-shadow: 0 5px 15px rgba(0,0,0,0.15);
        }
        
        .recommended-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
        }
        
        .dark-theme .recommended-title {
            color: #eee;
        }
        
        .recommended-list {
            display: flex;
            overflow-x: auto;
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE 10+ */
            padding-bottom: 10px;
        }
        
        .recommended-list::-webkit-scrollbar {
            display: none; /* Chrome/Safari/Opera */
        }
        
        .recommended-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-right: 20px;
            min-width: 70px;
        }
        
        .recommended-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            overflow: hidden;
            margin-bottom: 8px;
            border: 2px solid rgba(255,255,255,0.8);
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        
        .dark-theme .recommended-avatar {
            border: 2px solid rgba(40,40,40,0.8);
        }
        
        .recommended-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .recommended-name {
            font-size: 12px;
            color: #333;
            text-align: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 70px;
        }
        
        .dark-theme .recommended-name {
            color: #eee;
        }
    </style>
    
    <?php echo $scfontzt;?>
    
    <?php 
$folderPath = "./user/bobg/";
if (!file_exists($folderPath)) {
	mkdir($folderPath, 511, true);
}
if (file_exists("./user/bobg/bobg.jpg")) {
	?><!--背景图--><style>body{background-image: url(./user/bobg/bobg.jpg);}</style><?php 
} elseif (file_exists("./user/bobg/bobg.png")) {
	?><!--背景图--><style>body{background-image: url(./user/bobg/bobg.png);}</style><?php 
} elseif (file_exists("./user/bobg/bobg.jpeg")) {
	?><!--背景图--><style>body{background-image: url(./user/bobg/bobg.jpeg);}</style><?php 
} elseif (file_exists("./user/bobg/bobg.gif")) {
	?><!--背景图--><style>body{background-image: url(./user/bobg/bobg.gif);}</style><?php 
}
?>
    <?php echo "<style>" . $filtercucss . "</style>";?>
</head>
<body class="<?php 
if (isset($_COOKIE["dark_theme"])) {
	if ($_COOKIE["dark_theme"] == "dark-theme") {
		echo "dark-theme";
	}
}
echo "\">
    ";
if ($pagepass != "") {
	if (isset($_COOKIE["pagepass"])) {
		if ($_COOKIE["pagepass"] != md5(md5($pagepass))) {
			include "./site/pagepass.php";
		}
	} else {
		include "./site/pagepass.php";
	}
}
?>
    <div class="centent">
        <div class="sh-main">
            <!-- 消息页面头部 -->
            <div class="message-header">
                <div class="message-title">消息</div>
                <div class="message-actions">
                    <div class="message-action-btn">
                        <i class="iconfont icon-tianjia"></i>
                    </div>
                </div>
            </div>
            
            <!-- 搜索框 -->
            <div class="message-search">
                <i class="iconfont icon-sousuo message-search-icon"></i>
                <input type="text" class="message-search-input" placeholder="搜索联系人">
            </div>
            
            <!-- 消息列表 -->
            <?php if (!empty($chat_sessions)): ?>
            <div class="message-list">
                <?php foreach ($chat_sessions as $index => $session): 
                    $other_user = $session['other_username'];
                    $other_name = $session['other_name'];
                    $other_avatar = $session['other_avatar'];
                    $last_message = $session['last_message'];
                    $last_time = strtotime($session['last_time']);
                    $unread_count = $session['unread_count'];
                    
                    // 格式化时间
                    $now = time();
                    $today = strtotime('today');
                    $yesterday = strtotime('yesterday');
                    
                    if ($last_time >= $today) {
                        // 今天
                        $time_display = date('H:i', $last_time);
                    } elseif ($last_time >= $yesterday) {
                        // 昨天
                        $time_display = '昨天';
                    } else {
                        // 更早
                        $time_display = date('m-d', $last_time);
                    }
                ?>
                <div class="message-item" onclick="location.href='./chat.php?user=<?php echo md5(md5($other_user));?>'">
                    <div class="message-avatar">
                        <img src="<?php echo $other_avatar;?>" alt="<?php echo $other_name;?>">
                    </div>
                    <div class="message-content">
                        <div class="message-name"><?php echo $other_name;?></div>
                        <div class="message-text"><?php echo htmlspecialchars($last_message);?></div>
                    </div>
                    <div class="message-meta">
                        <div class="message-time"><?php echo $time_display;?></div>
                        <?php if ($unread_count > 0): ?>
                        <div class="message-badge"><?php echo $unread_count > 99 ? '99+' : $unread_count;?></div>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <?php else: ?>
            
            <!-- 推荐联系人 -->
            <?php if (!empty($recommended_users)): ?>
            <div class="recommended-section">
                <div class="recommended-title">推荐联系人</div>
                <div class="recommended-list">
                    <?php foreach ($recommended_users as $user): ?>
                    <div class="recommended-item" onclick="location.href='./chat.php?user=<?php echo md5(md5($user['username']));?>'">
                        <div class="recommended-avatar">
                            <img src="<?php echo $user['img'];?>" alt="<?php echo $user['name'];?>">
                        </div>
                        <div class="recommended-name"><?php echo $user['name'];?></div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php endif; ?>
            
            <!-- 没有消息时显示的内容 -->
            <div class="message-empty">
                <div class="message-empty-icon">
                    <i class="iconfont icon-xiaoxi"></i>
                </div>
                <div class="message-empty-text">暂无聊天记录</div>
                <a href="./index.php" class="message-empty-btn">发现朋友</a>
            </div>
            <?php endif; ?>
            
            <!-- 底部填充，避免内容被底部菜单遮挡 -->
            <div class="footer-padding"></div>
        </div>
    </div>
    
    <!-- 底部菜单 -->
    <div class="footer-menu">
        <a href="./index.php" class="footer-menu-item">
            <i class="iconfont icon-shouye"></i>
            <span>首页</span>
        </a>
        <a href="./discover.php" class="footer-menu-item">
            <i class="iconfont icon-faxian"></i>
            <span>发现</span>
        </a>
        <a href="./message.php" class="footer-menu-item active">
            <i class="iconfont icon-xiaoxi"></i>
            <span>消息</span>
            <?php if ($total_unread > 0): ?>
            <div class="notification-dot"></div>
            <?php endif; ?>
        </a>
        <a href="./notify.php" class="footer-menu-item">
            <i class="iconfont icon-tongzhi"></i>
            <span>通知</span>
            <?php
            // 检查是否有未读通知
            if ($userdlzt == 1) {
                $sql_unread = "SELECT COUNT(*) as count FROM notifications WHERE user_to = '{$user_zh}' AND is_read = 0";
                $result_unread = $conn->query($sql_unread);
                $unread_count = 0;
                
                if ($result_unread && $row_unread = $result_unread->fetch_assoc()) {
                    $unread_count = $row_unread['count'];
                }
                
                if ($unread_count > 0) {
                    echo '<div class="notification-dot"></div>';
                }
            }
            ?>
        </a>
        <a href="./home.php" class="footer-menu-item">
            <i class="iconfont icon-wode"></i>
            <span>我的</span>
        </a>
    </div>

    <script type="text/javascript" src="./assets/js/jquery.min.js"></script>
    <script type="text/javascript" src="./assets/mesg/dist/js/sh-noytf.js?v=<?php echo $resversion;?>"></script>
    <script type="text/javascript" src="./assets/js/jquery.fancybox.min.js?v=<?php echo $resversion;?>"></script>
    <?php echo "<script type=\"text/javascript\">" . $filtercujs . "</script>";?> 
</body>
</html> 