// 0717谦友圈小程序入口文件
App({
  globalData: {
    userInfo: null,
    isLogin: false,
    baseUrl: 'https://your-domain.com/api/', // 替换为实际API地址
    version: '1.0.0',
    systemInfo: null
  },

  onLaunch: function () {
    console.log('0717谦友圈小程序启动')
    
    // 获取系统信息
    this.getSystemInfo()
    
    // 检查登录状态
    this.checkLoginStatus()
    
    // 检查更新
    this.checkForUpdate()
  },

  onShow: function (options) {
    console.log('小程序显示')
  },

  onHide: function () {
    console.log('小程序隐藏')
  },

  onError: function (msg) {
    console.error('小程序错误:', msg)
  },

  // 获取系统信息
  getSystemInfo() {
    wx.getSystemInfo({
      success: (res) => {
        this.globalData.systemInfo = res
        console.log('系统信息:', res)
      }
    })
  },

  // 检查登录状态
  checkLoginStatus() {
    const username = wx.getStorageSync('username')
    const passid = wx.getStorageSync('passid')
    
    if (username && passid) {
      this.globalData.isLogin = true
      this.globalData.userInfo = {
        username: username,
        passid: passid
      }
    }
  },

  // 检查小程序更新
  checkForUpdate() {
    if (wx.canIUse('getUpdateManager')) {
      const updateManager = wx.getUpdateManager()
      
      updateManager.onCheckForUpdate((res) => {
        if (res.hasUpdate) {
          console.log('发现新版本')
        }
      })

      updateManager.onUpdateReady(() => {
        wx.showModal({
          title: '更新提示',
          content: '新版本已经准备好，是否重启应用？',
          success: (res) => {
            if (res.confirm) {
              updateManager.applyUpdate()
            }
          }
        })
      })

      updateManager.onUpdateFailed(() => {
        console.log('新版本下载失败')
      })
    }
  },

  // 全局方法：显示加载提示
  showLoading(title = '加载中...') {
    wx.showLoading({
      title: title,
      mask: true
    })
  },

  // 全局方法：隐藏加载提示
  hideLoading() {
    wx.hideLoading()
  },

  // 全局方法：显示提示信息
  showToast(title, icon = 'none', duration = 2000) {
    wx.showToast({
      title: title,
      icon: icon,
      duration: duration
    })
  },

  // 全局方法：网络请求
  request(options) {
    const app = this
    const defaultOptions = {
      url: '',
      method: 'GET',
      data: {},
      header: {
        'content-type': 'application/json'
      },
      success: () => {},
      fail: () => {},
      complete: () => {}
    }

    options = Object.assign(defaultOptions, options)
    
    // 添加基础URL
    if (!options.url.startsWith('http')) {
      options.url = this.globalData.baseUrl + options.url
    }

    // 添加用户认证信息
    if (this.globalData.isLogin) {
      options.data.username = this.globalData.userInfo.username
      options.data.passid = this.globalData.userInfo.passid
    }

    wx.request({
      ...options,
      success: (res) => {
        if (res.statusCode === 200) {
          options.success(res.data)
        } else {
          app.showToast('网络请求失败')
          options.fail(res)
        }
      },
      fail: (err) => {
        app.showToast('网络连接失败')
        options.fail(err)
      },
      complete: options.complete
    })
  },

  // 全局方法：登出
  logout() {
    wx.removeStorageSync('username')
    wx.removeStorageSync('passid')
    this.globalData.isLogin = false
    this.globalData.userInfo = null
    
    wx.reLaunch({
      url: '/pages/login/login'
    })
  }
})
